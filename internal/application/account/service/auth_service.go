package service

import (
	"context"
	"fmt"
	"time"

	"github.com/wongpinter/payment-gateway/internal/domain/account"
)

// JWTManager interface for JWT token operations
type JWTManager interface {
	GenerateAccessToken(ctx context.Context, user *account.User, roles []account.Role) (string, time.Time, error)
	GenerateRefreshToken(ctx context.Context, user *account.User) (string, time.Time, error)
	ValidateAccessToken(ctx context.Context, tokenString string) (*AccessTokenClaims, error)
	ValidateRefreshToken(ctx context.Context, tokenString string) (*RefreshTokenClaims, error)
	BlacklistToken(ctx context.Context, tokenString string) error
	BlacklistUserTokens(ctx context.Context, userID string, expiresAt time.Time) error
	ExtractTokenID(tokenString string) (string, error)
	GetTokenTTLs() (accessTTL, refreshTTL time.Duration)
}

// AccessTokenClaims represents JWT access token claims
type AccessTokenClaims struct {
	UserID string   `json:"user_id"`
	Email  string   `json:"email"`
	Name   string   `json:"name"`
	Roles  []string `json:"roles"`
}

// RefreshTokenClaims represents JWT refresh token claims
type RefreshTokenClaims struct {
	UserID string `json:"user_id"`
}

// AuthService provides authentication and authorization services
type AuthService struct {
	userRepo       account.UserRepository
	roleRepo       account.RoleRepository
	permissionRepo account.PermissionRepository
	sessionRepo    account.SessionRepository
	passwordHasher account.PasswordHasher
	eventPublisher account.EventPublisher
	jwtManager     JWTManager
}

// NewAuthService creates a new AuthService
func NewAuthService(
	userRepo account.UserRepository,
	roleRepo account.RoleRepository,
	permissionRepo account.PermissionRepository,
	sessionRepo account.SessionRepository,
	passwordHasher account.PasswordHasher,
	eventPublisher account.EventPublisher,
	jwtManager JWTManager,
) *AuthService {
	return &AuthService{
		userRepo:       userRepo,
		roleRepo:       roleRepo,
		permissionRepo: permissionRepo,
		sessionRepo:    sessionRepo,
		passwordHasher: passwordHasher,
		eventPublisher: eventPublisher,
		jwtManager:     jwtManager,
	}
}

// LoginRequest represents a login request
type LoginRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required,min=8"`
}

// LoginResponse represents a login response
type LoginResponse struct {
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresAt    time.Time `json:"expires_at"`
	User         UserInfo  `json:"user"`
}

// UserInfo represents user information in responses
type UserInfo struct {
	ID       string   `json:"id"`
	Email    string   `json:"email"`
	Name     string   `json:"name"`
	IsActive bool     `json:"is_active"`
	Roles    []string `json:"roles"`
}

// RefreshTokenRequest represents a refresh token request
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" validate:"required"`
}

// Login authenticates a user and returns JWT tokens
func (s *AuthService) Login(ctx context.Context, req LoginRequest) (*LoginResponse, error) {
	// Validate email format
	emailVO, err := account.NewEmail(req.Email)
	if err != nil {
		return nil, fmt.Errorf("auth_service.Login: %w", err)
	}

	// Get user by email
	user, err := s.userRepo.GetByEmail(ctx, emailVO)
	if err != nil {
		if err == account.ErrUserNotFound {
			return nil, account.ErrInvalidCredentials
		}
		return nil, fmt.Errorf("auth_service.Login: %w", err)
	}

	// Check if user is active
	if !user.IsActive() {
		return nil, account.ErrUserInactive
	}

	// Verify password
	if err := s.passwordHasher.Verify(req.Password, user.PasswordHash().String()); err != nil {
		return nil, account.ErrInvalidCredentials
	}

	// Get user roles
	roles, err := s.userRepo.GetUserRoles(ctx, user.ID())
	if err != nil {
		return nil, fmt.Errorf("auth_service.Login: %w", err)
	}

	// Generate tokens
	accessToken, expiresAt, err := s.jwtManager.GenerateAccessToken(ctx, user, roles)
	if err != nil {
		return nil, fmt.Errorf("auth_service.Login: %w", err)
	}

	refreshToken, _, err := s.jwtManager.GenerateRefreshToken(ctx, user)
	if err != nil {
		return nil, fmt.Errorf("auth_service.Login: %w", err)
	}

	// Create session
	_, refreshTokenTTL := s.jwtManager.GetTokenTTLs()
	session := account.NewSession(user.ID(), refreshToken, refreshTokenTTL)
	if err := s.sessionRepo.Create(ctx, session); err != nil {
		return nil, fmt.Errorf("auth_service.Login: %w", err)
	}

	// Update last login time
	// Note: This would require adding a method to the user entity and repository
	// For now, we'll skip this but it should be implemented

	// Publish login event
	// TODO: Extract IP address and user agent from context
	event := account.NewUserLoggedInEvent(user.ID(), user.Email(), "", "")
	if err := s.eventPublisher.Publish(ctx, event); err != nil {
		// Log error but don't fail the operation
	}

	// Prepare role names for response
	roleNames := make([]string, len(roles))
	for i, role := range roles {
		roleNames[i] = role.Name()
	}

	return &LoginResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    expiresAt,
		User: UserInfo{
			ID:       user.ID().String(),
			Email:    user.Email().String(),
			Name:     user.Name(),
			IsActive: user.IsActive(),
			Roles:    roleNames,
		},
	}, nil
}

// RefreshToken refreshes an access token using a refresh token
func (s *AuthService) RefreshToken(ctx context.Context, req RefreshTokenRequest) (*LoginResponse, error) {
	// Get session by refresh token
	session, err := s.sessionRepo.GetByRefreshToken(ctx, req.RefreshToken)
	if err != nil {
		if err == account.ErrSessionNotFound {
			return nil, account.ErrInvalidRefreshToken
		}
		return nil, fmt.Errorf("auth_service.RefreshToken: %w", err)
	}

	// Check if session is expired
	if session.IsExpired() {
		// Clean up expired session
		_ = s.sessionRepo.Delete(ctx, session.ID())
		return nil, account.ErrSessionExpired
	}

	// Get user
	user, err := s.userRepo.GetByID(ctx, session.UserID())
	if err != nil {
		return nil, fmt.Errorf("auth_service.RefreshToken: %w", err)
	}

	// Check if user is still active
	if !user.IsActive() {
		// Invalidate session
		_ = s.sessionRepo.Delete(ctx, session.ID())
		return nil, account.ErrUserInactive
	}

	// Get user roles
	roles, err := s.userRepo.GetUserRoles(ctx, user.ID())
	if err != nil {
		return nil, fmt.Errorf("auth_service.RefreshToken: %w", err)
	}

	// Generate new tokens
	accessToken, expiresAt, err := s.jwtManager.GenerateAccessToken(ctx, user, roles)
	if err != nil {
		return nil, fmt.Errorf("auth_service.RefreshToken: %w", err)
	}

	newRefreshToken, _, err := s.jwtManager.GenerateRefreshToken(ctx, user)
	if err != nil {
		return nil, fmt.Errorf("auth_service.RefreshToken: %w", err)
	}

	// Update session with new refresh token
	_, refreshTokenTTL := s.jwtManager.GetTokenTTLs()
	session.UpdateRefreshToken(newRefreshToken, refreshTokenTTL)
	if err := s.sessionRepo.Update(ctx, session); err != nil {
		return nil, fmt.Errorf("auth_service.RefreshToken: %w", err)
	}

	// Prepare role names for response
	roleNames := make([]string, len(roles))
	for i, role := range roles {
		roleNames[i] = role.Name()
	}

	return &LoginResponse{
		AccessToken:  accessToken,
		RefreshToken: newRefreshToken,
		ExpiresAt:    expiresAt,
		User: UserInfo{
			ID:       user.ID().String(),
			Email:    user.Email().String(),
			Name:     user.Name(),
			IsActive: user.IsActive(),
			Roles:    roleNames,
		},
	}, nil
}

// Logout invalidates a user's session
func (s *AuthService) Logout(ctx context.Context, refreshToken string) error {
	// Get session by refresh token
	session, err := s.sessionRepo.GetByRefreshToken(ctx, refreshToken)
	if err != nil {
		if err == account.ErrSessionNotFound {
			// Session doesn't exist, consider it already logged out
			return nil
		}
		return fmt.Errorf("auth_service.Logout: %w", err)
	}

	// Get user for event publishing
	user, err := s.userRepo.GetByID(ctx, session.UserID())
	if err != nil {
		// Log error but continue with logout
	}

	// Delete session
	if err := s.sessionRepo.Delete(ctx, session.ID()); err != nil {
		return fmt.Errorf("auth_service.Logout: %w", err)
	}

	// Publish logout event if we have user info
	if user != nil {
		event := account.NewUserLoggedOutEvent(session.UserID(), user.Email())
		if err := s.eventPublisher.Publish(ctx, event); err != nil {
			// Log error but don't fail the operation
		}
	}

	return nil
}

// LogoutWithAccessToken invalidates a user's session and blacklists the access token
func (s *AuthService) LogoutWithAccessToken(ctx context.Context, refreshToken, accessToken string) error {
	// Get session by refresh token
	session, err := s.sessionRepo.GetByRefreshToken(ctx, refreshToken)
	if err != nil {
		if err == account.ErrSessionNotFound {
			// Session doesn't exist, consider it already logged out
			// But still try to blacklist the access token if provided
			if accessToken != "" {
				if blacklistErr := s.jwtManager.BlacklistToken(ctx, accessToken); blacklistErr != nil {
					// Log error but don't fail logout
				}
			}
			return nil
		}
		return fmt.Errorf("auth_service.LogoutWithAccessToken: %w", err)
	}

	// Get user for event publishing
	user, err := s.userRepo.GetByID(ctx, session.UserID())
	if err != nil {
		// Log error but continue with logout
	}

	// Blacklist the access token if provided
	if accessToken != "" {
		if err := s.jwtManager.BlacklistToken(ctx, accessToken); err != nil {
			// Log error but don't fail logout - it's better to be permissive here
		}
	}

	// Delete session
	if err := s.sessionRepo.Delete(ctx, session.ID()); err != nil {
		return fmt.Errorf("auth_service.LogoutWithAccessToken: %w", err)
	}

	// Publish logout event if we have user info
	if user != nil {
		event := account.NewUserLoggedOutEvent(session.UserID(), user.Email())
		if err := s.eventPublisher.Publish(ctx, event); err != nil {
			// Log error but don't fail the operation
		}
	}

	return nil
}

// HasPermission checks if a user has a specific permission
func (s *AuthService) HasPermission(ctx context.Context, userID string, permissionName string) (bool, error) {
	// Parse user ID
	uid, err := account.UserIDFromString(userID)
	if err != nil {
		return false, fmt.Errorf("auth_service.HasPermission: invalid user ID: %w", err)
	}

	// Get user roles
	roles, err := s.userRepo.GetUserRoles(ctx, uid)
	if err != nil {
		return false, fmt.Errorf("auth_service.HasPermission: failed to get user roles: %w", err)
	}

	// Check each role for the permission
	for _, role := range roles {
		permissions, err := s.roleRepo.GetRolePermissions(ctx, role.ID())
		if err != nil {
			return false, fmt.Errorf("auth_service.HasPermission: failed to get role permissions: %w", err)
		}

		for _, permission := range permissions {
			if permission.Name() == permissionName {
				return true, nil
			}
		}
	}

	return false, nil
}

// ValidateToken validates an access token and returns user information
func (s *AuthService) ValidateToken(ctx context.Context, token string) (*UserInfo, error) {
	// Parse and validate JWT token
	claims, err := s.jwtManager.ValidateAccessToken(ctx, token)
	if err != nil {
		return nil, fmt.Errorf("auth_service.ValidateToken: %w", err)
	}

	// Parse user ID from claims
	userID, err := account.UserIDFromString(claims.UserID)
	if err != nil {
		return nil, fmt.Errorf("auth_service.ValidateToken: invalid user ID: %w", err)
	}

	// Get user to verify it still exists and is active
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("auth_service.ValidateToken: %w", err)
	}

	// Check if user is active
	if !user.IsActive() {
		return nil, account.ErrUserInactive
	}

	return &UserInfo{
		ID:       claims.UserID,
		Email:    claims.Email,
		Name:     claims.Name,
		IsActive: user.IsActive(),
		Roles:    claims.Roles,
	}, nil
}

// CheckPermission checks if a user has a specific permission
func (s *AuthService) CheckPermission(ctx context.Context, userID account.UserID, resource, action string) (bool, error) {
	// Get user roles
	roles, err := s.userRepo.GetUserRoles(ctx, userID)
	if err != nil {
		return false, fmt.Errorf("auth_service.CheckPermission: %w", err)
	}

	// Check each role for the required permission
	for _, role := range roles {
		permissions, err := s.roleRepo.GetRolePermissions(ctx, role.ID())
		if err != nil {
			return false, fmt.Errorf("auth_service.CheckPermission: %w", err)
		}

		for _, permission := range permissions {
			if permission.Resource() == resource && permission.Action() == action {
				return true, nil
			}
		}
	}

	return false, nil
}

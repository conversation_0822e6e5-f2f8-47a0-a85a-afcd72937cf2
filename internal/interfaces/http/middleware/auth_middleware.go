package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/wongpinter/payment-gateway/internal/application/account/service"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/webapi"
)

// AuthMiddleware handles authentication and authorization
type AuthMiddleware struct {
	authService *service.AuthService
}

// NewAuthMiddleware creates a new AuthMiddleware
func NewAuthMiddleware(authService *service.AuthService) *AuthMiddleware {
	return &AuthMiddleware{
		authService: authService,
	}
}

// RequireAuth middleware that requires valid authentication
func (m *AuthMiddleware) RequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract token from Authorization header
		token, err := m.extractTokenFromHeader(c)
		if err != nil {
			webapi.Error(c, http.StatusUnauthorized, "MISSING_TOKEN", "Authorization token required", nil)
			c.Abort()
			return
		}

		// Validate token and get user info
		userInfo, err := m.authService.ValidateToken(c.Request.Context(), token)
		if err != nil {
			// Handle specific errors
			switch {
			case strings.Contains(err.Error(), "blacklisted"):
				webapi.Error(c, http.StatusUnauthorized, "TOKEN_BLACKLISTED", "Token has been revoked", nil)
			case strings.Contains(err.Error(), "expired"):
				webapi.Error(c, http.StatusUnauthorized, "TOKEN_EXPIRED", "Token has expired", nil)
			case strings.Contains(err.Error(), "invalid"):
				webapi.Error(c, http.StatusUnauthorized, "INVALID_TOKEN", "Invalid token", nil)
			case strings.Contains(err.Error(), "inactive"):
				webapi.Error(c, http.StatusForbidden, "ACCOUNT_INACTIVE", "Account is inactive", nil)
			default:
				webapi.Error(c, http.StatusUnauthorized, "TOKEN_VALIDATION_FAILED", "Token validation failed", nil)
			}
			c.Abort()
			return
		}

		// Store user info in context for use by handlers
		c.Set("user", userInfo)
		c.Set("user_id", userInfo.ID)
		c.Set("user_email", userInfo.Email)
		c.Set("user_roles", userInfo.Roles)

		c.Next()
	}
}

// RequireRole middleware that requires specific role(s)
func (m *AuthMiddleware) RequireRole(requiredRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// First ensure user is authenticated
		userInfo, exists := c.Get("user")
		if !exists {
			webapi.Error(c, http.StatusUnauthorized, "NOT_AUTHENTICATED", "User not authenticated", nil)
			c.Abort()
			return
		}

		user, ok := userInfo.(*service.UserInfo)
		if !ok {
			webapi.Error(c, http.StatusInternalServerError, "INVALID_USER_CONTEXT", "Invalid user context", nil)
			c.Abort()
			return
		}

		// Check if user has any of the required roles
		if !m.hasAnyRole(user.Roles, requiredRoles) {
			webapi.Error(c, http.StatusForbidden, "INSUFFICIENT_PERMISSIONS", "Insufficient permissions", gin.H{
				"required_roles": requiredRoles,
				"user_roles":     user.Roles,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequirePermission middleware that requires specific permission(s)
func (m *AuthMiddleware) RequirePermission(requiredPermissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// First ensure user is authenticated
		userInfo, exists := c.Get("user")
		if !exists {
			webapi.Error(c, http.StatusUnauthorized, "NOT_AUTHENTICATED", "User not authenticated", nil)
			c.Abort()
			return
		}

		user, ok := userInfo.(*service.UserInfo)
		if !ok {
			webapi.Error(c, http.StatusInternalServerError, "INVALID_USER_CONTEXT", "Invalid user context", nil)
			c.Abort()
			return
		}

		// Check permissions for each required permission
		for _, permission := range requiredPermissions {
			hasPermission, err := m.authService.HasPermission(c.Request.Context(), user.ID, permission)
			if err != nil {
				webapi.ErrorWithSentry(c, http.StatusInternalServerError, "PERMISSION_CHECK_FAILED", "Permission check failed", err, nil)
				c.Abort()
				return
			}

			if !hasPermission {
				webapi.Error(c, http.StatusForbidden, "INSUFFICIENT_PERMISSIONS", "Insufficient permissions", gin.H{
					"required_permission": permission,
					"user_roles":          user.Roles,
				})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// OptionalAuth middleware that extracts user info if token is present but doesn't require it
func (m *AuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Try to extract token from Authorization header
		token, err := m.extractTokenFromHeader(c)
		if err != nil {
			// No token provided, continue without authentication
			c.Next()
			return
		}

		// Validate token and get user info
		userInfo, err := m.authService.ValidateToken(c.Request.Context(), token)
		if err != nil {
			// Invalid token, continue without authentication
			c.Next()
			return
		}

		// Store user info in context for use by handlers
		c.Set("user", userInfo)
		c.Set("user_id", userInfo.ID)
		c.Set("user_email", userInfo.Email)
		c.Set("user_roles", userInfo.Roles)

		c.Next()
	}
}

// extractTokenFromHeader extracts the JWT token from the Authorization header
func (m *AuthMiddleware) extractTokenFromHeader(c *gin.Context) (string, error) {
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		return "", gin.Error{Err: gin.Error{}, Type: gin.ErrorTypePublic, Meta: "Authorization header missing"}
	}

	// Check for Bearer token format
	parts := strings.SplitN(authHeader, " ", 2)
	if len(parts) != 2 || strings.ToLower(parts[0]) != "bearer" {
		return "", gin.Error{Err: gin.Error{}, Type: gin.ErrorTypePublic, Meta: "Invalid authorization header format"}
	}

	token := strings.TrimSpace(parts[1])
	if token == "" {
		return "", gin.Error{Err: gin.Error{}, Type: gin.ErrorTypePublic, Meta: "Empty token"}
	}

	return token, nil
}

// hasAnyRole checks if the user has any of the required roles
func (m *AuthMiddleware) hasAnyRole(userRoles, requiredRoles []string) bool {
	userRoleSet := make(map[string]bool)
	for _, role := range userRoles {
		userRoleSet[role] = true
	}

	for _, requiredRole := range requiredRoles {
		if userRoleSet[requiredRole] {
			return true
		}
	}

	return false
}

// AdminOnly is a convenience middleware for admin-only endpoints
func (m *AuthMiddleware) AdminOnly() gin.HandlerFunc {
	return m.RequireRole("admin")
}

// UserOrAdmin is a convenience middleware for user or admin access
func (m *AuthMiddleware) UserOrAdmin() gin.HandlerFunc {
	return m.RequireRole("user", "admin")
}

// SelfOrAdmin middleware that allows access if the user is accessing their own resource or is an admin
func (m *AuthMiddleware) SelfOrAdmin(userIDParam string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// First ensure user is authenticated
		userInfo, exists := c.Get("user")
		if !exists {
			webapi.Error(c, http.StatusUnauthorized, "NOT_AUTHENTICATED", "User not authenticated", nil)
			c.Abort()
			return
		}

		user, ok := userInfo.(*service.UserInfo)
		if !ok {
			webapi.Error(c, http.StatusInternalServerError, "INVALID_USER_CONTEXT", "Invalid user context", nil)
			c.Abort()
			return
		}

		// Get the user ID from URL parameter
		targetUserID := c.Param(userIDParam)
		if targetUserID == "" {
			webapi.Error(c, http.StatusBadRequest, "MISSING_USER_ID", "User ID parameter missing", nil)
			c.Abort()
			return
		}

		// Allow if user is accessing their own resource
		if user.ID == targetUserID {
			c.Next()
			return
		}

		// Allow if user is admin
		if m.hasAnyRole(user.Roles, []string{"admin"}) {
			c.Next()
			return
		}

		// Deny access
		webapi.Error(c, http.StatusForbidden, "ACCESS_DENIED", "Access denied", nil)
		c.Abort()
	}
}

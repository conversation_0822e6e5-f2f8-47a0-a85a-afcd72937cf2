// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: permissions.sql

package sqlc

import (
	"context"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
)

const countPermissions = `-- name: CountPermissions :one
SELECT COUNT(*) FROM permissions 
WHERE deleted_at IS NULL
`

func (q *Queries) CountPermissions(ctx context.Context) (int64, error) {
	row := q.db.QueryRow(ctx, countPermissions)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createPermission = `-- name: CreatePermission :one
INSERT INTO permissions (
    id, name, description, resource, action, is_active, created_at, updated_at
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8
) RETURNING id, name, description, resource, action, is_active, created_at, updated_at, deleted_at
`

type CreatePermissionParams struct {
	ID          uuid.UUID          `db:"id" json:"id"`
	Name        string             `db:"name" json:"name"`
	Description pgtype.Text        `db:"description" json:"description"`
	Resource    string             `db:"resource" json:"resource"`
	Action      string             `db:"action" json:"action"`
	IsActive    bool               `db:"is_active" json:"is_active"`
	CreatedAt   pgtype.Timestamptz `db:"created_at" json:"created_at"`
	UpdatedAt   pgtype.Timestamptz `db:"updated_at" json:"updated_at"`
}

func (q *Queries) CreatePermission(ctx context.Context, arg CreatePermissionParams) (Permission, error) {
	row := q.db.QueryRow(ctx, createPermission,
		arg.ID,
		arg.Name,
		arg.Description,
		arg.Resource,
		arg.Action,
		arg.IsActive,
		arg.CreatedAt,
		arg.UpdatedAt,
	)
	var i Permission
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.Resource,
		&i.Action,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const deletePermission = `-- name: DeletePermission :exec
UPDATE permissions 
SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND deleted_at IS NULL
`

func (q *Queries) DeletePermission(ctx context.Context, id uuid.UUID) error {
	_, err := q.db.Exec(ctx, deletePermission, id)
	return err
}

const existsPermissionByName = `-- name: ExistsPermissionByName :one
SELECT EXISTS(
    SELECT 1 FROM permissions 
    WHERE name = $1 AND deleted_at IS NULL
)
`

func (q *Queries) ExistsPermissionByName(ctx context.Context, name string) (bool, error) {
	row := q.db.QueryRow(ctx, existsPermissionByName, name)
	var exists bool
	err := row.Scan(&exists)
	return exists, err
}

const getPermissionByID = `-- name: GetPermissionByID :one
SELECT id, name, description, resource, action, is_active, created_at, updated_at, deleted_at FROM permissions 
WHERE id = $1 AND deleted_at IS NULL
`

func (q *Queries) GetPermissionByID(ctx context.Context, id uuid.UUID) (Permission, error) {
	row := q.db.QueryRow(ctx, getPermissionByID, id)
	var i Permission
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.Resource,
		&i.Action,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getPermissionByName = `-- name: GetPermissionByName :one
SELECT id, name, description, resource, action, is_active, created_at, updated_at, deleted_at FROM permissions 
WHERE name = $1 AND deleted_at IS NULL
`

func (q *Queries) GetPermissionByName(ctx context.Context, name string) (Permission, error) {
	row := q.db.QueryRow(ctx, getPermissionByName, name)
	var i Permission
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.Resource,
		&i.Action,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getPermissionByResourceAndAction = `-- name: GetPermissionByResourceAndAction :one
SELECT id, name, description, resource, action, is_active, created_at, updated_at, deleted_at FROM permissions 
WHERE resource = $1 AND action = $2 AND deleted_at IS NULL
`

type GetPermissionByResourceAndActionParams struct {
	Resource string `db:"resource" json:"resource"`
	Action   string `db:"action" json:"action"`
}

func (q *Queries) GetPermissionByResourceAndAction(ctx context.Context, arg GetPermissionByResourceAndActionParams) (Permission, error) {
	row := q.db.QueryRow(ctx, getPermissionByResourceAndAction, arg.Resource, arg.Action)
	var i Permission
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.Resource,
		&i.Action,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getPermissionsByResource = `-- name: GetPermissionsByResource :many
SELECT id, name, description, resource, action, is_active, created_at, updated_at, deleted_at FROM permissions 
WHERE resource = $1 AND deleted_at IS NULL
ORDER BY action
`

func (q *Queries) GetPermissionsByResource(ctx context.Context, resource string) ([]Permission, error) {
	rows, err := q.db.Query(ctx, getPermissionsByResource, resource)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Permission{}
	for rows.Next() {
		var i Permission
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.Resource,
			&i.Action,
			&i.IsActive,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPermissions = `-- name: ListPermissions :many
SELECT id, name, description, resource, action, is_active, created_at, updated_at, deleted_at FROM permissions 
WHERE deleted_at IS NULL
ORDER BY resource, action
LIMIT $1 OFFSET $2
`

type ListPermissionsParams struct {
	Limit  int32 `db:"limit" json:"limit"`
	Offset int32 `db:"offset" json:"offset"`
}

func (q *Queries) ListPermissions(ctx context.Context, arg ListPermissionsParams) ([]Permission, error) {
	rows, err := q.db.Query(ctx, listPermissions, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Permission{}
	for rows.Next() {
		var i Permission
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.Resource,
			&i.Action,
			&i.IsActive,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updatePermission = `-- name: UpdatePermission :one
UPDATE permissions 
SET 
    name = $2,
    description = $3,
    resource = $4,
    action = $5,
    is_active = $6,
    updated_at = $7
WHERE id = $1 AND deleted_at IS NULL
RETURNING id, name, description, resource, action, is_active, created_at, updated_at, deleted_at
`

type UpdatePermissionParams struct {
	ID          uuid.UUID          `db:"id" json:"id"`
	Name        string             `db:"name" json:"name"`
	Description pgtype.Text        `db:"description" json:"description"`
	Resource    string             `db:"resource" json:"resource"`
	Action      string             `db:"action" json:"action"`
	IsActive    bool               `db:"is_active" json:"is_active"`
	UpdatedAt   pgtype.Timestamptz `db:"updated_at" json:"updated_at"`
}

func (q *Queries) UpdatePermission(ctx context.Context, arg UpdatePermissionParams) (Permission, error) {
	row := q.db.QueryRow(ctx, updatePermission,
		arg.ID,
		arg.Name,
		arg.Description,
		arg.Resource,
		arg.Action,
		arg.IsActive,
		arg.UpdatedAt,
	)
	var i Permission
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.Resource,
		&i.Action,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

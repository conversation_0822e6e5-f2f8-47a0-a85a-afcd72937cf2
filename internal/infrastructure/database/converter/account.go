package converter

import (
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"

	"github.com/wongpinter/payment-gateway/internal/domain/account"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/database/sqlc"
)

// UserToModel converts a domain User to a database model
func UserToModel(user *account.User) sqlc.User {
	var lastLoginAt pgtype.Int8
	// Note: In the domain model, we don't have LastLoginAt, so we'll set it to null
	// This will be handled separately in the repository

	return sqlc.User{
		ID:           user.ID().UUID(),
		Name:         user.Name(),
		Email:        user.Email().String(),
		PasswordHash: user.PasswordHash().String(),
		LastLoginAt:  lastLoginAt, // Will be null
		IsActive:     user.IsActive(),
		CreatedAt:    TimeToPgTimestamptz(user.CreatedAt()),
		UpdatedAt:    TimeToPgTimestamptz(user.UpdatedAt()),
		DeletedAt:    pgtype.Timestamptz{}, // Will be null for active users
	}
}

// ModelToUser converts a database model to a domain User
func ModelToUser(model sqlc.User) (*account.User, error) {
	userID, err := account.UserIDFromString(model.ID.String())
	if err != nil {
		return nil, err
	}

	email, err := account.NewEmail(model.Email)
	if err != nil {
		return nil, err
	}

	passwordHash := account.NewPasswordHash(model.PasswordHash)

	// Create user with all fields using the constructor
	return account.NewUserWithID(
		userID,
		email,
		passwordHash,
		model.Name,
		model.IsActive,
		PgTimestamptzToTime(model.CreatedAt),
		PgTimestamptzToTime(model.UpdatedAt),
	), nil
}

// RoleToModel converts a domain Role to a database model
func RoleToModel(role *account.Role) sqlc.Role {
	return sqlc.Role{
		ID:          role.ID().UUID(),
		Name:        role.Name(),
		Description: TextToPgText(role.Description()),
		IsActive:    role.IsActive(),
		CreatedAt:   TimeToPgTimestamptz(role.CreatedAt()),
		UpdatedAt:   TimeToPgTimestamptz(role.UpdatedAt()),
		DeletedAt:   pgtype.Timestamptz{}, // Will be null for active roles
	}
}

// ModelToRole converts a database model to a domain Role
func ModelToRole(model sqlc.Role) (*account.Role, error) {
	roleID, err := account.RoleIDFromString(model.ID.String())
	if err != nil {
		return nil, err
	}

	return account.NewRoleWithID(
		roleID,
		model.Name,
		PgTextToString(model.Description),
		model.IsActive,
		PgTimestamptzToTime(model.CreatedAt),
		PgTimestamptzToTime(model.UpdatedAt),
	), nil
}

// PermissionToModel converts a domain Permission to a database model
func PermissionToModel(permission *account.Permission) sqlc.Permission {
	return sqlc.Permission{
		ID:          permission.ID().UUID(),
		Name:        permission.Name(),
		Description: TextToPgText(permission.Description()),
		Resource:    permission.Resource(),
		Action:      permission.Action(),
		IsActive:    permission.IsActive(),
		CreatedAt:   TimeToPgTimestamptz(permission.CreatedAt()),
		UpdatedAt:   TimeToPgTimestamptz(permission.UpdatedAt()),
		DeletedAt:   pgtype.Timestamptz{}, // Will be null for active permissions
	}
}

// ModelToPermission converts a database model to a domain Permission
func ModelToPermission(model sqlc.Permission) (*account.Permission, error) {
	permissionID, err := account.PermissionIDFromString(model.ID.String())
	if err != nil {
		return nil, err
	}

	return account.NewPermissionWithID(
		permissionID,
		model.Name,
		PgTextToString(model.Description),
		model.Resource,
		model.Action,
		model.IsActive,
		PgTimestamptzToTime(model.CreatedAt),
		PgTimestamptzToTime(model.UpdatedAt),
	), nil
}

// Helper functions for type conversions

// TimeToPgTimestamptz converts time.Time to pgtype.Timestamptz
func TimeToPgTimestamptz(t time.Time) pgtype.Timestamptz {
	return pgtype.Timestamptz{
		Time:  t,
		Valid: true,
	}
}

// PgTimestamptzToTime converts pgtype.Timestamptz to time.Time
func PgTimestamptzToTime(ts pgtype.Timestamptz) time.Time {
	if ts.Valid {
		return ts.Time
	}
	return time.Time{}
}

// TextToPgText converts string to pgtype.Text
func TextToPgText(s string) pgtype.Text {
	if s == "" {
		return pgtype.Text{Valid: false}
	}
	return pgtype.Text{
		String: s,
		Valid:  true,
	}
}

// PgTextToString converts pgtype.Text to string
func PgTextToString(t pgtype.Text) string {
	if t.Valid {
		return t.String
	}
	return ""
}

// UUIDToPgUUID converts uuid.UUID to pgtype.UUID
func UUIDToPgUUID(id uuid.UUID) pgtype.UUID {
	return pgtype.UUID{
		Bytes: id,
		Valid: true,
	}
}

// PgUUIDToUUID converts pgtype.UUID to uuid.UUID
func PgUUIDToUUID(id pgtype.UUID) uuid.UUID {
	if id.Valid {
		return id.Bytes
	}
	return uuid.Nil
}

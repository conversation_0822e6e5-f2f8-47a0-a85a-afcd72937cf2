package security

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

// JWTBlacklist provides JWT token blacklisting functionality using Redis
type JWTBlacklist struct {
	client *redis.Client
}

// NewJWTBlacklist creates a new JWTBlacklist
func NewJWTBlacklist(client *redis.Client) *JWTBlacklist {
	return &JWTBlacklist{
		client: client,
	}
}

// BlacklistToken adds a JWT token to the blacklist
func (b *JWTBlacklist) BlacklistToken(ctx context.Context, tokenID string, expiresAt time.Time) error {
	if tokenID == "" {
		return fmt.Errorf("jwt_blacklist.BlacklistToken: token ID cannot be empty")
	}

	key := fmt.Sprintf("blacklist:token:%s", tokenID)
	
	// Calculate TTL - only store until the token would naturally expire
	ttl := time.Until(expiresAt)
	if ttl <= 0 {
		// Token is already expired, no need to blacklist
		return nil
	}

	// Store the token ID with its expiration time
	if err := b.client.Set(ctx, key, expiresAt.Unix(), ttl).Err(); err != nil {
		return fmt.Errorf("jwt_blacklist.BlacklistToken: %w", err)
	}

	return nil
}

// IsTokenBlacklisted checks if a JWT token is blacklisted
func (b *JWTBlacklist) IsTokenBlacklisted(ctx context.Context, tokenID string) (bool, error) {
	if tokenID == "" {
		return false, fmt.Errorf("jwt_blacklist.IsTokenBlacklisted: token ID cannot be empty")
	}

	key := fmt.Sprintf("blacklist:token:%s", tokenID)
	
	exists, err := b.client.Exists(ctx, key).Result()
	if err != nil {
		return false, fmt.Errorf("jwt_blacklist.IsTokenBlacklisted: %w", err)
	}

	return exists > 0, nil
}

// BlacklistUserTokens blacklists all tokens for a specific user
// This is useful when a user's account is compromised or deactivated
func (b *JWTBlacklist) BlacklistUserTokens(ctx context.Context, userID string, expiresAt time.Time) error {
	if userID == "" {
		return fmt.Errorf("jwt_blacklist.BlacklistUserTokens: user ID cannot be empty")
	}

	key := fmt.Sprintf("blacklist:user:%s", userID)
	
	// Calculate TTL
	ttl := time.Until(expiresAt)
	if ttl <= 0 {
		return nil
	}

	// Store user blacklist entry
	if err := b.client.Set(ctx, key, expiresAt.Unix(), ttl).Err(); err != nil {
		return fmt.Errorf("jwt_blacklist.BlacklistUserTokens: %w", err)
	}

	return nil
}

// IsUserBlacklisted checks if all tokens for a user are blacklisted
func (b *JWTBlacklist) IsUserBlacklisted(ctx context.Context, userID string) (bool, error) {
	if userID == "" {
		return false, fmt.Errorf("jwt_blacklist.IsUserBlacklisted: user ID cannot be empty")
	}

	key := fmt.Sprintf("blacklist:user:%s", userID)
	
	exists, err := b.client.Exists(ctx, key).Result()
	if err != nil {
		return false, fmt.Errorf("jwt_blacklist.IsUserBlacklisted: %w", err)
	}

	return exists > 0, nil
}

// CleanupExpiredTokens removes expired blacklist entries
// This is automatically handled by Redis TTL, but this method can be used for manual cleanup
func (b *JWTBlacklist) CleanupExpiredTokens(ctx context.Context) error {
	// Redis automatically removes expired keys, so this is mostly a no-op
	// However, we can scan for any keys that might have been missed
	
	now := time.Now().Unix()
	
	// Scan for blacklist token keys
	iter := b.client.Scan(ctx, 0, "blacklist:token:*", 100).Iterator()
	for iter.Next(ctx) {
		key := iter.Val()
		
		// Get the expiration timestamp
		expiresAtStr, err := b.client.Get(ctx, key).Result()
		if err != nil {
			if errors.Is(err, redis.Nil) {
				continue // Key already expired
			}
			// Log error but continue
			continue
		}
		
		// Parse expiration time
		var expiresAt int64
		if _, err := fmt.Sscanf(expiresAtStr, "%d", &expiresAt); err != nil {
			// Invalid format, delete the key
			_ = b.client.Del(ctx, key)
			continue
		}
		
		// Check if expired
		if expiresAt < now {
			_ = b.client.Del(ctx, key)
		}
	}
	
	if err := iter.Err(); err != nil {
		return fmt.Errorf("jwt_blacklist.CleanupExpiredTokens: %w", err)
	}

	// Scan for blacklist user keys
	iter = b.client.Scan(ctx, 0, "blacklist:user:*", 100).Iterator()
	for iter.Next(ctx) {
		key := iter.Val()
		
		// Get the expiration timestamp
		expiresAtStr, err := b.client.Get(ctx, key).Result()
		if err != nil {
			if errors.Is(err, redis.Nil) {
				continue // Key already expired
			}
			continue
		}
		
		// Parse expiration time
		var expiresAt int64
		if _, err := fmt.Sscanf(expiresAtStr, "%d", &expiresAt); err != nil {
			// Invalid format, delete the key
			_ = b.client.Del(ctx, key)
			continue
		}
		
		// Check if expired
		if expiresAt < now {
			_ = b.client.Del(ctx, key)
		}
	}
	
	if err := iter.Err(); err != nil {
		return fmt.Errorf("jwt_blacklist.CleanupExpiredTokens: %w", err)
	}

	return nil
}

// GetBlacklistStats returns statistics about the blacklist
func (b *JWTBlacklist) GetBlacklistStats(ctx context.Context) (map[string]int64, error) {
	stats := make(map[string]int64)
	
	// Count blacklisted tokens
	tokenKeys, err := b.client.Keys(ctx, "blacklist:token:*").Result()
	if err != nil {
		return nil, fmt.Errorf("jwt_blacklist.GetBlacklistStats: %w", err)
	}
	stats["blacklisted_tokens"] = int64(len(tokenKeys))
	
	// Count blacklisted users
	userKeys, err := b.client.Keys(ctx, "blacklist:user:*").Result()
	if err != nil {
		return nil, fmt.Errorf("jwt_blacklist.GetBlacklistStats: %w", err)
	}
	stats["blacklisted_users"] = int64(len(userKeys))
	
	return stats, nil
}

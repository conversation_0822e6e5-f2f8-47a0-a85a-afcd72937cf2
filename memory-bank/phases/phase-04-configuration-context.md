# Phase 4: Configuration Bounded Context Implementation

## 1. Overview and Goals

This document outlines the fourth phase of refactoring the `payment-service`, focusing on the implementation of the `configuration` bounded context. This phase builds directly on the foundational infrastructure established in Phase 1 and the domain event mechanism from Phase 2.

**Goal for Phase 4:** To implement a secure, robust, and Clean Architecture-compliant `configuration` bounded context, centralizing the management of payment providers, payment channels, and their complex mappings, including secure handling of provider secrets and dynamic configurations.

**Objectives for Phase 4:**
1.  Implement core CRUD operations for Payment Providers and Payment Channels.
2.  Establish secure storage and management of sensitive payment provider credentials.
3.  Implement management for `PaymentProviderChannelMappings` and `CompanyPaymentProviderChannelMappings`.
4.  Standardize handling of dynamic configurations using JSONB.
5.  Ensure all implementations adhere to the defined coding guidelines and leverage the infrastructure from Phase 1 and domain events from Phase 2.
6.  Develop comprehensive unit, integration, and end-to-end tests for the `configuration` context.

**Scope for Phase 4:**
*   Refactoring `modules/payment_channel`, `modules/payment_provider`, and `modules/channel_mapping` into `internal/app/configuration` and `internal/domain/configuration`.
*   Updating database schemas for `payment_channels`, `payment_channel_types`, `payment_providers`, `payment_provider_channel_mappings`, `company_payment_provider_channel_mappings`, and `company_payment_provider_mappings` to use UUIDs and encrypted secrets.
*   Implementing `PaymentChannelService`, `PaymentProviderService`, and `ChannelMappingService` for managing these entities.
*   Implementing encryption/decryption logic for `encrypted_provider_secrets`.
*   Handling `Capability` (Cash-In/Cash-Out) for mappings.
*   Publishing domain events for significant changes (e.g., `PaymentProviderUpdatedEvent`, `ChannelMappingChangedEvent`).
*   Developing API endpoints for managing payment channels, providers, and mappings.
*   Creating initial unit, integration, and E2E tests for the `configuration` context.

**Out of Scope for Phase 4:**
*   Detailed fee calculation logic (the `configuration` context will provide the rules, but `billing` will perform the calculation).
*   Complex inter-context communication beyond basic domain event publishing/subscribing.
*   Any other bounded contexts (billing, notification, reconciliation).

## 2. Architectural Principles (Recap from Phase 1 & 2)

This refactoring effort formally adopts and enforces **Clean Architecture** and **Domain-Driven Design (DDD)** principles to create clear boundaries between different layers of the application and promote scalability through Bounded Contexts.

### 2.1. The Dependency Rule (Clean Architecture)

Dependencies must only point inwards: `Infrastructure` -> `Application` -> `Domain`.
*   **Domain Layer**: Contains business entities and rules. It has zero dependencies on any outer layer.
*   **Application Layer**: Contains use cases that orchestrate the business logic. It defines interfaces that the Infrastructure layer implements.
*   **Infrastructure Layer**: Contains all external-facing components (database, payment providers, etc.). It depends on the Application layer.

```mermaid
graph TD
    subgraph "Domain Layer"
        direction LR
        D_Entities[Entities]
        D_Interfaces[Repository Interfaces]
    end

    subgraph "Application Layer"
        direction LR
        A_UseCases[Use Cases] --> D_Interfaces
        A_Interfaces[Provider Interfaces]
    end

    subgraph "Infrastructure Layer"
        direction LR
        I_Handlers[HTTP Handlers] --> A_UseCases
        I_Adapters[Provider Adapters] -- implements --> A_Interfaces
        I_Repos[Repository Implementations] -- implements --> D_Interfaces
    end

    I_Handlers --> A_UseCases
    A_UseCases --> D_Entities
    I_Repos --> D_Entities
    I_Adapters -- implements --> A_Interfaces
    A_UseCases -- depends on --> A_Interfaces
```

### 2.2. Bounded Contexts (Domain-Driven Design)

The `configuration` bounded context is the focus of this phase.

*   **`account`**: Manages identity and access control for internal system users (admins, support, etc.).
*   **`organization`**: Manages the external parties involved in transactions (Companies, Partners, Products).
*   **`billing`**: The core transactional context that handles the entire payment lifecycle (Transactions, TransactionItems, TransactionHistory).
*   **`configuration`**: Manages the "how" of payments, including providers and channels (PaymentProvider, PaymentChannel, ProviderChannelMapping).
*   **`notification`**: Responsible for all outgoing communications (e.g., webhooks, emails).
*   **`reconciliation`**: Responsible for matching internal transaction records with external financial data.

### 2.3. Presentation Layer: Ports & Adapters

The presentation layer will adopt a Ports and Adapters approach, living in `/internal/ports` and responsible for translating external requests into calls to the application layer.

*   **`/ports/rest/`**: Contains all HTTP handlers, separated by their "persona" or purpose.
    *   **`openapi/`**: For external client APIs.
    *   **`dashboard/`**: For the internal admin panel API.
    *   **`callback/`**: For incoming webhooks from third parties.
*   **Shared Use Cases**: All handlers will call the same use cases in the `/internal/app` layer, ensuring business logic is centralized and consistently applied.

### 2.4. Target Directory Structure

The new directory structure reflects the DDD approach and Clean Architecture principles. This phase will populate the `internal/app/configuration` and `internal/domain/configuration` directories.

```
/payment-service/
├── cmd/
│   └── api/
│       ├── server/                # Server setup, DI, and routing
│       │   ├── server.go
│       │   ├── dependencies.go
│       │   └── routes.go
│       └── main.go                # Lean application entry point
├── internal/
│   ├── app/                       # Application layer (use cases, services)
│   │   ├── account/
│   │   ├── organization/
│   │   ├── billing/
│   │   ├── configuration/         # <-- Populated in this phase
│   │   └── notification/
│   ├── domain/                    # Domain layer (entities, value objects, core business logic)
│   │   ├── account/
│   │   ├── organization/
│   │   ├── billing/
│   │   ├── configuration/         # <-- Populated in this phase
│   │   └── notification/
│   ├── ports/                     # Presentation Layer (Handlers)
│   │   └── rest/
│   │       ├── openapi/
│   │       ├── dashboard/
│   │       └── callback/
│   └── infrastructure/            # Infrastructure layer (external concerns)
│       ├── database/              # PostgreSQL repository implementations
│       ├── provider/              # Adapters for the gateway packages
│       ├── cache/                 # Redis implementation
│       └── eventbus/              # Domain Event Bus implementation
├── pkg/
│   ├── gateways/                  # External, reusable gateway packages
│   │   ├── xendit/
│   │   │   ├── client.go
│   │   │   └── go.mod
│   │   └── nicepay/
│   │       ├── client.go
│   │       └── go.mod
│   └── ... (other shared libraries)
├── api/                           # API definitions (e.g., OpenAPI/Swagger specs)
├── db/
│   └── migrations/                # Database migration .sql files
├── docs/                          # Project documentation
└── scripts/                       # Scripts for build, deployment, etc.
```

## 3. Key Architectural Decisions (Recap from Phase 1 & 2)

### 3.1. Manual Dependency Injection

To maintain full control over the application's startup process and avoid external dependencies, **manual dependency injection** will be used. All dependencies will be constructed and "wired" together in the `/cmd/api/server` package.

### 3.2. Externalized Gateway Packages

All third-party payment provider integrations (e.g., Xendit, NicePay) will be built as **standalone, reusable Go packages** located in `/pkg/gateways`.

### 3.3. Internationalization (i18n)

The application will support multiple languages, starting with English (en) and Indonesian (id). This will impact user-facing strings in API responses, validation messages, and notifications.

### 3.4. Domain Event Mechanism

The in-process domain event mechanism established in Phase 2 will be utilized for inter-context communication, allowing `configuration` to publish events that other contexts (e.g., `billing`, `notification`) can subscribe to.

## 4. Standardized Libraries (Recap from Phase 1)

The following libraries will be standardized across the project:

*   **Database Driver:** `pgx/v5`
*   **SQL Code Generator:** `sqlc`
*   **HTTP Framework:** `Gin`
*   **Logger:** `Zerolog`
*   **Configuration:** `Koanf`
*   **HTTP Client:** `Resty v2`
*   **Message Broker:** `go-rabbitmq`
*   **Integration Testing:** `testcontainers-go`
*   **Database Migrations:** `golang-migrate/migrate`
*   **Error Reporting:** `sentry-go`
*   **Internationalization:** `go-i18n`

## 5. Coding Conventions & Guidelines (Recap from Phase 1)

Adherence to these guidelines is mandatory for all contributors to ensure a high-quality, consistent, and maintainable codebase.

### 5.1. General Principles & Idiomatic Go
*   **Rule 1.1 (Simplicity - KISS):** Prefer simple, clear solutions over complex ones.
*   **Rule 1.2 (Effective Go):** Follow the principles outlined in the official [Effective Go](https://go.dev/doc/effective_go) document.
*   **Rule 1.3 (Package Naming):** All package names MUST be short-lowercase.
*   **Rule 1.4 (Variable Naming):** All variable names MUST be `camelCase`.
*   **Rule 1.5 (Interfaces):** Define interfaces to decouple components.
*   **Rule 1.6 (Formatting):** All generated Go code MUST be formatted with `gofmt`.

### 5.2. Project Structure & Clean Architecture
*   **Rule 2.1 (Strict Layering):** The dependency direction `API` -> `Application` -> `Domain` MUST be strictly enforced.
*   **Rule 2.2 (Domain Purity):** The `internal/domain` package MUST NOT import any other project packages.
*   **Rule 2.3 (Thin Handlers):** API handlers (`internal/ports/rest`) MUST only parse requests, call a single application service method, and generate a standardized response.

### 5.3. Error Management Protocol
*   **Rule 3.1 (Error Wrapping):** Errors MUST be wrapped with context using `fmt.Errorf("package.function: %w", err)`.
*   **Rule 3.2 (Custom Domain Errors):** Use custom error variables for predictable business rule failures.
*   **Rule 3.3 (Centralized HTTP Error Mapping):** A dedicated Gin error-handling middleware is the only place where errors are translated into HTTP status codes.
*   **Rule 3.4 (Sentry for Unexpected Errors):** Use `webapi.ErrorWithSentry(c, err)` for all 5xx internal server errors.

### 5.4. Standardized API Responses (webapi package)
*   **Rule 4.1 (Mandatory Helpers):** All HTTP responses MUST be generated exclusively using helper functions from the `internal/ports/rest/webapi` package.
*   **Rule 4.2 (Success Structure):** The mandatory JSON structure for a successful response is: `{"status": "success", "data": { /* Actual response data */ }, "correlationId": "UUID_OF_REQUEST"}`.
*   **Rule 4.3 (Error Structure):** The mandatory JSON structure for an error response is: `{"status": "error", "error": {"code": "UNIQUE_ERROR_CODE", "message": "Human-readable error description."}, "correlationId": "UUID_OF_REQUEST"}`.
*   **Rule 4.4 (Helper Signatures and Usage):** Adhere to the defined signatures for `webapi.Success`, `webapi.SuccessPaginated`, `webapi.Error`, and `webapi.ErrorWithSentry`.

### 5.5. Logging Best Practices (zerolog)
*   **Rule 5.1 (Structured Logging):** All logging MUST be done using the global `zerolog` instance.
*   **Rule 5.2 (Contextual Logging):** Every log statement MUST include the `trace_id` from the context.
*   **Rule 5.3 (Error Logging):** When logging an error, the error object MUST be included using `.Err(err)`.

### 5.6. Concurrency and Graceful Shutdown
*   **Rule 6.1 (Panic Safety):** Any new goroutine MUST use a `defer...recover()` block, logging any recovered panic to Sentry.
*   **Rule 6.2 (Graceful Termination):** Any long-running goroutine MUST accept a `context.Context` and listen on `ctx.Done()`.
*   **Rule 6.3 (Server Shutdown):** `main.go` must implement graceful shutdown for the HTTP server.

### 5.7. Gin Middleware
*   **Rule 7.1 (Tracing Middleware):** MUST be the first middleware, generating a `trace_id`.
*   **Rule 7.2 (Logger Middleware):** MUST log request and response details.
*   **Rule 7.3 (Error Middleware):** MUST be the last middleware to centralize error handling.

### 5.8. Database Access (sqlc with pgx/v5)
*   **Rule 8.1 (SQL Files):** All SQL queries MUST be defined in `.sql` files.
*   **Rule 8.2 (sqlc.yaml Configuration):** The `sqlc.yaml` file MUST be configured to use `sql_package: "pgx/v5"` and appropriate type overrides.
*   **Rule 8.3 (Type Conversion):** All conversions between domain types and `pgtype` MUST be handled in a dedicated `internal/infrastructure/database/converters` package.
*   **Rule 8.4 (Transactions):** Repository methods that perform writes MUST accept `pgx.Tx` as a parameter.

### 5.9. Testing and CI/CD Strategy
*   **Rule 9.1 (Containerized Testing):** All integration and end-to-end tests that require external services (like a database or Redis) MUST use `testcontainers-go`.
*   **Rule 9.2 (Seeding Data for Tests):** For every new `.up.sql` migration file, a corresponding test data seed file MUST be created.
*   **Rule 9.3 (Test Coverage):** API handlers, application services, and repositories MUST have dedicated test files.
*   **Rule 9.4 (CI/CD Gate):** A pull request MUST NOT be allowed to merge into the main branch unless all tests (`go test ./...`) pass.

### 5.10. Internationalization (i18n)
*   **Rule 10.1 (Localization Support):** The application MUST support English (en) and Indonesian (id) locales.

### 5.11. Data Querying & Listing Guidelines

*   **Rule 11.1 (Repository Listing - Pagination):** All repository methods responsible for listing data that may require pagination MUST include a `ListWithPagination` method.
*   **Rule 11.2 (Repository Listing - Query Features):** All repository `List` and `ListWithPagination` methods MUST support common querying features including searching (e.g., full-text search, partial matching), filtering, and ordering.
*   **Rule 11.3 (Repository Listing - Filter Stacking):** Filtering capabilities in repository listing methods MUST allow for stacking multiple filter conditions (e.g., filtering by `status='active'` AND `category='electronics'`).
*   **Rule 11.4 (Repository Listing - Date Filtering):** If a data entity includes date fields, repository listing methods MUST support filtering by date ranges (e.g., "today", "yesterday", "last 7 days", "this month", "last month", custom start/end dates).

## 6. Database Schemas for Phase 4

This phase will introduce and refine the `configuration` related schemas.

### 6.1. Payment Channel and Provider Configuration Schemas (`configuration` Bounded Context)

#### `payment_channels` Table:
```sql
CREATE TABLE payment_channels (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    status BOOLEAN NOT NULL DEFAULT TRUE,
    payment_channel_type_id UUID NOT NULL, -- References payment_channel_types
    logo_id UUID, -- References files table (future relationship)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### `payment_channel_types` Table:
```sql
CREATE TABLE payment_channel_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payment_type VARCHAR(255) UNIQUE NOT NULL,
    config_templates JSONB, -- Stores templates for dynamic configuration fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### `payment_providers` Table:
```sql
CREATE TABLE payment_providers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    status BOOLEAN NOT NULL DEFAULT TRUE,
    secret_templates JSONB, -- Stores templates for provider secrets
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### `payment_provider_channel_mappings` Table:
```sql
CREATE TABLE payment_provider_channel_mappings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payment_provider_id UUID NOT NULL REFERENCES payment_providers(id),
    payment_channel_id UUID NOT NULL REFERENCES payment_channels(id),
    code VARCHAR(255) UNIQUE NOT NULL,
    max_transaction DECIMAL(18,2),
    sla INT,
    capability INT NOT NULL, -- 1 for Cash-In, 2 for Cash-Out
    provider_channel_code VARCHAR(255) NOT NULL,
    payment_instructions TEXT,
    channel_type_configs JSONB, -- Stores channel-specific configurations
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(payment_provider_id, payment_channel_id, capability)
);
```

#### `company_payment_provider_channel_mappings` Table:
```sql
CREATE TABLE company_payment_provider_channel_mappings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id),
    payment_provider_id UUID NOT NULL REFERENCES payment_providers(id),
    payment_channel_id UUID NOT NULL REFERENCES payment_channels(id),
    fee_fix_value DECIMAL(10,2),
    fee_percentage DECIMAL(5,2),
    capability INT NOT NULL, -- 1 for Cash-In, 2 for Cash-Out
    expired_time INT,
    status BOOLEAN NOT NULL DEFAULT TRUE,
    channel_type_configs JSONB, -- Stores company-specific channel configurations
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(company_id, payment_provider_id, payment_channel_id, capability)
);
```

#### `company_payment_provider_mappings` Table:
```sql
CREATE TABLE company_payment_provider_mappings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id),
    payment_provider_id UUID NOT NULL REFERENCES payment_providers(id),
    encrypted_provider_secrets TEXT NOT NULL, -- Stores encrypted provider secrets
    status BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(company_id, payment_provider_id)
);
```

## 7. Detailed Tasks for Phase 4

### 7.1. Configuration Domain & Repository Implementation
*   **Task 7.1.1: Define Configuration Domain Entities**
    *   Create `internal/domain/configuration/payment_channel.go`, `payment_provider.go`, `mapping.go`.
    *   Define structs for `PaymentChannel`, `PaymentChannelType`, `PaymentProvider`, `PaymentProviderChannelMapping`, `CompanyPaymentProviderChannelMapping`, `CompanyPaymentProviderMapping`.
*   **Task 7.1.2: Update Database Migrations**
    *   Create a new migration `db/migrations/000004_configuration_tables.up.sql` to define all `configuration` related tables. Ensure UUIDs are used for primary keys and `encrypted_provider_secrets` is handled.
    *   Create corresponding `000004_configuration_tables.down.sql`.
*   **Task 7.1.3: Implement Configuration Repositories**
    *   Create `internal/infrastructure/database/configuration/channel_repository.go`, `provider_repository.go`, `mapping_repository.go`.
    *   Implement repository interfaces for all `configuration` entities using `sqlc`-generated code.
    *   Ensure all `pgtype` conversions are handled by `internal/infrastructure/database/converters`.

### 7.2. Configuration Application Use Cases
*   **Task 7.2.1: Implement Payment Channel Service**
    *   Create `internal/app/configuration/channel_service.go`.
    *   Implement methods for `CreatePaymentChannel`, `GetPaymentChannelByID`, `UpdatePaymentChannel`, `DeletePaymentChannel`.
*   **Task 7.2.2: Implement Payment Provider Service**
    *   Create `internal/app/configuration/provider_service.go`.
    *   Implement methods for `CreatePaymentProvider`, `GetPaymentProviderByID`, `UpdatePaymentProvider`, `DeletePaymentProvider`.
    *   Implement encryption/decryption logic for `encrypted_provider_secrets` within this service.
    *   Publish `PaymentProviderUpdatedEvent` using `EventPublisher`.
*   **Task 7.2.3: Implement Channel Mapping Service**
    *   Create `internal/app/configuration/mapping_service.go`.
    *   Implement methods for `CreatePaymentProviderChannelMapping`, `UpdatePaymentProviderChannelMapping`, `CreateCompanyPaymentProviderChannelMapping`, `UpdateCompanyPaymentProviderChannelMapping`.
    *   Ensure `Capability` (Cash-In/Cash-Out) is correctly handled.
    *   Publish `ChannelMappingChangedEvent` using `EventPublisher`.

### 7.3. Configuration Presentation Layer (API)
*   **Task 7.3.1: Implement Payment Channel Handlers**
    *   Create `internal/ports/rest/dashboard/channel_handler.go`.
    *   Implement handlers for `POST /payment-channels`, `GET /payment-channels/{id}`, `PUT /payment-channels/{id}`, `DELETE /payment-channels/{id}`.
*   **Task 7.3.2: Implement Payment Provider Handlers**
    *   Create `internal/ports/rest/dashboard/provider_handler.go`.
    *   Implement handlers for `POST /payment-providers`, `GET /payment-providers/{id}`, `PUT /payment-providers/{id}`, `DELETE /payment-providers/{id}`.
*   **Task 7.3.3: Implement Channel Mapping Handlers**
    *   Create `internal/ports/rest/dashboard/mapping_handler.go`.
    *   Implement handlers for `POST /channel-mappings`, `GET /channel-mappings/{id}`, `PUT /channel-mappings/{id}`, `DELETE /channel-mappings/{id}`.
    *   Implement handlers for `POST /company-mappings`, `GET /company-mappings/{id}`, `PUT /company-mappings/{id}`, `DELETE /company-mappings/{id}`.
*   **Task 7.3.4: Update Main Application Wiring**
    *   Modify `cmd/api/server/dependencies.go` to wire up the new `Configuration` repositories and services.
    *   Modify `cmd/api/server/routes.go` to define the new `configuration` endpoints, ensuring proper authentication and authorization middleware is applied.

### 7.4. Testing and Validation
*   **Task 7.4.1: Update Seed Data**
    *   Create `testdata/seeds/000004_seed_configuration_data.sql` with sample data for all `configuration` tables.
*   **Task 7.4.2: Implement Unit Tests**
    *   Write unit tests for `internal/app/configuration` services (mocking repositories and `EventPublisher`).
*   **Task 7.4.3: Implement Integration Tests**
    *   Write integration tests for `internal/infrastructure/database/configuration` repositories using `testcontainers-go` for PostgreSQL.
    *   Write integration tests for `internal/app/configuration` services using `testcontainers-go` for PostgreSQL.
*   **Task 7.4.4: Implement End-to-End Tests**
    *   Write E2E tests for `configuration` API endpoints using `SetupTestServer()` from Phase 1.
    *   Test CRUD operations for all `configuration` entities.
    *   Test secure secret management (encryption/decryption).
    *   Verify that relevant domain events are published correctly.
*   **Task 7.4.5: Update Postman Collection**
    *   Create `docs/postman/collections/configuration.postman_collection.json` with requests for all new `configuration` endpoints.

## 8. Phase 4 Deliverables

*   Fully implemented `configuration` bounded context with management for payment providers, channels, and mappings.
*   Updated database schemas for all `configuration` related tables, including secure secret storage.
*   Working CRUD operations for all core `configuration` entities.
*   Publishing of relevant domain events for `configuration` changes.
*   Comprehensive unit, integration, and E2E tests for the `configuration` context.
*   Updated Postman collection for `configuration` endpoints.
*   All code adhering to the defined coding guidelines.

## 9. Phase 4 Diagram:

```mermaid
graph TD
    subgraph "Phase 4: Configuration Bounded Context Implementation"
        A[7.1. Configuration Domain & Repository] --> B(7.2. Configuration Application Use Cases)
        B --> C(7.3. Configuration Presentation Layer)
        C --> D(7.4. Testing and Validation)
        A --> D
        B --> D
        C --> D
    end

    subgraph "7.1. Configuration Domain & Repository"
        A1[7.1.1: Define Configuration Domain Entities]
        A2[7.1.2: Update Database Migrations]
        A3[7.1.3: Implement Configuration Repositories]
    end

    subgraph "7.2. Configuration Application Use Cases"
        B1[7.2.1: Implement Payment Channel Service]
        B2[7.2.2: Implement Payment Provider Service]
        B3[7.2.3: Implement Channel Mapping Service]
    end

    subgraph "7.3. Configuration Presentation Layer (API)"
        C1[7.3.1: Implement Payment Channel Handlers]
        C2[7.3.2: Implement Payment Provider Handlers]
        C3[7.3.3: Implement Channel Mapping Handlers]
        C4[7.3.4: Update Main Application Wiring]
    end

    subgraph "7.4. Testing and Validation"
        D1[7.4.1: Update Seed Data]
        D2[7.4.2: Implement Unit Tests]
        D3[7.4.3: Implement Integration Tests]
        D4[7.4.4: Implement End-to-End Tests]
        D5[7.4.5: Update Postman Collection]
    end

    style A1 fill:#f9f,stroke:#333,stroke-width:2px
    style A2 fill:#f9f,stroke:#333,stroke-width:2px
    style A3 fill:#f9f,stroke:#333,stroke-width:2px

    style B1 fill:#bbf,stroke:#333,stroke-width:2px
    style B2 fill:#bbf,stroke:#333,stroke-width:2px
    style B3 fill:#bbf,stroke:#333,stroke-width:2px

    style C1 fill:#ccf,stroke:#333,stroke-width:2px
    style C2 fill:#ccf,stroke:#333,stroke-width:2px
    style C3 fill:#ccf,stroke:#333,stroke-width:2px
    style C4 fill:#ccf,stroke:#333,stroke-width:2px

    style D1 fill:#ddf,stroke:#333,stroke-width:2px
    style D2 fill:#ddf,stroke:#333,stroke-width:2px
    style D3 fill:#ddf,stroke:#333,stroke-width:2px
    style D4 fill:#ddf,stroke:#333,stroke-width:2px
    style D5 fill:#ddf,stroke:#333,stroke-width:2px

## 10. Phase Completion Checklist

This checklist serves as a verification tool to ensure all tasks, requirements, rules, and guidelines for Phase 4 have been successfully completed and adhered to. Before marking this phase as complete, all items below must be checked off.

### 10.1. General Completion & Quality
*   [ ] All tasks outlined in Section 7 ("Detailed Tasks for Phase 4") have been implemented.
*   [ ] The project builds successfully without errors (`go build ./...`).
*   [ ] All tests pass (`go test ./...`).
*   [ ] Code has been formatted with `gofmt` (`gofmt -s -w .`).
*   [ ] No new warnings or errors are introduced by linters (e.g., `golangci-lint run`).
*   [ ] All dependencies are properly managed (`go mod tidy`).

### 10.2. Architectural Adherence
*   [ ] The `internal/app/configuration` and `internal/domain/configuration` directories are populated as per the target structure.
*   [ ] Dependencies within the `configuration` context strictly follow the Clean Architecture rule.
*   [ ] Domain Event Mechanism is correctly utilized for publishing events from the `configuration` context.

### 10.3. Core Functionality Verification
*   [ ] Core CRUD operations for Payment Providers and Payment Channels are functional.
*   [ ] Secure storage and management of sensitive payment provider credentials (encryption/decryption) is implemented and functional.
*   [ ] Management for `PaymentProviderChannelMappings` and `CompanyPaymentProviderChannelMappings` is functional.
*   [ ] Dynamic configurations using JSONB are handled correctly.
*   [ ] `Capability` (Cash-In/Cash-Out) is correctly handled for mappings.
*   [ ] Relevant domain events (`PaymentProviderUpdatedEvent`, `ChannelMappingChangedEvent`) are published.
*   [ ] API endpoints for managing payment channels, providers, and mappings are functional.

### 10.4. Testing & Validation
*   [ ] All unit tests for `configuration` services are passing.
*   [ ] All integration tests for `configuration` repositories and services are passing.
*   [ ] All E2E tests for `configuration` API endpoints are passing, covering CRUD operations and secure secret management.
*   [ ] E2E tests verify that relevant domain events are published.
*   [ ] Postman collection for `configuration` endpoints is updated and functional.
*   [ ] Seed data for `configuration` tables is correctly applied in tests.

### 10.5. Coding Guidelines Compliance (Self-Assessment)
*   [ ] **Rule 1 (General):** `gofmt`, `camelCase`, short-lowercase package names, interface-based design are followed.
*   [ ] **Rule 2 (Architecture):** `API` -> `Application` -> `Domain` dependency rule and thin handlers are enforced.
*   [ ] **Rule 3 (Error Handling):** Error wrapping, custom errors, centralized mapping, and Sentry integration are used.
*   [ ] **Rule 4 (API Responses):** `webapi` helpers are used for all responses, and error messages are localized.
*   [ ] **Rule 5 (Logging):** Structured, contextual logging with `zerolog` is implemented.
*   [ ] **Rule 6 (Concurrency):** Panic safety and graceful termination are considered.
*   [ ] **Rule 7 (Middleware):** The exact middleware order is followed.
*   [ ] **Rule 8 (Database):** `.sql` files for `sqlc`, `pgx/v5` configuration, centralized type converters, and transactional repositories are used.
*   [ ] **Rule 9 (Testing & CI/CD):** `testcontainers-go`, DDL/DML separation, and test coverage are adhered to.
*   [ ] **Rule 10 (Internationalization):** Localization support is correctly implemented.
package security

import (
	"crypto/rand"
	"crypto/subtle"
	"encoding/base64"
	"errors"
	"fmt"
	"strings"

	"golang.org/x/crypto/argon2"
	"golang.org/x/crypto/bcrypt"

	"github.com/wongpinter/payment-gateway/internal/domain/account"
)

// PasswordHasher implements the account.PasswordHasher interface
type PasswordHasher struct {
	algorithm string // "argon2" or "bcrypt"
	// Argon2 parameters
	argon2Time    uint32
	argon2Memory  uint32
	argon2Threads uint8
	argon2KeyLen  uint32
	argon2SaltLen uint32
	// Bcrypt parameters
	bcryptCost int
}

// PasswordHasherConfig holds configuration for password hashing
type PasswordHasherConfig struct {
	Algorithm     string `mapstructure:"algorithm"`     // "argon2" or "bcrypt"
	Argon2Time    uint32 `mapstructure:"argon2_time"`    // Number of iterations
	Argon2Memory  uint32 `mapstructure:"argon2_memory"`  // Memory usage in KB
	Argon2Threads uint8  `mapstructure:"argon2_threads"` // Number of threads
	Argon2KeyLen  uint32 `mapstructure:"argon2_keylen"`  // Length of derived key
	Argon2SaltLen uint32 `mapstructure:"argon2_saltlen"` // Length of salt
	BcryptCost    int    `mapstructure:"bcrypt_cost"`    // Bcrypt cost factor
}

// DefaultPasswordHasherConfig returns default configuration for password hashing
func DefaultPasswordHasherConfig() PasswordHasherConfig {
	return PasswordHasherConfig{
		Algorithm:     "argon2",
		Argon2Time:    1,
		Argon2Memory:  64 * 1024, // 64 MB
		Argon2Threads: 4,
		Argon2KeyLen:  32,
		Argon2SaltLen: 16,
		BcryptCost:    12,
	}
}

// NewPasswordHasher creates a new PasswordHasher with the given configuration
func NewPasswordHasher(config PasswordHasherConfig) *PasswordHasher {
	return &PasswordHasher{
		algorithm:     config.Algorithm,
		argon2Time:    config.Argon2Time,
		argon2Memory:  config.Argon2Memory,
		argon2Threads: config.Argon2Threads,
		argon2KeyLen:  config.Argon2KeyLen,
		argon2SaltLen: config.Argon2SaltLen,
		bcryptCost:    config.BcryptCost,
	}
}

// Hash generates a hash from a plain text password
func (h *PasswordHasher) Hash(password string) (string, error) {
	switch h.algorithm {
	case "argon2":
		return h.hashArgon2(password)
	case "bcrypt":
		return h.hashBcrypt(password)
	default:
		return "", fmt.Errorf("unsupported hashing algorithm: %s", h.algorithm)
	}
}

// Verify checks if a plain text password matches a hash
func (h *PasswordHasher) Verify(password, hash string) error {
	// Determine the algorithm from the hash format
	if strings.HasPrefix(hash, "$argon2id$") {
		return h.verifyArgon2(password, hash)
	} else if strings.HasPrefix(hash, "$2a$") || strings.HasPrefix(hash, "$2b$") || strings.HasPrefix(hash, "$2y$") {
		return h.verifyBcrypt(password, hash)
	} else {
		return errors.New("unsupported hash format")
	}
}

// hashArgon2 generates an Argon2id hash
func (h *PasswordHasher) hashArgon2(password string) (string, error) {
	// Generate a random salt
	salt := make([]byte, h.argon2SaltLen)
	if _, err := rand.Read(salt); err != nil {
		return "", fmt.Errorf("failed to generate salt: %w", err)
	}

	// Generate the hash
	hash := argon2.IDKey([]byte(password), salt, h.argon2Time, h.argon2Memory, h.argon2Threads, h.argon2KeyLen)

	// Encode the hash in the standard format
	b64Salt := base64.RawStdEncoding.EncodeToString(salt)
	b64Hash := base64.RawStdEncoding.EncodeToString(hash)

	// Format: $argon2id$v=19$m=memory,t=time,p=threads$salt$hash
	encodedHash := fmt.Sprintf("$argon2id$v=19$m=%d,t=%d,p=%d$%s$%s",
		h.argon2Memory, h.argon2Time, h.argon2Threads, b64Salt, b64Hash)

	return encodedHash, nil
}

// verifyArgon2 verifies an Argon2id hash
func (h *PasswordHasher) verifyArgon2(password, encodedHash string) error {
	// Parse the encoded hash
	parts := strings.Split(encodedHash, "$")
	if len(parts) != 6 {
		return errors.New("invalid argon2 hash format")
	}

	var version int
	if _, err := fmt.Sscanf(parts[2], "v=%d", &version); err != nil {
		return fmt.Errorf("invalid version: %w", err)
	}

	var memory, time uint32
	var threads uint8
	if _, err := fmt.Sscanf(parts[3], "m=%d,t=%d,p=%d", &memory, &time, &threads); err != nil {
		return fmt.Errorf("invalid parameters: %w", err)
	}

	salt, err := base64.RawStdEncoding.DecodeString(parts[4])
	if err != nil {
		return fmt.Errorf("invalid salt: %w", err)
	}

	hash, err := base64.RawStdEncoding.DecodeString(parts[5])
	if err != nil {
		return fmt.Errorf("invalid hash: %w", err)
	}

	// Generate hash with the same parameters
	otherHash := argon2.IDKey([]byte(password), salt, time, memory, threads, uint32(len(hash)))

	// Compare hashes using constant-time comparison
	if subtle.ConstantTimeCompare(hash, otherHash) == 1 {
		return nil
	}

	return errors.New("password verification failed")
}

// hashBcrypt generates a bcrypt hash
func (h *PasswordHasher) hashBcrypt(password string) (string, error) {
	hash, err := bcrypt.GenerateFromPassword([]byte(password), h.bcryptCost)
	if err != nil {
		return "", fmt.Errorf("failed to generate bcrypt hash: %w", err)
	}
	return string(hash), nil
}

// verifyBcrypt verifies a bcrypt hash
func (h *PasswordHasher) verifyBcrypt(password, hash string) error {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	if err != nil {
		if errors.Is(err, bcrypt.ErrMismatchedHashAndPassword) {
			return errors.New("password verification failed")
		}
		return fmt.Errorf("bcrypt verification error: %w", err)
	}
	return nil
}

// Ensure PasswordHasher implements the account.PasswordHasher interface
var _ account.PasswordHasher = (*PasswordHasher)(nil)

-- Test seed data for roles table
-- This file contains test data for the roles table used in integration tests
-- Updated to match the new Clean Architecture schema

-- Clear existing test roles (if any)
DELETE FROM roles WHERE id::text LIKE '550e8400-%';

INSERT INTO roles (id, name, description, is_active, created_at, updated_at) VALUES
    ('550e8400-e29b-41d4-a716-446655440001', 'Super Administrator', 'Full system access with all permissions', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('550e8400-e29b-41d4-a716-446655440002', 'Administrator', 'Administrative access with most permissions', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('550e8400-e29b-41d4-a716-446655440003', 'User Manager', 'User management access with limited permissions', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('550e8400-e29b-41d4-a716-446655440004', 'Payment Manager', 'Payment management permissions', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('550e8400-e29b-41d4-a716-446655440005', 'Viewer', 'Read-only access to most resources', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('550e8400-e29b-41d4-a716-446655440006', 'Regular User', 'Standard user access with basic permissions', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('550e8400-e29b-41d4-a716-446655440007', 'Inactive Role', 'Test role that is inactive', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert role-permission assignments for testing
-- Clear existing test role-permission assignments
DELETE FROM role_permissions WHERE role_id::text LIKE '550e8400-%';

-- Super Administrator gets all test permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT '550e8400-e29b-41d4-a716-446655440001', id FROM permissions WHERE id::text LIKE '11111111-%';

-- Administrator gets most permissions (excluding delete permissions and system admin)
INSERT INTO role_permissions (role_id, permission_id)
SELECT '550e8400-e29b-41d4-a716-446655440002', id FROM permissions
WHERE id::text LIKE '11111111-%' AND name NOT LIKE '%.delete' AND name != 'system.admin';

-- User Manager gets user and role management permissions
INSERT INTO role_permissions (role_id, permission_id) VALUES
    ('550e8400-e29b-41d4-a716-446655440003', '11111111-1111-1111-1111-111111111001'), -- user.create
    ('550e8400-e29b-41d4-a716-446655440003', '11111111-1111-1111-1111-111111111002'), -- user.read
    ('550e8400-e29b-41d4-a716-446655440003', '11111111-1111-1111-1111-111111111003'), -- user.update
    ('550e8400-e29b-41d4-a716-446655440003', '11111111-1111-1111-1111-111111111005'), -- user.list
    ('550e8400-e29b-41d4-a716-446655440003', '11111111-1111-1111-1111-111111111007'), -- role.read
    ('550e8400-e29b-41d4-a716-446655440003', '11111111-1111-1111-1111-111111111010'), -- role.list
    ('550e8400-e29b-41d4-a716-446655440003', '11111111-1111-1111-1111-111111111011'), -- permission.read
    ('550e8400-e29b-41d4-a716-446655440003', '11111111-1111-1111-1111-111111111012'); -- permission.list

-- Payment Manager gets payment and basic user read permissions
INSERT INTO role_permissions (role_id, permission_id) VALUES
    ('550e8400-e29b-41d4-a716-446655440004', '11111111-1111-1111-1111-111111111013'), -- payment.create
    ('550e8400-e29b-41d4-a716-446655440004', '11111111-1111-1111-1111-111111111014'), -- payment.read
    ('550e8400-e29b-41d4-a716-446655440004', '11111111-1111-1111-1111-111111111015'), -- payment.update
    ('550e8400-e29b-41d4-a716-446655440004', '11111111-1111-1111-1111-111111111017'), -- payment.list
    ('550e8400-e29b-41d4-a716-446655440004', '11111111-1111-1111-1111-111111111002'), -- user.read
    ('550e8400-e29b-41d4-a716-446655440004', '11111111-1111-1111-1111-111111111005'); -- user.list

-- Viewer gets read-only permissions
INSERT INTO role_permissions (role_id, permission_id) VALUES
    ('550e8400-e29b-41d4-a716-446655440005', '11111111-1111-1111-1111-111111111002'), -- user.read
    ('550e8400-e29b-41d4-a716-446655440005', '11111111-1111-1111-1111-111111111005'), -- user.list
    ('550e8400-e29b-41d4-a716-446655440005', '11111111-1111-1111-1111-111111111007'), -- role.read
    ('550e8400-e29b-41d4-a716-446655440005', '11111111-1111-1111-1111-111111111010'), -- role.list
    ('550e8400-e29b-41d4-a716-446655440005', '11111111-1111-1111-1111-111111111011'), -- permission.read
    ('550e8400-e29b-41d4-a716-446655440005', '11111111-1111-1111-1111-111111111012'), -- permission.list
    ('550e8400-e29b-41d4-a716-446655440005', '11111111-1111-1111-1111-111111111014'), -- payment.read
    ('550e8400-e29b-41d4-a716-446655440005', '11111111-1111-1111-1111-111111111017'), -- payment.list
    ('550e8400-e29b-41d4-a716-446655440005', '11111111-1111-1111-1111-111111111019'), -- organization.read
    ('550e8400-e29b-41d4-a716-446655440005', '11111111-1111-1111-1111-111111111022'); -- organization.list

-- Regular User gets basic permissions
INSERT INTO role_permissions (role_id, permission_id) VALUES
    ('550e8400-e29b-41d4-a716-446655440006', '11111111-1111-1111-1111-111111111002'), -- user.read (own profile)
    ('550e8400-e29b-41d4-a716-446655440006', '11111111-1111-1111-1111-111111111014'), -- payment.read (own payments)
    ('550e8400-e29b-41d4-a716-446655440006', '11111111-1111-1111-1111-111111111019'); -- organization.read (own org)

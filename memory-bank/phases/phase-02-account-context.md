# Phase 2: Account Bounded Context Implementation

## 1. Overview and Goals

This document outlines the second phase of refactoring the `payment-service`, focusing on the implementation of the `account` bounded context. This phase builds directly on the foundational infrastructure established in Phase 1.

**Goal for Phase 2:** To implement a secure, robust, and Clean Architecture-compliant `account` bounded context, covering user authentication, authorization, and basic user management functionalities.

**Objectives for Phase 2:**
1.  Implement secure password hashing.
2.  Refactor existing user management modules into the `account` bounded context.
3.  Implement JWT-based authentication with Redis-backed session management.
4.  Establish basic user management use cases (registration, login, password changes).
5.  Ensure all implementations adhere to the defined coding guidelines and leverage the infrastructure from Phase 1.
6.  Develop comprehensive unit, integration, and end-to-end tests for the `account` context.

**Scope for Phase 2:**
*   Secure password hashing implementation (`bcrypt` or `Argon2`).
*   Refactoring `modules/user_management` into `internal/app/account` and `internal/domain/account`.
*   Updating database schemas for `users`, `roles`, `role_permissions`, and `user_companies` to use UUIDs and `password_hash`.
*   Implementing `AuthService` for user registration, login, and refresh token handling.
*   Integrating Redis for JWT blacklisting and refresh token storage.
*   Developing API endpoints for authentication (`/auth/login`, `/auth/register`, `/auth/refresh`, `/auth/logout`).
*   Implementing authentication and authorization middleware.
*   Creating initial unit, integration, and E2E tests for the `account` context.

**Out of Scope for Phase 2:**
*   Advanced authorization features (e.g., fine-grained permissions beyond basic role checks).
*   Two-factor authentication (2FA).
*   Password reset/forgot password flows (these can be added in a subsequent phase).
*   Full `organization` bounded context implementation (only `user_companies` table is touched for user-company relationships).
*   Any other bounded contexts (billing, configuration, notification, reconciliation).

## 2. Architectural Principles (Recap from Phase 1)

This refactoring effort formally adopts and enforces **Clean Architecture** and **Domain-Driven Design (DDD)** principles to create clear boundaries between different layers of the application and promote scalability through Bounded Contexts.

### 2.1. The Dependency Rule (Clean Architecture)

Dependencies must only point inwards: `Infrastructure` -> `Application` -> `Domain`.
*   **Domain Layer**: Contains business entities and rules. It has zero dependencies on any outer layer.
*   **Application Layer**: Contains use cases that orchestrate the business logic. It defines interfaces that the Infrastructure layer implements.
*   **Infrastructure Layer**: Contains all external-facing components (database, payment providers, etc.). It depends on the Application layer.

```mermaid
graph TD
    subgraph "Domain Layer"
        direction LR
        D_Entities[Entities]
        D_Interfaces[Repository Interfaces]
    end

    subgraph "Application Layer"
        direction LR
        A_UseCases[Use Cases] --> D_Interfaces
        A_Interfaces[Provider Interfaces]
    end

    subgraph "Infrastructure Layer"
        direction LR
        I_Handlers[HTTP Handlers] --> A_UseCases
        I_Adapters[Provider Adapters] -- implements --> A_Interfaces
        I_Repos[Repository Implementations] -- implements --> D_Interfaces
    end

    I_Handlers --> A_UseCases
    A_UseCases --> D_Entities
    I_Repos --> D_Entities
    I_Adapters -- implements --> A_Interfaces
    A_UseCases -- depends on --> A_Interfaces
```

### 2.2. Bounded Contexts (Domain-Driven Design)

The `account` bounded context is the focus of this phase.

*   **`account`**: Manages identity and access control for internal system users (admins, support, etc.).
*   **`organization`**: Manages the external parties involved in transactions (Companies, Partners, Products).
*   **`billing`**: The core transactional context that handles the entire payment lifecycle (Transactions, TransactionItems, TransactionHistory).
*   **`configuration`**: Manages the "how" of payments, including providers and channels (PaymentProvider, PaymentChannel, ProviderChannelMapping).
*   **`notification`**: Responsible for all outgoing communications (e.g., webhooks, emails).
*   **`reconciliation`**: Responsible for matching internal transaction records with external financial data.

### 2.3. Presentation Layer: Ports & Adapters

The presentation layer will adopt a Ports and Adapters approach, living in `/internal/ports` and responsible for translating external requests into calls to the application layer.

*   **`/ports/rest/`**: Contains all HTTP handlers, separated by their "persona" or purpose.
    *   **`openapi/`**: For external client APIs.
    *   **`dashboard/`**: For the internal admin panel API.
    *   **`callback/`**: For incoming webhooks from third parties.
*   **Shared Use Cases**: All handlers will call the same use cases in the `/internal/app` layer, ensuring business logic is centralized and consistently applied.

### 2.4. Target Directory Structure

The new directory structure reflects the DDD approach and Clean Architecture principles. This phase will populate the `internal/app/account` and `internal/domain/account` directories.

```
/payment-service/
├── cmd/
│   └── api/
│       ├── server/                # Server setup, DI, and routing
│       │   ├── server.go
│       │   ├── dependencies.go
│       │   └── routes.go
│       └── main.go                # Lean application entry point
├── internal/
│   ├── app/                       # Application layer (use cases, services)
│   │   ├── account/               # <-- Populated in this phase
│   │   ├── organization/
│   │   └── billing/
│   ├── domain/                    # Domain layer (entities, value objects, core business logic)
│   │   ├── account/               # <-- Populated in this phase
│   │   ├── organization/
│   │   ├── billing/
│   │   ├── configuration/
│   │   └── notification/
│   ├── ports/                     # Presentation Layer (Handlers)
│   │   └── rest/
│   │       ├── openapi/
│   │       ├── dashboard/
│   │       └── callback/
│   └── infrastructure/            # Infrastructure layer (external concerns)
│       ├── database/              # PostgreSQL repository implementations
│       ├── provider/              # Adapters for the gateway packages
│       ├── cache/                 # Redis implementation
│       └── eventbus/              # Domain Event Bus implementation
├── pkg/
│   ├── gateways/                  # External, reusable gateway packages
│   │   ├── xendit/
│   │   │   ├── client.go
│   │   │   └── go.mod
│   │   └── nicepay/
│   │       ├── client.go
│   │       └── go.mod
│   └── ... (other shared libraries)
├── api/                           # API definitions (e.g., OpenAPI/Swagger specs)
├── db/
│   └── migrations/                # Database migration .sql files
├── docs/                          # Project documentation
└── scripts/                       # Scripts for build, deployment, etc.
```

## 3. Key Architectural Decisions (Recap from Phase 1)

### 3.1. Manual Dependency Injection

To maintain full control over the application's startup process and avoid external dependencies, **manual dependency injection** will be used. All dependencies will be constructed and "wired" together in the `/cmd/api/server` package.

### 3.2. Externalized Gateway Packages

All third-party payment provider integrations (e.g., Xendit, NicePay) will be built as **standalone, reusable Go packages** located in `/pkg/gateways`.

### 3.3. Internationalization (i18n)

The application will support multiple languages, starting with English (en) and Indonesian (id). This will impact user-facing strings in API responses, validation messages, and notifications.

## 4. Standardized Libraries (Recap from Phase 1)

The following libraries will be standardized across the project:

*   **Database Driver:** `pgx/v5`
*   **SQL Code Generator:** `sqlc`
*   **HTTP Framework:** `Gin`
*   **Logger:** `Zerolog`
*   **Configuration:** `Koanf`
*   **HTTP Client:** `Resty v2`
*   **Message Broker:** `go-rabbitmq`
*   **Integration Testing:** `testcontainers-go`
*   **Database Migrations:** `golang-migrate/migrate`
*   **Error Reporting:** `sentry-go`
*   **Internationalization:** `go-i18n`

## 5. Coding Conventions & Guidelines (Recap from Phase 1)

Adherence to these guidelines is mandatory for all contributors to ensure a high-quality, consistent, and maintainable codebase.

### 5.1. General Principles & Idiomatic Go
*   **Rule 1.1 (Simplicity - KISS):** Prefer simple, clear solutions over complex ones.
*   **Rule 1.2 (Effective Go):** Follow the principles outlined in the official [Effective Go](https://go.dev/doc/effective_go) document.
*   **Rule 1.3 (Package Naming):** All package names MUST be short-lowercase.
*   **Rule 1.4 (Variable Naming):** All variable names MUST be `camelCase`.
*   **Rule 1.5 (Interfaces):** Define interfaces to decouple components.
*   **Rule 1.6 (Formatting):** All generated Go code MUST be formatted with `gofmt`.

### 5.2. Project Structure & Clean Architecture
*   **Rule 2.1 (Strict Layering):** The dependency direction `API` -> `Application` -> `Domain` MUST be strictly enforced.
*   **Rule 2.2 (Domain Purity):** The `internal/domain` package MUST NOT import any other project packages.
*   **Rule 2.3 (Thin Handlers):** API handlers (`internal/ports/rest`) MUST only parse requests, call a single application service method, and generate a standardized response.

### 5.3. Error Management Protocol
*   **Rule 3.1 (Error Wrapping):** Errors MUST be wrapped with context using `fmt.Errorf("package.function: %w", err)`.
*   **Rule 3.2 (Custom Domain Errors):** Use custom error variables for predictable business rule failures.
*   **Rule 3.3 (Centralized HTTP Error Mapping):** A dedicated Gin error-handling middleware is the only place where errors are translated into HTTP status codes.
*   **Rule 3.4 (Sentry for Unexpected Errors):** Use `webapi.ErrorWithSentry(c, err)` for all 5xx internal server errors.

### 5.4. Standardized API Responses (webapi package)
*   **Rule 4.1 (Mandatory Helpers):** All HTTP responses MUST be generated exclusively using helper functions from the `internal/ports/rest/webapi` package.
*   **Rule 4.2 (Success Structure):** The mandatory JSON structure for a successful response is: `{"status": "success", "data": { /* Actual response data */ }, "correlationId": "UUID_OF_REQUEST"}`.
*   **Rule 4.3 (Error Structure):** The mandatory JSON structure for an error response is: `{"status": "error", "error": {"code": "UNIQUE_ERROR_CODE", "message": "Human-readable error description."}, "correlationId": "UUID_OF_REQUEST"}`.
*   **Rule 4.4 (Helper Signatures and Usage):** Adhere to the defined signatures for `webapi.Success`, `webapi.SuccessPaginated`, `webapi.Error`, and `webapi.ErrorWithSentry`.

### 5.5. Logging Best Practices (zerolog)
*   **Rule 5.1 (Structured Logging):** All logging MUST be done using the global `zerolog` instance.
*   **Rule 5.2 (Contextual Logging):** Every log statement MUST include the `trace_id` from the context.
*   **Rule 5.3 (Error Logging):** When logging an error, the error object MUST be included using `.Err(err)`.

### 5.6. Concurrency and Graceful Shutdown
*   **Rule 6.1 (Panic Safety):** Any new goroutine MUST use a `defer...recover()` block, logging any recovered panic to Sentry.
*   **Rule 6.2 (Graceful Termination):** Any long-running goroutine MUST accept a `context.Context` and listen on `ctx.Done()`.
*   **Rule 6.3 (Server Shutdown):** `main.go` must implement graceful shutdown for the HTTP server.

### 5.7. Gin Middleware
*   **Rule 7.1 (Tracing Middleware):** MUST be the first middleware, generating a `trace_id`.
*   **Rule 7.2 (Logger Middleware):** MUST log request and response details.
*   **Rule 7.3 (Error Middleware):** MUST be the last middleware to centralize error handling.

### 5.8. Database Access (sqlc with pgx/v5)
*   **Rule 8.1 (SQL Files):** All SQL queries MUST be defined in `.sql` files.
*   **Rule 8.2 (sqlc.yaml Configuration):** The `sqlc.yaml` file MUST be configured to use `sql_package: "pgx/v5"` and appropriate type overrides.
*   **Rule 8.3 (Type Conversion):** All conversions between domain types and `pgtype` MUST be handled in a dedicated `internal/infrastructure/database/converters` package.
*   **Rule 8.4 (Transactions):** Repository methods that perform writes MUST accept `pgx.Tx` as a parameter.

### 5.9. Testing and CI/CD Strategy
*   **Rule 9.1 (Containerized Testing):** All integration and end-to-end tests that require external services (like a database or Redis) MUST use `testcontainers-go`.
*   **Rule 9.2 (Seeding Data for Tests):** For every new `.up.sql` migration file, a corresponding test data seed file MUST be created.
*   **Rule 9.3 (Test Coverage):** API handlers, application services, and repositories MUST have dedicated test files.
*   **Rule 9.4 (CI/CD Gate):** A pull request MUST NOT be allowed to merge into the main branch unless all tests (`go test ./...`) pass.

### 5.10. Internationalization (i18n)
*   **Rule 10.1 (Localization Support):** The application MUST support English (en) and Indonesian (id) locales.

## 6. Domain Event Mechanism

This section outlines the design and implementation of a simple, maintainable, and extensible in-process domain event mechanism to facilitate communication between bounded contexts.

### 6.1. Core Concepts & Definitions

#### 6.1.1. Domain Event
A `Domain Event` is something that happened in the domain that other parts of the domain (or other bounded contexts) care about. It should be immutable and represent a past occurrence.

**Contract/Interface for a Domain Event:**
A simple interface `DomainEvent` will be defined. All domain events will implement this interface.

```go
// internal/pkg/event/event.go
package event

import "time"

// DomainEvent is the interface that all domain events must implement.
type DomainEvent interface {
	EventName() string
	OccurredAt() time.Time
	AggregateID() string // ID of the aggregate that produced the event
	// Add other common fields as needed, e.g., CorrelationID, UserID
}

// BaseEvent provides common fields for domain events.
type BaseEvent struct {
	Name      string    `json:"eventName"`
	Timestamp time.Time `json:"occurredAt"`
	ID        string    `json:"aggregateId"`
}

func (b BaseEvent) EventName() string {
	return b.Name
}

func (b BaseEvent) OccurredAt() time.Time {
	return b.Timestamp
}

func (b BaseEvent) AggregateID() string {
	return b.ID
}
```

**Example Domain Event (for `account` context):**
```go
// internal/domain/account/events.go
package account

import (
	"time"
	"github.com/your-org/payment-service-v2/internal/pkg/event" // Replace with actual module path
)

// UserRegisteredEvent is a domain event published when a new user registers.
type UserRegisteredEvent struct {
	event.BaseEvent
	UserID string `json:"userId"`
	Email  string `json:"email"`
	Name   string `json:"name"`
}

// NewUserRegisteredEvent creates a new UserRegisteredEvent.
func NewUserRegisteredEvent(userID, email, name string) UserRegisteredEvent {
	return UserRegisteredEvent{
		BaseEvent: event.BaseEvent{
			Name:      "UserRegistered",
			Timestamp: time.Now(),
			ID:        userID,
		},
		UserID: userID,
		Email:  email,
		Name:   name,
	}
}
```

#### 6.1.2. Event Publisher
The component responsible for publishing domain events. In a simple in-process model, this will be an in-memory event bus.

**Contract/Interface for Event Publisher:**
```go
// internal/pkg/event/publisher.go
package event

import "context"

// EventPublisher defines the interface for publishing domain events.
type EventPublisher interface {
	Publish(ctx context.Context, event DomainEvent) error
}
```

#### 6.1.3. Event Subscriber/Handler
The component responsible for listening to and reacting to domain events.

**Contract/Interface for Event Handler:**
```go
// internal/pkg/event/handler.go
package event

import "context"

// EventHandler defines the interface for handling a specific type of domain event.
type EventHandler interface {
	Handle(ctx context.Context, event DomainEvent) error
	Handles() string // Returns the name of the event this handler handles (e.g., "UserRegistered")
}
```

### 6.2. In-Process Event Bus Implementation

A simple, in-memory event bus will be implemented. This is easy to start with and can be replaced by a message broker (like RabbitMQ) later without changing the `DomainEvent` or `EventPublisher` interfaces.

```go
// internal/infrastructure/eventbus/in_memory_bus.go
package eventbus

import (
	"context"
	"fmt"
	"sync"

	"github.com/rs/zerolog/log"
	"github.com/your-org/payment-service-v2/internal/pkg/event" // Replace with actual module path
)

// InMemoryEventBus is a simple in-memory implementation of an EventPublisher.
type InMemoryEventBus struct {
	handlers map[string][]event.EventHandler
	mu       sync.RWMutex
}

// NewInMemoryEventBus creates a new InMemoryEventBus.
func NewInMemoryEventBus() *InMemoryEventBus {
	return &InMemoryEventBus{
		handlers: make(map[string][]event.EventHandler),
	}
}

// Subscribe registers an EventHandler for a specific event type.
func (b *InMemoryEventBus) Subscribe(handler event.EventHandler) {
	b.mu.Lock()
	defer b.mu.Unlock()
	eventName := handler.Handles()
	b.handlers[eventName] = append(b.handlers[eventName], handler)
	log.Info().Msgf("Subscribed handler for event: %s", eventName)
}

// Publish dispatches a domain event to all registered handlers.
func (b *InMemoryEventBus) Publish(ctx context.Context, evt event.DomainEvent) error {
	b.mu.RLock()
	handlers := b.handlers[evt.EventName()]
	b.mu.RUnlock()

	if len(handlers) == 0 {
		log.Warn().Msgf("No handlers registered for event: %s", evt.EventName())
		return nil
	}

	var wg sync.WaitGroup
	var errs []error
	for _, handler := range handlers {
		wg.Add(1)
		go func(h event.EventHandler) {
			defer wg.Done()
			defer func() {
				if r := recover(); r != nil {
					log.Error().Interface("panic", r).Msgf("Handler for %s panicked", evt.EventName())
					errs = append(errs, fmt.Errorf("handler panicked: %v", r))
				}
			}()

			log.Info().Msgf("Dispatching event %s to handler %T", evt.EventName(), h)
			if err := h.Handle(ctx, evt); err != nil {
				log.Error().Err(err).Msgf("Handler for %s failed", evt.EventName())
				errs = append(errs, err)
			}
		}(handler)
	}
	wg.Wait()

	if len(errs) > 0 {
		return fmt.Errorf("one or more handlers failed for event %s: %v", evt.EventName(), errs)
	}
	return nil
}
```

### 6.3. Benefits of this Approach

*   **Decoupling:** Bounded contexts communicate via events, reducing direct dependencies.
*   **Simplicity:** In-memory bus is easy to implement and test.
*   **Extensibility:** The `EventPublisher` interface allows swapping the `InMemoryEventBus` with a RabbitMQ or Kafka implementation later without changing the core domain logic.
*   **Auditability:** Events provide a clear log of what happened in the system.

## 7. Database Schemas for Phase 2

## 6. Database Schemas for Phase 2

This phase will primarily interact with and refine the `account` related schemas introduced in Phase 1.

### 6.1. Authentication and User Management Schemas (`account` Bounded Context)

#### `users` Table:
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL, -- Stores securely hashed passwords
    role_id INT NOT NULL,
    last_login_at BIGINT,
    status BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);
```
*   **`password_hash`**: Replaces insecure `password` field. Will store hashes generated by a strong, one-way algorithm (e.g., bcrypt or Argon2).
*   **`id`**: `UUID` with `gen_random_uuid()` default.

#### `roles` Table:
```sql
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);
```
*   **`id`**: `UUID` with `gen_random_uuid()` default.

#### `role_permissions` Table:
```sql
CREATE TABLE role_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role_id UUID NOT NULL REFERENCES roles(id),
    menu_id INT NOT NULL, -- Assuming 'menus' table exists for menu items
    permissions JSONB NOT NULL, -- Stores permissions as JSON (e.g., {"view": true, "create": false})
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(role_id, menu_id)
);
```
*   **`id`**: `UUID` with `gen_random_uuid()` default.
*   **`role_id`**: References `roles(id)` which is `UUID`.

#### `user_companies` Table (for user-company relationships):
```sql
CREATE TABLE user_companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    company_id UUID NOT NULL, -- Assuming 'companies' table exists (will be in organization context)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, company_id)
);
```
*   **`id`**: `UUID` with `gen_random_uuid()` default.
*   **`user_id`**: References `users(id)` which is `UUID`.
*   **`company_id`**: Will eventually reference `companies(id)` from the `organization` bounded context.

## 7. Detailed Tasks for Phase 2

### 7.1. Account Domain & Repository Implementation
*   **Task 7.1.1: Define Account Domain Entities**
    *   Create `internal/domain/account/user.go`, `role.go`, `permission.go`.
    *   Define `User` struct with `PasswordHash` field.
    *   Implement `SetPassword` and `CheckPassword` methods for `User` using `golang.org/x/crypto/argon2` (or `bcrypt`).
*   **Task 7.1.2: Update Database Migrations**
    *   Create a new migration `db/migrations/000002_account_tables.up.sql` to ensure `users`, `roles`, `role_permissions`, and `user_companies` tables are correctly defined with UUIDs and `password_hash`. If these tables were already created in Phase 1, this migration should contain `ALTER TABLE` statements to modify them as needed (e.g., add `password_hash`, change `id` to `UUID` if not already).
    *   Create corresponding `000002_account_tables.down.sql`.
*   **Task 7.1.3: Implement Account Repository**
    *   Create `internal/infrastructure/database/account/repository.go`.
    *   Implement `AccountRepository` interface (e.g., `CreateUser`, `GetUserByEmail`, `GetRolesForUser`, `GetPermissionsForRole`) using `sqlc`-generated code.
    *   Ensure all `pgtype` conversions are handled by `internal/infrastructure/database/converters`.
*   **Task 7.1.4: Implement Password Hashing Utility**
    *   Create `internal/pkg/password/password.go` to encapsulate password hashing and verification logic (e.g., `HashPassword`, `CheckPassword`).

### 7.2. Domain Event Mechanism Implementation
*   **Task 7.2.1: Define Domain Event Interfaces and Base Struct**
    *   Create `internal/pkg/event/event.go` with `DomainEvent` interface and `BaseEvent` struct.
    *   Create `internal/pkg/event/publisher.go` with `EventPublisher` interface.
    *   Create `internal/pkg/event/handler.go` with `EventHandler` interface.
*   **Task 7.2.2: Implement In-Memory Event Bus**
    *   Create `internal/infrastructure/eventbus/in_memory_bus.go` with `InMemoryEventBus` implementation.
*   **Task 7.2.3: Define Account-Specific Domain Events**
    *   Create `internal/domain/account/events.go` and define `UserRegisteredEvent`.

### 7.3. Account Application Use Cases
*   **Task 7.3.1: Implement Account Service**
    *   Create `internal/app/account/service.go`.
    *   Implement `AuthService` with methods for:
        *   `RegisterUser(name, email, password)`: Hashes password using `internal/pkg/password` and saves to DB. **After successful registration, publish a `UserRegisteredEvent` using the injected `EventPublisher`.**
        *   `Login(email, password)`: Verifies password, generates JWT and refresh token.
        *   `RefreshToken(refreshToken)`: Validates refresh token, issues new JWT and refresh token.
*   **Task 7.3.2: Integrate Redis for Session Management**
    *   Implement `internal/infrastructure/cache/redis.go` for Redis client initialization (if not already done in Phase 1).
    *   Modify `AuthService` to store refresh tokens and JWT `jti` (JWT ID) in Redis for session management and blacklisting.
*   **Task 7.3.3: Create Sample Event Handler**
    *   Create a sample `NotificationEventHandler` (e.g., in `internal/app/notification/handlers.go`) that implements `event.EventHandler` and subscribes to `UserRegisteredEvent` (e.g., by logging a message).

### 7.4. Account Presentation Layer (API)
*   **Task 7.4.1: Implement Auth Handlers**
    *   Create `internal/ports/rest/openapi/auth_handler.go`.
    *   Implement handlers for `/auth/register`, `/auth/login`, `/auth/refresh`, `/auth/logout`.
    *   Ensure handlers are "thin" (Rule 2.3), only parsing requests and calling `AuthService` methods.
*   **Task 7.4.2: Implement Authentication Middleware**
    *   Create `internal/ports/rest/middleware/auth.go`.
    *   Implement `AuthMiddleware` to:
        *   Validate JWT signature and expiration.
        *   Check JWT `jti` against Redis blacklist.
        *   Extract `userID`, `roleID`, and `permissions` from JWT claims and store in Gin context.
*   **Task 7.4.3: Update Main Application Wiring**
    *   Modify `cmd/api/server/dependencies.go` to wire up the `AccountRepository`, `AuthService`, `InMemoryEventBus`, and register event handlers.
    *   Modify `cmd/api/server/routes.go` to define the new `/auth` endpoints and apply `AuthMiddleware` to protected routes.

### 7.5. Testing and Validation
*   **Task 7.5.1: Update Seed Data**
    *   Create `testdata/seeds/000002_seed_account_data.sql` with sample `users`, `roles`, and `role_permissions` data.
*   **Task 7.5.2: Implement Unit Tests**
    *   Write unit tests for `internal/pkg/password/password.go`.
    *   Write unit tests for `internal/app/account/service.go` (mocking repository, Redis, and `EventPublisher`).
    *   Write unit tests for `internal/infrastructure/eventbus/in_memory_bus.go`.
*   **Task 7.5.3: Implement Integration Tests**
    *   Write integration tests for `internal/infrastructure/database/account/repository.go` using `testcontainers-go` for PostgreSQL.
    *   Write integration tests for `internal/app/account/service.go` using `testcontainers-go` for PostgreSQL and Redis.
*   **Task 7.5.4: Implement End-to-End Tests**
    *   Write E2E tests for `/auth` endpoints using `SetupTestServer()` from Phase 1.
    *   Test successful registration, login, refresh, and logout flows.
    *   Test invalid credentials, expired tokens, and blacklisted tokens.
    *   **Verify that `UserRegisteredEvent` is published and handled correctly in E2E tests.**
*   **Task 7.5.5: Update Postman Collection**
    *   Create `docs/postman/collections/auth.postman_collection.json` with requests for all new authentication endpoints.
    *   Ensure post-request scripts handle token extraction and environment updates.

## 8. Phase 2 Deliverables

*   Fully implemented `account` bounded context with secure authentication and basic user management.
*   **Implemented a simple in-process Domain Event Mechanism.**
*   Updated database schema for `users`, `roles`, `role_permissions`, and `user_companies`.
*   Working JWT-based authentication with Redis-backed session management.
*   Comprehensive unit, integration, and E2E tests for the `account` context, **including verification of domain event publishing and handling.**
*   Updated Postman collection for authentication endpoints.
*   All code adhering to the defined coding guidelines.

## 9. Phase 2 Diagram:

```mermaid
graph TD
    subgraph "Phase 2: Account Bounded Context Implementation"
        A[7.1. Account Domain & Repository] --> B(7.2. Account Application Use Cases)
        B --> C(7.3. Account Presentation Layer)
        C --> D(7.4. Testing and Validation)
        A --> D
        B --> D
        C --> D
    end

    subgraph "7.1. Account Domain & Repository"
        A1[7.1.1: Define Account Domain Entities]
        A2[7.1.2: Update Database Migrations]
        A3[7.1.3: Implement Account Repository]
        A4[7.1.4: Implement Password Hashing Utility]
    end

    subgraph "7.2. Account Application Use Cases"
        B1[7.2.1: Implement Account Service]
        B2[7.2.2: Integrate Redis for Session Management]
    end

    subgraph "7.3. Account Presentation Layer (API)"
        C1[7.3.1: Implement Auth Handlers]
        C2[7.3.2: Implement Authentication Middleware]
        C3[7.3.3: Update Main Application Wiring]
    end

    subgraph "7.4. Testing and Validation"
        D1[7.4.1: Update Seed Data]
        D2[7.4.2: Implement Unit Tests]
        D3[7.4.3: Implement Integration Tests]
        D4[7.4.4: Implement End-to-End Tests]
        D5[7.4.5: Update Postman Collection]
    end

    style A1 fill:#f9f,stroke:#333,stroke-width:2px
    style A2 fill:#f9f,stroke:#333,stroke-width:2px
    style A3 fill:#f9f,stroke:#333,stroke-width:2px
    style A4 fill:#f9f,stroke:#333,stroke-width:2px

    style B1 fill:#bbf,stroke:#333,stroke-width:2px
    style B2 fill:#bbf,stroke:#333,stroke-width:2px

    style C1 fill:#ccf,stroke:#333,stroke-width:2px
    style C2 fill:#ccf,stroke:#333,stroke-width:2px
    style C3 fill:#ccf,stroke:#333,stroke-width:2px

    style D1 fill:#ddf,stroke:#333,stroke-width:2px
    style D2 fill:#ddf,stroke:#333,stroke-width:2px
    style D3 fill:#ddf,stroke:#333,stroke-width:2px
    style D4 fill:#ddf,stroke:#333,stroke-width:2px
    style D5 fill:#ddf,stroke:#333,stroke-width:2px

## 10. Phase Completion Checklist

This checklist serves as a verification tool to ensure all tasks, requirements, rules, and guidelines for Phase 2 have been successfully completed and adhered to. Before marking this phase as complete, all items below must be checked off.

### 10.1. General Completion & Quality
*   [ ] All tasks outlined in Section 7 ("Detailed Tasks for Phase 2") have been implemented.
*   [ ] The project builds successfully without errors (`go build ./...`).
*   [ ] All tests pass (`go test ./...`).
*   [ ] Code has been formatted with `gofmt` (`gofmt -s -w .`).
*   [ ] No new warnings or errors are introduced by linters (e.g., `golangci-lint run`).
*   [ ] All dependencies are properly managed (`go mod tidy`).

### 10.2. Architectural Adherence
*   [ ] The `internal/app/account` and `internal/domain/account` directories are populated as per the target structure.
*   [ ] Dependencies within the `account` context strictly follow the Clean Architecture rule.
*   [ ] Domain Event Mechanism (Section 6) is implemented and integrated.
*   [ ] `AuthService` publishes `UserRegisteredEvent` correctly.
*   [ ] Sample `NotificationEventHandler` is implemented and subscribes to `UserRegisteredEvent`.

### 10.3. Core Functionality Verification
*   [ ] Secure password hashing (`argon2` or `bcrypt`) is implemented and used for user registration and login.
*   [ ] JWT-based authentication is functional (token generation, validation, refresh).
*   [ ] Redis is correctly integrated for JWT blacklisting and refresh token storage.
*   [ ] API endpoints for `/auth/register`, `/auth/login`, `/auth/refresh`, `/auth/logout` are functional.
*   [ ] Authentication and authorization middleware are correctly applied and functional.

### 10.4. Testing & Validation
*   [ ] All unit tests for `account` services, password utility, and event bus are passing.
*   [ ] All integration tests for `account` repositories and services are passing.
*   [ ] All E2E tests for `/auth` endpoints are passing, covering success and failure scenarios.
*   [ ] E2E tests verify that `UserRegisteredEvent` is published and handled.
*   [ ] Postman collection for authentication endpoints is updated and functional.
*   [ ] Seed data for `account` tables is correctly applied in tests.

### 10.5. Coding Guidelines Compliance (Self-Assessment)
*   [ ] **Rule 1 (General):** `gofmt`, `camelCase`, short-lowercase package names, interface-based design are followed.
*   [ ] **Rule 2 (Architecture):** `API` -> `Application` -> `Domain` dependency rule and thin handlers are enforced.
*   [ ] **Rule 3 (Error Handling):** Error wrapping, custom errors, centralized mapping, and Sentry integration are used.
*   [ ] **Rule 4 (API Responses):** `webapi` helpers are used for all responses, and error messages are localized.
*   [ ] **Rule 5 (Logging):** Structured, contextual logging with `zerolog` is implemented.
*   [ ] **Rule 6 (Concurrency):** Panic safety and graceful termination are considered.
*   [ ] **Rule 7 (Middleware):** The exact middleware order is followed.
*   [ ] **Rule 8 (Database):** `.sql` files for `sqlc`, `pgx/v5` configuration, centralized type converters, and transactional repositories are used.
*   [ ] **Rule 9 (Testing & CI/CD):** `testcontainers-go`, DDL/DML separation, and test coverage are adhered to.
*   [ ] **Rule 10 (Internationalization):** Localization support is correctly implemented.
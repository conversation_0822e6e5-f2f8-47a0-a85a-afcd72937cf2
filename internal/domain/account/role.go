package account

import (
	"errors"
	"time"

	"github.com/google/uuid"
)

// Role represents a role in the system
type Role struct {
	id          RoleID
	name        string
	description string
	isActive    bool
	createdAt   time.Time
	updatedAt   time.Time
	permissions []Permission
}

// RoleID is a value object representing a role identifier
type RoleID struct {
	value uuid.UUID
}

// NewRoleID creates a new RoleID
func NewRoleID() RoleID {
	return RoleID{value: uuid.New()}
}

// RoleIDFromString creates a RoleID from a string
func RoleIDFromString(s string) (RoleID, error) {
	id, err := uuid.Parse(s)
	if err != nil {
		return RoleID{}, errors.New("invalid role ID format")
	}
	return RoleID{value: id}, nil
}

// String returns the string representation of RoleID
func (r RoleID) String() string {
	return r.value.String()
}

// UUID returns the underlying UUID
func (r RoleID) UUID() uuid.UUID {
	return r.value
}

// NewRole creates a new Role entity
func NewRole(name, description string) *Role {
	now := time.Now()
	return &Role{
		id:          NewRoleID(),
		name:        name,
		description: description,
		isActive:    true,
		createdAt:   now,
		updatedAt:   now,
		permissions: make([]Permission, 0),
	}
}

// NewRoleWithID creates a Role entity with all fields specified (for repository use)
func NewRoleWithID(id RoleID, name, description string, isActive bool, createdAt, updatedAt time.Time) *Role {
	return &Role{
		id:          id,
		name:        name,
		description: description,
		isActive:    isActive,
		createdAt:   createdAt,
		updatedAt:   updatedAt,
		permissions: make([]Permission, 0),
	}
}

// ID returns the role's ID
func (r *Role) ID() RoleID {
	return r.id
}

// Name returns the role's name
func (r *Role) Name() string {
	return r.name
}

// Description returns the role's description
func (r *Role) Description() string {
	return r.description
}

// IsActive returns whether the role is active
func (r *Role) IsActive() bool {
	return r.isActive
}

// CreatedAt returns when the role was created
func (r *Role) CreatedAt() time.Time {
	return r.createdAt
}

// UpdatedAt returns when the role was last updated
func (r *Role) UpdatedAt() time.Time {
	return r.updatedAt
}

// Permissions returns the role's permissions
func (r *Role) Permissions() []Permission {
	return r.permissions
}

// UpdateName updates the role's name
func (r *Role) UpdateName(newName string) {
	r.name = newName
	r.updatedAt = time.Now()
}

// UpdateDescription updates the role's description
func (r *Role) UpdateDescription(newDescription string) {
	r.description = newDescription
	r.updatedAt = time.Now()
}

// Deactivate deactivates the role
func (r *Role) Deactivate() {
	r.isActive = false
	r.updatedAt = time.Now()
}

// Activate activates the role
func (r *Role) Activate() {
	r.isActive = true
	r.updatedAt = time.Now()
}

// AddPermission adds a permission to the role
func (r *Role) AddPermission(permission Permission) {
	// Check if permission already exists
	for _, existingPermission := range r.permissions {
		if existingPermission.ID() == permission.ID() {
			return // Permission already exists
		}
	}
	r.permissions = append(r.permissions, permission)
	r.updatedAt = time.Now()
}

// RemovePermission removes a permission from the role
func (r *Role) RemovePermission(permissionID PermissionID) {
	for i, permission := range r.permissions {
		if permission.ID() == permissionID {
			r.permissions = append(r.permissions[:i], r.permissions[i+1:]...)
			r.updatedAt = time.Now()
			break
		}
	}
}

// HasPermission checks if the role has a specific permission
func (r *Role) HasPermission(permission Permission) bool {
	for _, p := range r.permissions {
		if p.ID() == permission.ID() {
			return true
		}
	}
	return false
}

// HasPermissionByID checks if the role has a specific permission by ID
func (r *Role) HasPermissionByID(permissionID PermissionID) bool {
	for _, p := range r.permissions {
		if p.ID() == permissionID {
			return true
		}
	}
	return false
}

// HasPermissionByName checks if the role has a specific permission by name
func (r *Role) HasPermissionByName(permissionName string) bool {
	for _, p := range r.permissions {
		if p.Name() == permissionName {
			return true
		}
	}
	return false
}

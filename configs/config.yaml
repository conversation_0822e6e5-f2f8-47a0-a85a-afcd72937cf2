# Payment Gateway Configuration
# This file contains the default configuration for the payment gateway service
# Environment variables can override these values using the pattern: PAYMENT_GATEWAY_<SECTION>_<KEY>
# Example: PAYMENT_GATEWAY_SERVER_PORT=8080

server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"
  shutdown_timeout: "30s"
  trusted_proxies: []
  cors:
    allowed_origins: ["*"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["*"]
    exposed_headers: ["X-Correlation-ID"]
    allow_credentials: true
    max_age: 86400

logger:
  level: "info"  # trace, debug, info, warn, error, fatal, panic
  format: "console"  # console, json
  caller: true
  timestamp: true
  color: true

database:
  host: "localhost"
  port: 5432
  name: "payment_gateway"
  user: "postgres"
  password: "postgres"
  ssl_mode: "disable"  # disable, require, verify-ca, verify-full
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "5m"
  conn_max_idle_time: "5m"
  migration_path: "file://db/migrations"

redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0
  pool_size: 10
  min_idle_conns: 5
  dial_timeout: "5s"
  read_timeout: "3s"
  write_timeout: "3s"
  pool_timeout: "4s"
  idle_timeout: "5m"

jwt:
  secret: "your-super-secret-jwt-key-change-this-in-production"
  issuer: "payment-gateway"
  audience: "payment-gateway-users"
  access_token_duration: "15m"
  refresh_token_duration: "24h"

sentry:
  dsn: ""
  environment: "development"
  debug: false
  sample_rate: 1.0
  traces_sample_rate: 0.1

i18n:
  default_language: "en"
  supported_languages: ["en", "id"]
  fallback_language: "en"

external_services:
  xendit:
    base_url: "https://api.xendit.co"
    secret_key: ""
    webhook_token: ""
    timeout: "30s"
  
  nicepay:
    base_url: "https://api.nicepay.co.id"
    merchant_id: ""
    secret_key: ""
    timeout: "30s"

rate_limiting:
  enabled: true
  requests_per_minute: 100
  burst: 10

monitoring:
  metrics_enabled: true
  health_check_interval: "30s"
  
security:
  bcrypt_cost: 12
  max_login_attempts: 5
  lockout_duration: "15m"

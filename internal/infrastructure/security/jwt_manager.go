package security

import (
	"context"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"

	"github.com/wongpinter/payment-gateway/internal/domain/account"
)

// JWTManager handles JWT token generation, validation, and blacklisting
type JWTManager struct {
	secret          []byte
	accessTokenTTL  time.Duration
	refreshTokenTTL time.Duration
	blacklist       *JWTBlacklist
}

// JWTManagerConfig holds configuration for JWTManager
type JWTManagerConfig struct {
	Secret          string
	AccessTokenTTL  time.Duration
	RefreshTokenTTL time.Duration
}

// NewJWTManager creates a new JWTManager
func NewJWTManager(config JWTManagerConfig, blacklist *JWTBlacklist) *JWTManager {
	return &JWTManager{
		secret:          []byte(config.Secret),
		accessTokenTTL:  config.AccessTokenTTL,
		refreshTokenTTL: config.RefreshTokenTTL,
		blacklist:       blacklist,
	}
}

// AccessTokenClaims represents the claims in an access token
type AccessTokenClaims struct {
	UserID string   `json:"user_id"`
	Email  string   `json:"email"`
	Name   string   `json:"name"`
	Roles  []string `json:"roles"`
	jwt.RegisteredClaims
}

// RefreshTokenClaims represents the claims in a refresh token
type RefreshTokenClaims struct {
	UserID string `json:"user_id"`
	jwt.RegisteredClaims
}

// GenerateAccessToken generates a new access token
func (m *JWTManager) GenerateAccessToken(ctx context.Context, user *account.User, roles []account.Role) (string, time.Time, error) {
	now := time.Now()
	expiresAt := now.Add(m.accessTokenTTL)
	tokenID := uuid.New().String()

	// Prepare role names
	roleNames := make([]string, len(roles))
	for i, role := range roles {
		roleNames[i] = role.Name()
	}

	claims := AccessTokenClaims{
		UserID: user.ID().String(),
		Email:  user.Email().String(),
		Name:   user.Name(),
		Roles:  roleNames,
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        tokenID,
			Subject:   user.ID().String(),
			IssuedAt:  jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "payment-gateway",
			Audience:  []string{"payment-gateway-api"},
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(m.secret)
	if err != nil {
		return "", time.Time{}, fmt.Errorf("jwt_manager.GenerateAccessToken: %w", err)
	}

	return tokenString, expiresAt, nil
}

// GenerateRefreshToken generates a new refresh token
func (m *JWTManager) GenerateRefreshToken(ctx context.Context, user *account.User) (string, time.Time, error) {
	now := time.Now()
	expiresAt := now.Add(m.refreshTokenTTL)
	tokenID := uuid.New().String()

	claims := RefreshTokenClaims{
		UserID: user.ID().String(),
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        tokenID,
			Subject:   user.ID().String(),
			IssuedAt:  jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "payment-gateway",
			Audience:  []string{"payment-gateway-refresh"},
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(m.secret)
	if err != nil {
		return "", time.Time{}, fmt.Errorf("jwt_manager.GenerateRefreshToken: %w", err)
	}

	return tokenString, expiresAt, nil
}

// ValidateAccessToken validates an access token and returns the claims
func (m *JWTManager) ValidateAccessToken(ctx context.Context, tokenString string) (*AccessTokenClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &AccessTokenClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return m.secret, nil
	})

	if err != nil {
		return nil, fmt.Errorf("jwt_manager.ValidateAccessToken: %w", err)
	}

	claims, ok := token.Claims.(*AccessTokenClaims)
	if !ok || !token.Valid {
		return nil, fmt.Errorf("jwt_manager.ValidateAccessToken: invalid token")
	}

	// Check if token is blacklisted
	if m.blacklist != nil {
		isBlacklisted, err := m.blacklist.IsTokenBlacklisted(ctx, claims.ID)
		if err != nil {
			return nil, fmt.Errorf("jwt_manager.ValidateAccessToken: %w", err)
		}
		if isBlacklisted {
			return nil, fmt.Errorf("jwt_manager.ValidateAccessToken: token is blacklisted")
		}

		// Check if user is blacklisted
		isUserBlacklisted, err := m.blacklist.IsUserBlacklisted(ctx, claims.UserID)
		if err != nil {
			return nil, fmt.Errorf("jwt_manager.ValidateAccessToken: %w", err)
		}
		if isUserBlacklisted {
			return nil, fmt.Errorf("jwt_manager.ValidateAccessToken: user tokens are blacklisted")
		}
	}

	return claims, nil
}

// ValidateRefreshToken validates a refresh token and returns the claims
func (m *JWTManager) ValidateRefreshToken(ctx context.Context, tokenString string) (*RefreshTokenClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &RefreshTokenClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return m.secret, nil
	})

	if err != nil {
		return nil, fmt.Errorf("jwt_manager.ValidateRefreshToken: %w", err)
	}

	claims, ok := token.Claims.(*RefreshTokenClaims)
	if !ok || !token.Valid {
		return nil, fmt.Errorf("jwt_manager.ValidateRefreshToken: invalid token")
	}

	// Check if token is blacklisted
	if m.blacklist != nil {
		isBlacklisted, err := m.blacklist.IsTokenBlacklisted(ctx, claims.ID)
		if err != nil {
			return nil, fmt.Errorf("jwt_manager.ValidateRefreshToken: %w", err)
		}
		if isBlacklisted {
			return nil, fmt.Errorf("jwt_manager.ValidateRefreshToken: token is blacklisted")
		}

		// Check if user is blacklisted
		isUserBlacklisted, err := m.blacklist.IsUserBlacklisted(ctx, claims.UserID)
		if err != nil {
			return nil, fmt.Errorf("jwt_manager.ValidateRefreshToken: %w", err)
		}
		if isUserBlacklisted {
			return nil, fmt.Errorf("jwt_manager.ValidateRefreshToken: user tokens are blacklisted")
		}
	}

	return claims, nil
}

// BlacklistToken adds a token to the blacklist
func (m *JWTManager) BlacklistToken(ctx context.Context, tokenString string) error {
	if m.blacklist == nil {
		return fmt.Errorf("jwt_manager.BlacklistToken: blacklist not configured")
	}

	// Parse token to get claims (without validation to handle expired tokens)
	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, &AccessTokenClaims{})
	if err != nil {
		return fmt.Errorf("jwt_manager.BlacklistToken: %w", err)
	}

	claims, ok := token.Claims.(*AccessTokenClaims)
	if !ok {
		return fmt.Errorf("jwt_manager.BlacklistToken: invalid token claims")
	}

	// Add to blacklist with original expiration time
	expiresAt := claims.ExpiresAt.Time
	return m.blacklist.BlacklistToken(ctx, claims.ID, expiresAt)
}

// BlacklistUserTokens blacklists all tokens for a user
func (m *JWTManager) BlacklistUserTokens(ctx context.Context, userID string, expiresAt time.Time) error {
	if m.blacklist == nil {
		return fmt.Errorf("jwt_manager.BlacklistUserTokens: blacklist not configured")
	}

	return m.blacklist.BlacklistUserTokens(ctx, userID, expiresAt)
}

// ExtractTokenID extracts the token ID from a token string without full validation
func (m *JWTManager) ExtractTokenID(tokenString string) (string, error) {
	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, &AccessTokenClaims{})
	if err != nil {
		return "", fmt.Errorf("jwt_manager.ExtractTokenID: %w", err)
	}

	claims, ok := token.Claims.(*AccessTokenClaims)
	if !ok {
		return "", fmt.Errorf("jwt_manager.ExtractTokenID: invalid token claims")
	}

	return claims.ID, nil
}

// GetTokenTTLs returns the configured TTLs for access and refresh tokens
func (m *JWTManager) GetTokenTTLs() (accessTTL, refreshTTL time.Duration) {
	return m.accessTokenTTL, m.refreshTokenTTL
}

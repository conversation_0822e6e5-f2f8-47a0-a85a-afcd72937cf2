-- Update account schema to match Clean Architecture domain model
-- This migration updates the account bounded context to support proper RBAC

-- Create permissions table
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    resource VARCHAR(255) NOT NULL,
    action VARCHAR(255) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(resource, action)
);

-- Update roles table to match domain model
ALTER TABLE roles 
    DROP COLUMN IF EXISTS code,
    DROP COLUMN IF EXISTS status,
    ADD COLUMN IF NOT EXISTS is_active BOOLEAN NOT NULL DEFAULT TRUE;

-- Update users table to remove single role_id (we'll use many-to-many)
ALTER TABLE users 
    DROP COLUMN IF EXISTS role_id,
    DROP COLUMN IF EXISTS status,
    ADD COLUMN IF NOT EXISTS is_active BOOLEAN NOT NULL DEFAULT TRUE;

-- Drop the old role_permissions table as it doesn't match our domain model
DROP TABLE IF EXISTS role_permissions;

-- Create new role_permissions junction table
CREATE TABLE role_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(role_id, permission_id)
);

-- Create user_roles junction table for many-to-many relationship
CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, role_id)
);

-- Create indexes for better performance
CREATE INDEX idx_permissions_name ON permissions(name) WHERE deleted_at IS NULL;
CREATE INDEX idx_permissions_resource ON permissions(resource) WHERE deleted_at IS NULL;
CREATE INDEX idx_permissions_action ON permissions(action) WHERE deleted_at IS NULL;
CREATE INDEX idx_permissions_is_active ON permissions(is_active) WHERE deleted_at IS NULL;

CREATE INDEX idx_roles_name ON roles(name) WHERE deleted_at IS NULL;
CREATE INDEX idx_roles_is_active ON roles(is_active) WHERE deleted_at IS NULL;

CREATE INDEX idx_users_email_active ON users(email) WHERE deleted_at IS NULL AND is_active = TRUE;
CREATE INDEX idx_users_is_active ON users(is_active) WHERE deleted_at IS NULL;

CREATE INDEX idx_role_permissions_role_id ON role_permissions(role_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_role_permissions_permission_id ON role_permissions(permission_id) WHERE deleted_at IS NULL;

CREATE INDEX idx_user_roles_user_id ON user_roles(user_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_user_roles_role_id ON user_roles(role_id) WHERE deleted_at IS NULL;

-- Create triggers for updated_at columns on new tables
CREATE TRIGGER update_permissions_updated_at BEFORE UPDATE ON permissions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_role_permissions_updated_at BEFORE UPDATE ON role_permissions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_roles_updated_at BEFORE UPDATE ON user_roles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default permissions for the payment gateway system
INSERT INTO permissions (id, name, description, resource, action, is_active) VALUES
    ('00000000-0000-0000-0000-000000000001', 'user.create', 'Create new users', 'user', 'create', TRUE),
    ('00000000-0000-0000-0000-000000000002', 'user.read', 'Read user information', 'user', 'read', TRUE),
    ('00000000-0000-0000-0000-000000000003', 'user.update', 'Update user information', 'user', 'update', TRUE),
    ('00000000-0000-0000-0000-000000000004', 'user.delete', 'Delete users', 'user', 'delete', TRUE),
    ('00000000-0000-0000-0000-000000000005', 'payment.create', 'Create new payments', 'payment', 'create', TRUE),
    ('00000000-0000-0000-0000-000000000006', 'payment.read', 'Read payment information', 'payment', 'read', TRUE),
    ('00000000-0000-0000-0000-000000000007', 'organization.create', 'Create new organizations', 'organization', 'create', TRUE),
    ('00000000-0000-0000-0000-000000000008', 'organization.read', 'Read organization information', 'organization', 'read', TRUE),
    ('00000000-0000-0000-0000-000000000009', 'role.create', 'Create new roles', 'role', 'create', TRUE),
    ('00000000-0000-0000-0000-000000000010', 'role.read', 'Read role information', 'role', 'read', TRUE),
    ('00000000-0000-0000-0000-000000000011', 'role.update', 'Update role information', 'role', 'update', TRUE),
    ('00000000-0000-0000-0000-000000000012', 'role.delete', 'Delete roles', 'role', 'delete', TRUE),
    ('00000000-0000-0000-0000-000000000013', 'permission.read', 'Read permission information', 'permission', 'read', TRUE);

-- Insert default roles
INSERT INTO roles (id, name, description, is_active) VALUES
    ('00000000-0000-0000-0000-000000000101', 'Super Admin', 'Full system access with all permissions', TRUE),
    ('00000000-0000-0000-0000-000000000102', 'Admin', 'Administrative access with most permissions', TRUE),
    ('00000000-0000-0000-0000-000000000103', 'User Manager', 'User management permissions', TRUE),
    ('00000000-0000-0000-0000-000000000104', 'Payment Manager', 'Payment management permissions', TRUE),
    ('00000000-0000-0000-0000-000000000105', 'Viewer', 'Read-only access to most resources', TRUE);

-- Assign permissions to roles
-- Super Admin gets all permissions
INSERT INTO role_permissions (role_id, permission_id) 
SELECT '00000000-0000-0000-0000-000000000101', id FROM permissions;

-- Admin gets most permissions (excluding user.delete)
INSERT INTO role_permissions (role_id, permission_id) 
SELECT '00000000-0000-0000-0000-000000000102', id FROM permissions 
WHERE name != 'user.delete';

-- User Manager gets user management permissions
INSERT INTO role_permissions (role_id, permission_id) VALUES
    ('00000000-0000-0000-0000-000000000103', '00000000-0000-0000-0000-000000000001'), -- user.create
    ('00000000-0000-0000-0000-000000000103', '00000000-0000-0000-0000-000000000002'), -- user.read
    ('00000000-0000-0000-0000-000000000103', '00000000-0000-0000-0000-000000000003'), -- user.update
    ('00000000-0000-0000-0000-000000000103', '00000000-0000-0000-0000-000000000010'), -- role.read
    ('00000000-0000-0000-0000-000000000103', '00000000-0000-0000-0000-000000000013'); -- permission.read

-- Payment Manager gets payment permissions
INSERT INTO role_permissions (role_id, permission_id) VALUES
    ('00000000-0000-0000-0000-000000000104', '00000000-0000-0000-0000-000000000005'), -- payment.create
    ('00000000-0000-0000-0000-000000000104', '00000000-0000-0000-0000-000000000006'), -- payment.read
    ('00000000-0000-0000-0000-000000000104', '00000000-0000-0000-0000-000000000002'); -- user.read

-- Viewer gets read permissions
INSERT INTO role_permissions (role_id, permission_id) VALUES
    ('00000000-0000-0000-0000-000000000105', '00000000-0000-0000-0000-000000000002'), -- user.read
    ('00000000-0000-0000-0000-000000000105', '00000000-0000-0000-0000-000000000006'), -- payment.read
    ('00000000-0000-0000-0000-000000000105', '00000000-0000-0000-0000-000000000008'), -- organization.read
    ('00000000-0000-0000-0000-000000000105', '00000000-0000-0000-0000-000000000010'), -- role.read
    ('00000000-0000-0000-0000-000000000105', '00000000-0000-0000-0000-000000000013'); -- permission.read

package events

import (
	"context"
	"fmt"

	"github.com/rs/zerolog"

	"github.com/wongpinter/payment-gateway/internal/domain/account"
)

// InMemoryEventPublisher is a simple in-memory event publisher for development/testing
type InMemoryEventPublisher struct {
	logger *zerolog.Logger
}

// NewInMemoryEventPublisher creates a new in-memory event publisher
func NewInMemoryEventPublisher(logger *zerolog.Logger) *InMemoryEventPublisher {
	return &InMemoryEventPublisher{
		logger: logger,
	}
}

// Publish publishes an event (currently just logs it)
func (p *InMemoryEventPublisher) Publish(ctx context.Context, event account.DomainEvent) error {
	// For now, just log the event
	// In a real implementation, this would publish to a message broker like RabbitMQ
	p.logger.Info().
		Str("event_id", event.EventID()).
		Str("event_name", event.EventName()).
		Time("event_timestamp", event.EventTimestamp()).
		Str("aggregate_id", event.AggregateID()).
		Interface("event_data", event).
		Msg("Domain event published")

	return nil
}

// PublishBatch publishes multiple events in a batch
func (p *InMemoryEventPublisher) PublishBatch(ctx context.Context, events []account.DomainEvent) error {
	for _, event := range events {
		if err := p.Publish(ctx, event); err != nil {
			return fmt.Errorf("failed to publish event %s: %w", event.EventID(), err)
		}
	}
	return nil
}

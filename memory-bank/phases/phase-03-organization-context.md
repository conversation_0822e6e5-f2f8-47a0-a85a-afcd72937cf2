# Phase 3: Organization Bounded Context Implementation

## 1. Overview and Goals

This document outlines the third phase of refactoring the `payment-service` , focusing on the implementation of the `organization` bounded context. This phase builds directly on the foundational infrastructure established in Phase 1 and the domain event mechanism from Phase 2.

**Goal for Phase 3:** To implement a secure, robust, and Clean Architecture-compliant `organization` bounded context, covering the management of companies, partners, products, and their associated configurations, including secure API key management.

**Objectives for Phase 3:**
1.  Implement core CRUD operations for Companies, Partners, and Products.
2.  Establish secure API key management for Companies.
3.  Refactor existing modules related to company, partner, and product management into the `organization` bounded context.
4.  Implement partner-specific configurations.
5.  Ensure all implementations adhere to the defined coding guidelines and leverage the infrastructure from Phase 1 and domain events from Phase 2.
6.  Develop comprehensive unit, integration, and end-to-end tests for the `organization` context.

**Scope for Phase 3:**
*   Refactoring `modules/company_management`,  `modules/partner`,  `modules/company_product`, and `modules/company_product_provider_channel_mapping` into `internal/app/organization` and `internal/domain/organization`.
*   Updating database schemas for `companies`,  `partners`,  `partner_emails`,  `partner_configurations`,  `api_keys`,  `partner_tags`, and `tags` to use UUIDs and secure hashing where applicable.
*   Implementing `CompanyService`,  `PartnerService`, and `ProductService` for managing these entities.
*   Implementing secure API key generation, storage (hashed), and validation.
*   Handling partner-specific configurations using JSONB.
*   Publishing domain events for significant changes (e.g.,  `OrganizationCreatedEvent`,  `CompanyUpdatedEvent`,  `PartnerDeletedEvent`).
*   Developing API endpoints for managing companies, partners, and products.
*   Creating initial unit, integration, and E2E tests for the `organization` context.

**Out of Scope for Phase 3:**
*   Detailed fee calculation logic (this will be handled by the `configuration` and `billing` contexts).
*   Complex inter-context communication beyond basic domain event publishing/subscribing.
*   Any other bounded contexts (billing, configuration, notification, reconciliation).

## 2. Architectural Principles (Recap from Phase 1 & 2)

This refactoring effort formally adopts and enforces **Clean Architecture** and **Domain-Driven Design (DDD)** principles to create clear boundaries between different layers of the application and promote scalability through Bounded Contexts.

### 2.1. The Dependency Rule (Clean Architecture)

Dependencies must only point inwards: `Infrastructure` -> `Application` -> `Domain` .
*   **Domain Layer**: Contains business entities and rules. It has zero dependencies on any outer layer.
*   **Application Layer**: Contains use cases that orchestrate the business logic. It defines interfaces that the Infrastructure layer implements.
*   **Infrastructure Layer**: Contains all external-facing components (database, payment providers, etc.). It depends on the Application layer.

```mermaid
graph TD
    subgraph "Domain Layer"
        direction LR
        D_Entities[Entities]
        D_Interfaces[Repository Interfaces]
    end

    subgraph "Application Layer"
        direction LR
        A_UseCases[Use Cases] --> D_Interfaces
        A_Interfaces[Provider Interfaces]
    end

    subgraph "Infrastructure Layer"
        direction LR
        I_Handlers[HTTP Handlers] --> A_UseCases
        I_Adapters[Provider Adapters] -- implements --> A_Interfaces
        I_Repos[Repository Implementations] -- implements --> D_Interfaces
    end

    I_Handlers --> A_UseCases
    A_UseCases --> D_Entities
    I_Repos --> D_Entities
    I_Adapters -- implements --> A_Interfaces
    A_UseCases -- depends on --> A_Interfaces
```

### 2.2. Bounded Contexts (Domain-Driven Design)

The `organization` bounded context is the focus of this phase.

*   **`account`**: Manages identity and access control for internal system users (admins, support, etc.).
*   **`organization`**: Manages the external parties involved in transactions (Companies, Partners, Products).
*   **`billing`**: The core transactional context that handles the entire payment lifecycle (Transactions, TransactionItems, TransactionHistory).
*   **`configuration`**: Manages the "how" of payments, including providers and channels (PaymentProvider, PaymentChannel, ProviderChannelMapping).
*   **`notification`**: Responsible for all outgoing communications (e.g., webhooks, emails).
*   **`reconciliation`**: Responsible for matching internal transaction records with external financial data.

### 2.3. Presentation Layer: Ports & Adapters

The presentation layer will adopt a Ports and Adapters approach, living in `/internal/ports` and responsible for translating external requests into calls to the application layer.

*   **`/ports/rest/`**: Contains all HTTP handlers, separated by their "persona" or purpose.
    -   **`openapi/`**: For external client APIs.
    -   **`dashboard/`**: For the internal admin panel API.
    -   **`callback/`**: For incoming webhooks from third parties.
*   **Shared Use Cases**: All handlers will call the same use cases in the `/internal/app` layer, ensuring business logic is centralized and consistently applied.

### 2.4. Target Directory Structure

The new directory structure reflects the DDD approach and Clean Architecture principles. This phase will populate the `internal/app/organization` and `internal/domain/organization` directories.

```
/payment-service/
├── cmd/
│   └── api/
│       ├── server/                # Server setup, DI, and routing
│       │   ├── server.go
│       │   ├── dependencies.go
│       │   └── routes.go
│       └── main.go                # Lean application entry point
├── internal/
│   ├── app/                       # Application layer (use cases, services)
│   │   ├── account/
│   │   ├── organization/          # <-- Populated in this phase
│   │   └── billing/
│   ├── domain/                    # Domain layer (entities, value objects, core business logic)
│   │   ├── account/
│   │   ├── organization/          # <-- Populated in this phase
│   │   ├── billing/
│   │   ├── configuration/
│   │   └── notification/
│   ├── ports/                     # Presentation Layer (Handlers)
│   │   └── rest/
│   │       ├── openapi/
│   │       ├── dashboard/
│   │       └── callback/
│   └── infrastructure/            # Infrastructure layer (external concerns)
│       ├── database/              # PostgreSQL repository implementations
│       ├── provider/              # Adapters for the gateway packages
│       ├── cache/                 # Redis implementation
│       └── eventbus/              # Domain Event Bus implementation
├── pkg/
│   ├── gateways/                  # External, reusable gateway packages
│   │   ├── xendit/
│   │   │   ├── client.go
│   │   │   └── go.mod
│   │   └── nicepay/
│   │       ├── client.go
│   │       └── go.mod
│   └── ... (other shared libraries)
├── api/                           # API definitions (e.g., OpenAPI/Swagger specs)
├── db/
│   └── migrations/                # Database migration .sql files
├── docs/                          # Project documentation
└── scripts/                       # Scripts for build, deployment, etc.
```

## 3. Key Architectural Decisions (Recap from Phase 1 & 2)

### 3.1. Manual Dependency Injection

To maintain full control over the application's startup process and avoid external dependencies, **manual dependency injection** will be used. All dependencies will be constructed and "wired" together in the `/cmd/api/server` package.

### 3.2. Externalized Gateway Packages

All third-party payment provider integrations (e.g., Xendit, NicePay) will be built as **standalone, reusable Go packages** located in `/pkg/gateways` .

### 3.3. Internationalization (i18n)

The application will support multiple languages, starting with English (en) and Indonesian (id). This will impact user-facing strings in API responses, validation messages, and notifications.

### 3.4. Domain Event Mechanism

The in-process domain event mechanism established in Phase 2 will be utilized for inter-context communication, allowing `organization` to publish events that other contexts (e.g., `account` , `billing` , `notification` ) can subscribe to.

## 4. Standardized Libraries (Recap from Phase 1)

The following libraries will be standardized across the project:

*   **Database Driver:** `pgx/v5`
*   **SQL Code Generator:** `sqlc`
*   **HTTP Framework:** `Gin`
*   **Logger:** `Zerolog`
*   **Configuration:** `Koanf`
*   **HTTP Client:** `Resty v2`
*   **Message Broker:** `go-rabbitmq`
*   **Integration Testing:** `testcontainers-go`
*   **Database Migrations:** `golang-migrate/migrate`
*   **Error Reporting:** `sentry-go`
*   **Internationalization:** `go-i18n`

## 5. Coding Conventions & Guidelines (Recap from Phase 1)

Adherence to these guidelines is mandatory for all contributors to ensure a high-quality, consistent, and maintainable codebase.

### 5.1. General Principles & Idiomatic Go

*   **Rule 1.1 (Simplicity - KISS):** Prefer simple, clear solutions over complex ones.
*   **Rule 1.2 (Effective Go):** Follow the principles outlined in the official [Effective Go](https://go.dev/doc/effective_go) document.
*   **Rule 1.3 (Package Naming):** All package names MUST be short-lowercase.
*   **Rule 1.4 (Variable Naming):** All variable names MUST be `camelCase`.
*   **Rule 1.5 (Interfaces):** Define interfaces to decouple components.
*   **Rule 1.6 (Formatting):** All generated Go code MUST be formatted with `gofmt`.
*   **Rule 1.7 (Tests):** Before create tests reviews the implementation and the interface.

### 5.2. Project Structure & Clean Architecture

*   **Rule 2.1 (Strict Layering):** The dependency direction `API` -> `Application` -> `Domain` MUST be strictly enforced.
*   **Rule 2.2 (Domain Purity):** The `internal/domain` package MUST NOT import any other project packages.
*   **Rule 2.3 (Thin Handlers):** API handlers (`internal/ports/rest`) MUST only parse requests, call a single application service method, and generate a standardized response.

### 5.3. Error Management Protocol

*   **Rule 3.1 (Error Wrapping):** Errors MUST be wrapped with context using `fmt.Errorf("package.function: %w", err)`.
*   **Rule 3.2 (Custom Domain Errors):** Use custom error variables for predictable business rule failures.
*   **Rule 3.3 (Centralized HTTP Error Mapping):** A dedicated Gin error-handling middleware is the only place where errors are translated into HTTP status codes.
*   **Rule 3.4 (Sentry for Unexpected Errors):** Use `webapi.ErrorWithSentry(c, err)` for all 5xx internal server errors.

### 5.4. Standardized API Responses (webapi package)

*   **Rule 4.1 (Mandatory Helpers):** All HTTP responses MUST be generated exclusively using helper functions from the `internal/ports/rest/webapi` package.
*   **Rule 4.2 (Success Structure):** The mandatory JSON structure for a successful response is: `{"status": "success", "data": { /* Actual response data */ }, "correlationId": "UUID_OF_REQUEST"}`.
*   **Rule 4.3 (Error Structure):** The mandatory JSON structure for an error response is: `{"status": "error", "error": {"code": "UNIQUE_ERROR_CODE", "message": "Human-readable error description."}, "correlationId": "UUID_OF_REQUEST"}`.
*   **Rule 4.4 (Helper Signatures and Usage):** Adhere to the defined signatures for `webapi.Success`,  `webapi.SuccessPaginated`,  `webapi.Error`, and `webapi.ErrorWithSentry`.

### 5.5. Logging Best Practices (zerolog)

*   **Rule 5.1 (Structured Logging):** All logging MUST be done using the global `zerolog` instance.
*   **Rule 5.2 (Contextual Logging):** Every log statement MUST include the `trace_id` from the context.
*   **Rule 5.3 (Error Logging):** When logging an error, the error object MUST be included using `.Err(err)`.

### 5.6. Concurrency and Graceful Shutdown

*   **Rule 6.1 (Panic Safety):** Any new goroutine MUST use a `defer...recover()` block, logging any recovered panic to Sentry.
*   **Rule 6.2 (Graceful Termination):** Any long-running goroutine MUST accept a `context.Context` and listen on `ctx.Done()`.
*   **Rule 6.3 (Server Shutdown):** `main.go` must implement graceful shutdown for the HTTP server.

### 5.7. Gin Middleware

*   **Rule 7.1 (Tracing Middleware):** MUST be the first middleware, generating a `trace_id`.
*   **Rule 7.2 (Logger Middleware):** MUST log request and response details.
*   **Rule 7.3 (Error Middleware):** MUST be the last middleware to centralize error handling.

### 5.8. Database Access (sqlc with pgx/v5)

*   **Rule 8.1 (SQL Files):** All SQL queries MUST be defined in `.sql` files.
*   **Rule 8.2 (sqlc.yaml Configuration):** The `sqlc.yaml` file MUST be configured to use `sql_package: "pgx/v5"` and appropriate type overrides.
*   **Rule 8.3 (Type Conversion):** All conversions between domain types and `pgtype` MUST be handled in a dedicated `internal/infrastructure/database/converters` package.
*   **Rule 8.4 (Transactions):** Repository methods that perform writes MUST accept `pgx.Tx` as a parameter.

### 5.9. Testing and CI/CD Strategy

*   **Rule 9.1 (Containerized Testing):** All integration and end-to-end tests that require external services (like a database or Redis) MUST use `testcontainers-go`.
*   **Rule 9.2 (Seeding Data for Tests):** For every new `.up.sql` migration file, a corresponding test data seed file MUST be created.
*   **Rule 9.3 (Test Coverage):** API handlers, application services, and repositories MUST have dedicated test files.
*   **Rule 9.4 (CI/CD Gate):** A pull request MUST NOT be allowed to merge into the main branch unless all tests (`go test ./...`) pass.

### 5.10. Internationalization (i18n)

*   **Rule 10.1 (Localization Support):** The application MUST support English (en) and Indonesian (id) locales.

### 5.11. Data Querying & Listing Guidelines

*   **Rule 11.1 (Repository Listing - Pagination):** All repository methods responsible for listing data that may require pagination MUST include a `ListWithPagination` method.
*   **Rule 11.2 (Repository Listing - Query Features):** All repository `List` and `ListWithPagination` methods MUST support common querying features including searching (e.g., full-text search, partial matching), filtering, and ordering.
*   **Rule 11.3 (Repository Listing - Filter Stacking):** Filtering capabilities in repository listing methods MUST allow for stacking multiple filter conditions (e.g., filtering by `status='active'` AND `category='electronics'`).
*   **Rule 11.4 (Repository Listing - Date Filtering):** If a data entity includes date fields, repository listing methods MUST support filtering by date ranges (e.g., "today", "yesterday", "last 7 days", "this month", "last month", custom start/end dates).

## 6. Database Schemas for Phase 3

This phase will introduce and refine the `organization` related schemas.

### 6.1. Company and Partner Management Schemas ( `organization` Bounded Context)

#### `companies` Table:

```sql
CREATE TABLE companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    parent_company_id UUID REFERENCES companies(id), -- Self-referencing for hierarchical companies
    code VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    alias VARCHAR(255),
    webhooks JSONB, -- Stores webhook configurations
    status BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### `api_keys` Table (New):

```sql
CREATE TABLE api_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id),
    key_hash VARCHAR(255) UNIQUE NOT NULL, -- Stores hashed API key
    secret_hash VARCHAR(255) NOT NULL, -- Stores hashed API secret
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE, -- Optional: for key expiration
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### `partners` Table:

```sql
CREATE TABLE partners (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id),
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(255),
    ar_account VARCHAR(255), -- Account Receivable Account
    ap_account VARCHAR(255), -- Account Payable Account (Bank Account Number)
    fee_percentage DECIMAL(5,2),
    fee_fix_value DECIMAL(10,2),
    sla INT,
    status BOOLEAN NOT NULL DEFAULT TRUE,
    code VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    is_send_email BOOLEAN NOT NULL DEFAULT FALSE,
    payment_channel_id UUID, -- References payment_channels table (future relationship)
    fee_behind BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### `partner_emails` Table (New):

```sql
CREATE TABLE partner_emails (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    partner_id UUID NOT NULL REFERENCES partners(id),
    email VARCHAR(255) NOT NULL,
    is_primary BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(partner_id, email)
);
```

#### `partner_configurations` Table (New):

```sql
CREATE TABLE partner_configurations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    partner_id UUID NOT NULL UNIQUE REFERENCES partners(id),
    configurations JSONB, -- Stores flexible, schema-less partner-specific settings
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### `partner_tags` Table:

```sql
CREATE TABLE partner_tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    partner_id UUID NOT NULL REFERENCES partners(id),
    tag_id UUID NOT NULL REFERENCES tags(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(partner_id, tag_id)
);
```

#### `tags` Table:

```sql
CREATE TABLE tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

## 7. Detailed Tasks for Phase 3

### 7.1. Organization Domain & Repository Implementation

*   **Task 7.1.1: Define Organization Domain Entities**
    -   Create `internal/domain/organization/company.go`, `partner.go`, `product.go`, `api_key.go`, `tag.go`.
    -   Define structs for `Company`, `Partner`, `Product`, `ApiKey`, `PartnerEmail`, `PartnerConfiguration`, `PartnerTag`, `Tag`.
*   **Task 7.1.2: Update Database Migrations**
    -   Create a new migration `db/migrations/000003_organization_tables.up.sql` to define `companies`, `api_keys`, `partners`, `partner_emails`, `partner_configurations`, `partner_tags`, and `tags` tables. Ensure UUIDs are used for primary keys.
    -   Create corresponding `000003_organization_tables.down.sql`.
*   **Task 7.1.3: Implement Organization Repositories**
    -   Create `internal/infrastructure/database/organization/company_repository.go`, `partner_repository.go`, `product_repository.go`.
    -   Implement repository interfaces for `Company`, `Partner`, `Product`, `ApiKey`, `PartnerEmail`, `PartnerConfiguration`, `PartnerTag`, `Tag` using `sqlc`-generated code.
    -   Ensure all `pgtype` conversions are handled by `internal/infrastructure/database/converters`.

### 7.2. Organization Application Use Cases

*   **Task 7.2.1: Implement Company Service**
    -   Create `internal/app/organization/company_service.go`.
    -   Implement methods for `CreateCompany`, `GetCompanyByID`, `UpdateCompany`, `DeleteCompany`, `GenerateApiKey`, `ValidateApiKey`.
    -   Ensure API keys are securely hashed before storage.
    -   Publish `CompanyCreatedEvent` and `CompanyUpdatedEvent` using `EventPublisher`.
*   **Task 7.2.2: Implement Partner Service**
    -   Create `internal/app/organization/partner_service.go`.
    -   Implement methods for `CreatePartner`, `GetPartnerByID`, `UpdatePartner`, `DeletePartner`, `AddPartnerEmail`, `UpdatePartnerConfiguration`.
    -   Publish `PartnerCreatedEvent` and `PartnerUpdatedEvent` using `EventPublisher`.
*   **Task 7.2.3: Implement Product Service**
    -   Create `internal/app/organization/product_service.go`.
    -   Implement methods for `CreateProduct`, `GetProductByID`, `UpdateProduct`, `DeleteProduct`.
    -   Publish `ProductCreatedEvent` and `ProductUpdatedEvent` using `EventPublisher`.

### 7.3. Organization Presentation Layer (API)

*   **Task 7.3.1: Implement Company Handlers**
    -   Create `internal/ports/rest/dashboard/company_handler.go` (for admin panel).
    -   Implement handlers for `POST /companies`, `GET /companies/{id}`, `PUT /companies/{id}`, `DELETE /companies/{id}`, `POST /companies/{id}/api-keys`.
*   **Task 7.3.2: Implement Partner Handlers**
    -   Create `internal/ports/rest/dashboard/partner_handler.go`.
    -   Implement handlers for `POST /partners`, `GET /partners/{id}`, `PUT /partners/{id}`, `DELETE /partners/{id}`.
*   **Task 7.3.3: Implement Product Handlers**
    -   Create `internal/ports/rest/dashboard/product_handler.go`.
    -   Implement handlers for `POST /products`, `GET /products/{id}`, `PUT /products/{id}`, `DELETE /products/{id}`.
*   **Task 7.3.4: Update Main Application Wiring**
    -   Modify `cmd/api/server/dependencies.go` to wire up the new `Organization` repositories and services.
    -   Modify `cmd/api/server/routes.go` to define the new `/companies`, `/partners`, `/products` endpoints, ensuring proper authentication and authorization middleware is applied.

### 7.4. Testing and Validation

*   **Task 7.4.1: Update Seed Data**
    -   Create `testdata/seeds/000003_seed_organization_data.sql` with sample `companies`, `partners`, `products`, `api_keys`, `tags`, etc.
*   **Task 7.4.2: Implement Unit Tests**
    -   Write unit tests for `internal/app/organization` services (mocking repositories and `EventPublisher`).
*   **Task 7.4.3: Implement Integration Tests**
    -   Write integration tests for `internal/infrastructure/database/organization` repositories using `testcontainers-go` for PostgreSQL.
    -   Write integration tests for `internal/app/organization` services using `testcontainers-go` for PostgreSQL.
*   **Task 7.4.4: Implement End-to-End Tests**
    -   Write E2E tests for `organization` API endpoints using `SetupTestServer()` from Phase 1.
    -   Test CRUD operations for companies, partners, and products.
    -   Test API key generation and validation.
    -   Verify that relevant domain events are published correctly.
*   **Task 7.4.5: Update Postman Collection**
    -   Create `docs/postman/collections/organization.postman_collection.json` with requests for all new organization endpoints.

## 8. Phase 3 Deliverables

*   Fully implemented `organization` bounded context with management for companies, partners, and products.
*   Updated database schemas for `companies`,  `api_keys`,  `partners`,  `partner_emails`,  `partner_configurations`,  `partner_tags`, and `tags`.
*   Secure API key management for companies.
*   Working CRUD operations for all core `organization` entities.
*   Publishing of relevant domain events for `organization` changes.
*   Comprehensive unit, integration, and E2E tests for the `organization` context.
*   Updated Postman collection for `organization` endpoints.
*   All code adhering to the defined coding guidelines.

## 9. Phase 3 Diagram:

```mermaid
graph TD

    subgraph "Phase 3: Organization Bounded Context Implementation"
        A[7.1. Organization Domain & Repository] --> B(7.2. Organization Application Use Cases)
        B --> C(7.3. Organization Presentation Layer)
        C --> D(7.4. Testing and Validation)
        A --> D
        B --> D
        C --> D
    end

    subgraph "7.1. Organization Domain & Repository"
        A1[7.1.1: Define Organization Domain Entities]
        A2[7.1.2: Update Database Migrations]
        A3[7.1.3: Implement Organization Repositories]
    end

    subgraph "7.2. Organization Application Use Cases"
        B1[7.2.1: Implement Company Service]
        B2[7.2.2: Implement Partner Service]
        B3[7.2.3: Implement Product Service]
    end

    subgraph "7.3. Organization Presentation Layer (API)"
        C1[7.3.1: Implement Company Handlers]
        C2[7.3.2: Implement Partner Handlers]
        C3[7.3.3: Implement Product Handlers]
        C4[7.3.4: Update Main Application Wiring]
    end

    subgraph "7.4. Testing and Validation"
        D1[7.4.1: Update Seed Data]
        D2[7.4.2: Implement Unit Tests]
        D3[7.4.3: Implement Integration Tests]
        D4[7.4.4: Implement End-to-End Tests]
        D5[7.4.5: Update Postman Collection]
    end

    style A1 fill:#f9f,stroke:#333,stroke-width:2px
    style A2 fill:#f9f,stroke:#333,stroke-width:2px
    style A3 fill:#f9f,stroke:#333,stroke-width:2px

    style B1 fill:#bbf,stroke:#333,stroke-width:2px
    style B2 fill:#bbf,stroke:#333,stroke-width:2px
    style B3 fill:#bbf,stroke:#333,stroke-width:2px

    style C1 fill:#ccf,stroke:#333,stroke-width:2px
    style C2 fill:#ccf,stroke:#333,stroke-width:2px
    style C3 fill:#ccf,stroke:#333,stroke-width:2px
    style C4 fill:#ccf,stroke:#333,stroke-width:2px

    style D1 fill:#ddf,stroke:#333,stroke-width:2px
    style D2 fill:#ddf,stroke:#333,stroke-width:2px
    style D3 fill:#ddf,stroke:#333,stroke-width:2px
    style D4 fill:#ddf,stroke:#333,stroke-width:2px
    style D5 fill:#ddf,stroke:#333,stroke-width:2px

## 10. Phase Completion Checklist

This checklist serves as a verification tool to ensure all tasks, requirements, rules, and guidelines for Phase 3 have been successfully completed and adhered to. Before marking this phase as complete, all items below must be checked off.

### 10.1. General Completion & Quality

*   [ ] All tasks outlined in Section 7 ("Detailed Tasks for Phase 3") have been implemented.
*   [ ] The project builds successfully without errors (`go build ./...`).
*   [ ] All tests pass (`go test ./...`).
*   [ ] Code has been formatted with `gofmt` (`gofmt -s -w .`).
*   [ ] No new warnings or errors are introduced by linters (e.g.,  `golangci-lint run`).
*   [ ] All dependencies are properly managed (`go mod tidy`).

### 10.2. Architectural Adherence

*   [ ] The `internal/app/organization` and `internal/domain/organization` directories are populated as per the target structure.
*   [ ] Dependencies within the `organization` context strictly follow the Clean Architecture rule.
*   [ ] Domain Event Mechanism is correctly utilized for publishing events from the `organization` context.

### 10.3. Core Functionality Verification

*   [ ] Core CRUD operations for Companies, Partners, and Products are functional.
*   [ ] Secure API key management (generation, storage, validation) is implemented and functional.
*   [ ] Partner-specific configurations using JSONB are handled correctly.
*   [ ] Relevant domain events (`CompanyCreatedEvent`,  `CompanyUpdatedEvent`,  `PartnerCreatedEvent`,  `PartnerUpdatedEvent`,  `ProductCreatedEvent`,  `ProductUpdatedEvent`) are published.
*   [ ] API endpoints for managing companies, partners, and products are functional.

### 10.4. Testing & Validation

*   [ ] All unit tests for `organization` services are passing.
*   [ ] All integration tests for `organization` repositories and services are passing.
*   [ ] All E2E tests for `organization` API endpoints are passing, covering CRUD operations and API key validation.
*   [ ] E2E tests verify that relevant domain events are published.
*   [ ] Postman collection for `organization` endpoints is updated and functional.
*   [ ] Seed data for `organization` tables is correctly applied in tests.

### 10.5. Coding Guidelines Compliance (Self-Assessment)

*   [ ] **Rule 1 (General):** `gofmt`,  `camelCase`, short-lowercase package names, interface-based design are followed.
*   [ ] **Rule 2 (Architecture):** `API` -> `Application` -> `Domain` dependency rule and thin handlers are enforced.
*   [ ] **Rule 3 (Error Handling):** Error wrapping, custom errors, centralized mapping, and Sentry integration are used.
*   [ ] **Rule 4 (API Responses):** `webapi` helpers are used for all responses, and error messages are localized.
*   [ ] **Rule 5 (Logging):** Structured, contextual logging with `zerolog` is implemented.
*   [ ] **Rule 6 (Concurrency):** Panic safety and graceful termination are considered.
*   [ ] **Rule 7 (Middleware):** The exact middleware order is followed.
*   [ ] **Rule 8 (Database):** `.sql` files for `sqlc`,  `pgx/v5` configuration, centralized type converters, and transactional repositories are used.
*   [ ] **Rule 9 (Testing & CI/CD):** `testcontainers-go`, DDL/DML separation, and test coverage are adhered to.
*   [ ] **Rule 10 (Internationalization):** Localization support is correctly implemented.

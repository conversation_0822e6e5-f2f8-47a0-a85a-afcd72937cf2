package handler

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"

	"github.com/wongpinter/payment-gateway/internal/application/account/service"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/webapi"
)

// AuthHandler handles authentication-related HTTP requests
type AuthHandler struct {
	userService service.UserService
	authService service.AuthService
	validator   *validator.Validate
}

// NewAuthHandler creates a new AuthHandler
func NewAuthHandler(userService *service.UserService, authService *service.AuthService) *AuthHandler {
	return &AuthHandler{
		userService: *userService,
		authService: *authService,
		validator:   validator.New(),
	}
}

// RegisterRequest represents a user registration request
type RegisterRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required,min=8"`
	Name     string `json:"name" validate:"required,min=2,max=100"`
}

// LoginRequest represents a user login request
type LoginRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
}

// RefreshTokenRequest represents a refresh token request
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" validate:"required"`
}

// LogoutRequest represents a logout request
type LogoutRequest struct {
	RefreshToken string `json:"refresh_token" validate:"required"`
}

// ChangePasswordRequest represents a change password request
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" validate:"required"`
	NewPassword     string `json:"new_password" validate:"required,min=8"`
}

// AuthResponse represents an authentication response
type AuthResponse struct {
	AccessToken  string                 `json:"access_token"`
	RefreshToken string                 `json:"refresh_token"`
	ExpiresAt    string                 `json:"expires_at"`
	User         service.UserInfo       `json:"user"`
}

// Register handles user registration
func (h *AuthHandler) Register(c *gin.Context) {
	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		webapi.Error(c, http.StatusBadRequest, "Invalid request format", err)
		return
	}

	// Validate request
	if err := h.validator.Struct(req); err != nil {
		webapi.Error(c, http.StatusBadRequest, "Validation failed", err)
		return
	}

	// Register user
	user, err := h.userService.RegisterUser(c.Request.Context(), req.Email, req.Password, req.Name)
	if err != nil {
		// Handle specific errors
		switch {
		case strings.Contains(err.Error(), "already exists"):
			webapi.Error(c, http.StatusConflict, "User already exists", err)
		case strings.Contains(err.Error(), "invalid email"):
			webapi.Error(c, http.StatusBadRequest, "Invalid email format", err)
		default:
			webapi.Error(c, http.StatusInternalServerError, "Failed to register user", err)
		}
		return
	}

	// Return user info (without sensitive data)
	response := gin.H{
		"id":         user.ID().String(),
		"email":      user.Email().String(),
		"name":       user.Name(),
		"is_active":  user.IsActive(),
		"created_at": user.CreatedAt(),
	}

	webapi.Success(c, http.StatusCreated, "User registered successfully", response)
}

// Login handles user authentication
func (h *AuthHandler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		webapi.Error(c, http.StatusBadRequest, "Invalid request format", err)
		return
	}

	// Validate request
	if err := h.validator.Struct(req); err != nil {
		webapi.Error(c, http.StatusBadRequest, "Validation failed", err)
		return
	}

	// Convert to service request
	loginReq := service.LoginRequest{
		Email:    req.Email,
		Password: req.Password,
	}

	// Authenticate user
	loginResp, err := h.authService.Login(c.Request.Context(), loginReq)
	if err != nil {
		// Handle specific errors
		switch {
		case strings.Contains(err.Error(), "invalid credentials"):
			webapi.Error(c, http.StatusUnauthorized, "Invalid email or password", err)
		case strings.Contains(err.Error(), "inactive"):
			webapi.Error(c, http.StatusForbidden, "Account is inactive", err)
		default:
			webapi.Error(c, http.StatusInternalServerError, "Login failed", err)
		}
		return
	}

	// Return authentication response
	response := AuthResponse{
		AccessToken:  loginResp.AccessToken,
		RefreshToken: loginResp.RefreshToken,
		ExpiresAt:    loginResp.ExpiresAt.Format("2006-01-02T15:04:05Z07:00"),
		User:         loginResp.User,
	}

	webapi.Success(c, http.StatusOK, "Login successful", response)
}

// RefreshToken handles token refresh
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		webapi.Error(c, http.StatusBadRequest, "Invalid request format", err)
		return
	}

	// Validate request
	if err := h.validator.Struct(req); err != nil {
		webapi.Error(c, http.StatusBadRequest, "Validation failed", err)
		return
	}

	// Convert to service request
	refreshReq := service.RefreshTokenRequest{
		RefreshToken: req.RefreshToken,
	}

	// Refresh token
	loginResp, err := h.authService.RefreshToken(c.Request.Context(), refreshReq)
	if err != nil {
		// Handle specific errors
		switch {
		case strings.Contains(err.Error(), "invalid refresh token"):
			webapi.Error(c, http.StatusUnauthorized, "Invalid refresh token", err)
		case strings.Contains(err.Error(), "expired"):
			webapi.Error(c, http.StatusUnauthorized, "Refresh token expired", err)
		case strings.Contains(err.Error(), "inactive"):
			webapi.Error(c, http.StatusForbidden, "Account is inactive", err)
		default:
			webapi.Error(c, http.StatusInternalServerError, "Token refresh failed", err)
		}
		return
	}

	// Return authentication response
	response := AuthResponse{
		AccessToken:  loginResp.AccessToken,
		RefreshToken: loginResp.RefreshToken,
		ExpiresAt:    loginResp.ExpiresAt.Format("2006-01-02T15:04:05Z07:00"),
		User:         loginResp.User,
	}

	webapi.Success(c, http.StatusOK, "Token refreshed successfully", response)
}

// Logout handles user logout
func (h *AuthHandler) Logout(c *gin.Context) {
	var req LogoutRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		webapi.Error(c, http.StatusBadRequest, "Invalid request format", err)
		return
	}

	// Validate request
	if err := h.validator.Struct(req); err != nil {
		webapi.Error(c, http.StatusBadRequest, "Validation failed", err)
		return
	}

	// Logout user
	if err := h.authService.Logout(c.Request.Context(), req.RefreshToken); err != nil {
		// Log error but don't fail logout - it's better to be permissive here
		// In most cases, logout should succeed even if the token is already invalid
		webapi.Error(c, http.StatusInternalServerError, "Logout failed", err)
		return
	}

	webapi.Success(c, http.StatusOK, "Logout successful", nil)
}

// GetProfile returns the current user's profile
func (h *AuthHandler) GetProfile(c *gin.Context) {
	// Get user info from context (set by auth middleware)
	userInfo, exists := c.Get("user")
	if !exists {
		webapi.Error(c, http.StatusUnauthorized, "User not authenticated", nil)
		return
	}

	webapi.Success(c, http.StatusOK, "Profile retrieved successfully", userInfo)
}

// ChangePassword handles password change
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		webapi.Error(c, http.StatusBadRequest, "Invalid request format", err)
		return
	}

	// Validate request
	if err := h.validator.Struct(req); err != nil {
		webapi.Error(c, http.StatusBadRequest, "Validation failed", err)
		return
	}

	// Get user info from context (set by auth middleware)
	userInfo, exists := c.Get("user")
	if !exists {
		webapi.Error(c, http.StatusUnauthorized, "User not authenticated", nil)
		return
	}

	user, ok := userInfo.(*service.UserInfo)
	if !ok {
		webapi.Error(c, http.StatusInternalServerError, "Invalid user context", nil)
		return
	}

	// Parse user ID
	userID, err := account.UserIDFromString(user.ID)
	if err != nil {
		webapi.Error(c, http.StatusInternalServerError, "Invalid user ID", err)
		return
	}

	// Change password
	if err := h.userService.ChangePassword(c.Request.Context(), userID, req.CurrentPassword, req.NewPassword); err != nil {
		// Handle specific errors
		switch {
		case strings.Contains(err.Error(), "invalid credentials"):
			webapi.Error(c, http.StatusUnauthorized, "Current password is incorrect", err)
		default:
			webapi.Error(c, http.StatusInternalServerError, "Failed to change password", err)
		}
		return
	}

	webapi.Success(c, http.StatusOK, "Password changed successfully", nil)
}

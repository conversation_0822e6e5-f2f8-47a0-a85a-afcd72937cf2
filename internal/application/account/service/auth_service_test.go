package service

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/wongpinter/payment-gateway/internal/domain/account"
)

// Mock implementations for testing
type MockUserRepository struct {
	mock.Mock
}

func (m *MockUserRepository) Create(ctx context.Context, user *account.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserRepository) GetByID(ctx context.Context, id account.UserID) (*account.User, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*account.User), args.Error(1)
}

func (m *MockUserRepository) GetByEmail(ctx context.Context, email account.Email) (*account.User, error) {
	args := m.Called(ctx, email)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*account.User), args.Error(1)
}

func (m *MockUserRepository) Update(ctx context.Context, user *account.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserRepository) Delete(ctx context.Context, id account.UserID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockUserRepository) List(ctx context.Context, limit, offset int) ([]*account.User, error) {
	args := m.Called(ctx, limit, offset)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*account.User), args.Error(1)
}

func (m *MockUserRepository) Count(ctx context.Context) (int, error) {
	args := m.Called(ctx)
	return args.Int(0), args.Error(1)
}

func (m *MockUserRepository) ExistsByEmail(ctx context.Context, email account.Email) (bool, error) {
	args := m.Called(ctx, email)
	return args.Bool(0), args.Error(1)
}

func (m *MockUserRepository) GetUserRoles(ctx context.Context, userID account.UserID) ([]account.Role, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]account.Role), args.Error(1)
}

func (m *MockUserRepository) AssignRole(ctx context.Context, userID account.UserID, roleID account.RoleID) error {
	args := m.Called(ctx, userID, roleID)
	return args.Error(0)
}

func (m *MockUserRepository) RevokeRole(ctx context.Context, userID account.UserID, roleID account.RoleID) error {
	args := m.Called(ctx, userID, roleID)
	return args.Error(0)
}

func (m *MockUserRepository) GetUsersWithRole(ctx context.Context, roleID account.RoleID) ([]*account.User, error) {
	args := m.Called(ctx, roleID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*account.User), args.Error(1)
}

type MockRoleRepository struct {
	mock.Mock
}

func (m *MockRoleRepository) Create(ctx context.Context, role *account.Role) error {
	args := m.Called(ctx, role)
	return args.Error(0)
}

func (m *MockRoleRepository) GetByID(ctx context.Context, id account.RoleID) (*account.Role, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*account.Role), args.Error(1)
}

func (m *MockRoleRepository) GetByName(ctx context.Context, name string) (*account.Role, error) {
	args := m.Called(ctx, name)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*account.Role), args.Error(1)
}

func (m *MockRoleRepository) Update(ctx context.Context, role *account.Role) error {
	args := m.Called(ctx, role)
	return args.Error(0)
}

func (m *MockRoleRepository) Delete(ctx context.Context, id account.RoleID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockRoleRepository) List(ctx context.Context, limit, offset int) ([]*account.Role, error) {
	args := m.Called(ctx, limit, offset)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*account.Role), args.Error(1)
}

func (m *MockRoleRepository) Count(ctx context.Context) (int, error) {
	args := m.Called(ctx)
	return args.Int(0), args.Error(1)
}

func (m *MockRoleRepository) ExistsByName(ctx context.Context, name string) (bool, error) {
	args := m.Called(ctx, name)
	return args.Bool(0), args.Error(1)
}

func (m *MockRoleRepository) GetRolePermissions(ctx context.Context, roleID account.RoleID) ([]account.Permission, error) {
	args := m.Called(ctx, roleID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]account.Permission), args.Error(1)
}

func (m *MockRoleRepository) AssignPermission(ctx context.Context, roleID account.RoleID, permissionID account.PermissionID) error {
	args := m.Called(ctx, roleID, permissionID)
	return args.Error(0)
}

func (m *MockRoleRepository) RevokePermission(ctx context.Context, roleID account.RoleID, permissionID account.PermissionID) error {
	args := m.Called(ctx, roleID, permissionID)
	return args.Error(0)
}

func (m *MockRoleRepository) GetRolesWithPermission(ctx context.Context, permissionID account.PermissionID) ([]*account.Role, error) {
	args := m.Called(ctx, permissionID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*account.Role), args.Error(1)
}

type MockPermissionRepository struct {
	mock.Mock
}

func (m *MockPermissionRepository) Create(ctx context.Context, permission *account.Permission) error {
	args := m.Called(ctx, permission)
	return args.Error(0)
}

func (m *MockPermissionRepository) GetByID(ctx context.Context, id account.PermissionID) (*account.Permission, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*account.Permission), args.Error(1)
}

func (m *MockPermissionRepository) GetByName(ctx context.Context, name string) (*account.Permission, error) {
	args := m.Called(ctx, name)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*account.Permission), args.Error(1)
}

func (m *MockPermissionRepository) Update(ctx context.Context, permission *account.Permission) error {
	args := m.Called(ctx, permission)
	return args.Error(0)
}

func (m *MockPermissionRepository) Delete(ctx context.Context, id account.PermissionID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockPermissionRepository) List(ctx context.Context, limit, offset int) ([]*account.Permission, error) {
	args := m.Called(ctx, limit, offset)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*account.Permission), args.Error(1)
}

func (m *MockPermissionRepository) Count(ctx context.Context) (int, error) {
	args := m.Called(ctx)
	return args.Int(0), args.Error(1)
}

func (m *MockPermissionRepository) ExistsByName(ctx context.Context, name string) (bool, error) {
	args := m.Called(ctx, name)
	return args.Bool(0), args.Error(1)
}

func (m *MockPermissionRepository) GetByResource(ctx context.Context, resource string) ([]*account.Permission, error) {
	args := m.Called(ctx, resource)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*account.Permission), args.Error(1)
}

func (m *MockPermissionRepository) GetByResourceAndAction(ctx context.Context, resource, action string) (*account.Permission, error) {
	args := m.Called(ctx, resource, action)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*account.Permission), args.Error(1)
}

type MockSessionRepository struct {
	mock.Mock
}

func (m *MockSessionRepository) Create(ctx context.Context, session *account.Session) error {
	args := m.Called(ctx, session)
	return args.Error(0)
}

func (m *MockSessionRepository) GetByID(ctx context.Context, id account.SessionID) (*account.Session, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*account.Session), args.Error(1)
}

func (m *MockSessionRepository) Update(ctx context.Context, session *account.Session) error {
	args := m.Called(ctx, session)
	return args.Error(0)
}

func (m *MockSessionRepository) Delete(ctx context.Context, id account.SessionID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockSessionRepository) DeleteByUserID(ctx context.Context, userID account.UserID) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockSessionRepository) DeleteExpired(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func (m *MockSessionRepository) GetByUserID(ctx context.Context, userID account.UserID) ([]*account.Session, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*account.Session), args.Error(1)
}

type MockPasswordHasher struct {
	mock.Mock
}

func (m *MockPasswordHasher) Hash(password string) (string, error) {
	args := m.Called(password)
	return args.String(0), args.Error(1)
}

func (m *MockPasswordHasher) Verify(password, hash string) error {
	args := m.Called(password, hash)
	return args.Error(0)
}

type MockEventPublisher struct {
	mock.Mock
}

func (m *MockEventPublisher) Publish(ctx context.Context, event account.DomainEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

type MockJWTManager struct {
	mock.Mock
}

func (m *MockJWTManager) GenerateAccessToken(ctx context.Context, user *account.User, roles []account.Role) (string, time.Time, error) {
	args := m.Called(ctx, user, roles)
	return args.String(0), args.Get(1).(time.Time), args.Error(2)
}

func (m *MockJWTManager) GenerateRefreshToken(ctx context.Context, user *account.User) (string, time.Time, error) {
	args := m.Called(ctx, user)
	return args.String(0), args.Get(1).(time.Time), args.Error(2)
}

func (m *MockJWTManager) ValidateAccessToken(ctx context.Context, tokenString string) (*AccessTokenClaims, error) {
	args := m.Called(ctx, tokenString)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*AccessTokenClaims), args.Error(1)
}

func (m *MockJWTManager) ValidateRefreshToken(ctx context.Context, tokenString string) (*RefreshTokenClaims, error) {
	args := m.Called(ctx, tokenString)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*RefreshTokenClaims), args.Error(1)
}

func (m *MockJWTManager) BlacklistToken(ctx context.Context, tokenString string) error {
	args := m.Called(ctx, tokenString)
	return args.Error(0)
}

func (m *MockJWTManager) BlacklistUserTokens(ctx context.Context, userID string, expiresAt time.Time) error {
	args := m.Called(ctx, userID, expiresAt)
	return args.Error(0)
}

func (m *MockJWTManager) ExtractTokenID(tokenString string) (string, error) {
	args := m.Called(tokenString)
	return args.String(0), args.Error(1)
}

func (m *MockJWTManager) GetTokenTTLs() (accessTTL, refreshTTL time.Duration) {
	args := m.Called()
	return args.Get(0).(time.Duration), args.Get(1).(time.Duration)
}

func TestAuthService_Login(t *testing.T) {
	t.Run("successful login", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		permissionRepo := &MockPermissionRepository{}
		sessionRepo := &MockSessionRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}
		jwtManager := &MockJWTManager{}

		// Create service
		authService := NewAuthService(userRepo, roleRepo, permissionRepo, sessionRepo, passwordHasher, eventPublisher, jwtManager)

		// Setup test data
		email, _ := account.NewEmail("<EMAIL>")
		passwordHash := account.NewPasswordHash("hashed_password")
		user := account.NewUser(email, passwordHash, "Test User")
		roles := []account.Role{*account.NewRole("user", "User role")}

		// Setup expectations
		userRepo.On("GetByEmail", mock.Anything, email).Return(user, nil)
		passwordHasher.On("Verify", "password123", "hashed_password").Return(nil)
		userRepo.On("GetUserRoles", mock.Anything, user.ID()).Return(roles, nil)
		
		accessToken := "access_token"
		refreshToken := "refresh_token"
		expiresAt := time.Now().Add(time.Hour)
		jwtManager.On("GenerateAccessToken", mock.Anything, user, roles).Return(accessToken, expiresAt, nil)
		jwtManager.On("GenerateRefreshToken", mock.Anything, user).Return(refreshToken, expiresAt, nil)

		// Execute
		req := LoginRequest{
			Email:    "<EMAIL>",
			Password: "password123",
		}
		
		resp, err := authService.Login(context.Background(), req)

		// Assert
		require.NoError(t, err)
		assert.Equal(t, accessToken, resp.AccessToken)
		assert.Equal(t, refreshToken, resp.RefreshToken)
		assert.Equal(t, expiresAt, resp.ExpiresAt)
		assert.Equal(t, user.ID().String(), resp.User.ID)
		assert.Equal(t, user.Email().String(), resp.User.Email)
		assert.Equal(t, user.Name(), resp.User.Name)

		// Verify all expectations were met
		userRepo.AssertExpectations(t)
		passwordHasher.AssertExpectations(t)
		jwtManager.AssertExpectations(t)
	})

	t.Run("invalid email format", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		permissionRepo := &MockPermissionRepository{}
		sessionRepo := &MockSessionRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}
		jwtManager := &MockJWTManager{}

		// Create service
		authService := NewAuthService(userRepo, roleRepo, permissionRepo, sessionRepo, passwordHasher, eventPublisher, jwtManager)

		// Execute
		req := LoginRequest{
			Email:    "invalid-email",
			Password: "password123",
		}
		
		_, err := authService.Login(context.Background(), req)

		// Assert
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid email format")
	})

	t.Run("user not found", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		permissionRepo := &MockPermissionRepository{}
		sessionRepo := &MockSessionRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}
		jwtManager := &MockJWTManager{}

		// Create service
		authService := NewAuthService(userRepo, roleRepo, permissionRepo, sessionRepo, passwordHasher, eventPublisher, jwtManager)

		// Setup test data
		email, _ := account.NewEmail("<EMAIL>")

		// Setup expectations
		userRepo.On("GetByEmail", mock.Anything, email).Return(nil, account.ErrUserNotFound)

		// Execute
		req := LoginRequest{
			Email:    "<EMAIL>",
			Password: "password123",
		}
		
		_, err := authService.Login(context.Background(), req)

		// Assert
		assert.Error(t, err)
		assert.Equal(t, account.ErrInvalidCredentials, err)

		// Verify expectations
		userRepo.AssertExpectations(t)
	})

	t.Run("inactive user", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		permissionRepo := &MockPermissionRepository{}
		sessionRepo := &MockSessionRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}
		jwtManager := &MockJWTManager{}

		// Create service
		authService := NewAuthService(userRepo, roleRepo, permissionRepo, sessionRepo, passwordHasher, eventPublisher, jwtManager)

		// Setup test data
		email, _ := account.NewEmail("<EMAIL>")
		passwordHash := account.NewPasswordHash("hashed_password")
		user := account.NewUserWithID(account.NewUserID(), email, passwordHash, "Test User", false, time.Now(), time.Now())

		// Setup expectations
		userRepo.On("GetByEmail", mock.Anything, email).Return(user, nil)

		// Execute
		req := LoginRequest{
			Email:    "<EMAIL>",
			Password: "password123",
		}
		
		_, err := authService.Login(context.Background(), req)

		// Assert
		assert.Error(t, err)
		assert.Equal(t, account.ErrUserInactive, err)

		// Verify expectations
		userRepo.AssertExpectations(t)
	})

	t.Run("invalid password", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		permissionRepo := &MockPermissionRepository{}
		sessionRepo := &MockSessionRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}
		jwtManager := &MockJWTManager{}

		// Create service
		authService := NewAuthService(userRepo, roleRepo, permissionRepo, sessionRepo, passwordHasher, eventPublisher, jwtManager)

		// Setup test data
		email, _ := account.NewEmail("<EMAIL>")
		passwordHash := account.NewPasswordHash("hashed_password")
		user := account.NewUser(email, passwordHash, "Test User")

		// Setup expectations
		userRepo.On("GetByEmail", mock.Anything, email).Return(user, nil)
		passwordHasher.On("Verify", "wrong_password", "hashed_password").Return(errors.New("password mismatch"))

		// Execute
		req := LoginRequest{
			Email:    "<EMAIL>",
			Password: "wrong_password",
		}
		
		_, err := authService.Login(context.Background(), req)

		// Assert
		assert.Error(t, err)
		assert.Equal(t, account.ErrInvalidCredentials, err)

		// Verify expectations
		userRepo.AssertExpectations(t)
		passwordHasher.AssertExpectations(t)
	})
}

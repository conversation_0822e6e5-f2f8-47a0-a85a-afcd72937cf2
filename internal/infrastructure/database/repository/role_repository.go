package repository

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"

	"github.com/wongpinter/payment-gateway/internal/domain/account"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/database/converter"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/database/sqlc"
)

// RoleRepository implements the account.RoleRepository interface
type RoleRepository struct {
	db      *pgxpool.Pool
	queries *sqlc.Queries
}

// NewRoleRepository creates a new RoleRepository
func NewRoleRepository(db *pgxpool.Pool) *RoleRepository {
	return &RoleRepository{
		db:      db,
		queries: sqlc.New(db),
	}
}

// Create saves a new role to the repository
func (r *RoleRepository) Create(ctx context.Context, role *account.Role) error {
	model := converter.RoleToModel(role)
	
	_, err := r.queries.CreateRole(ctx, sqlc.CreateRoleParams{
		ID:          model.ID,
		Name:        model.Name,
		Description: model.Description,
		IsActive:    model.IsActive,
		CreatedAt:   model.CreatedAt,
		UpdatedAt:   model.UpdatedAt,
	})
	
	if err != nil {
		return fmt.Errorf("role_repository.Create: %w", err)
	}
	
	return nil
}

// GetByID retrieves a role by its ID
func (r *RoleRepository) GetByID(ctx context.Context, id account.RoleID) (*account.Role, error) {
	model, err := r.queries.GetRoleByID(ctx, id.UUID())
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, account.ErrRoleNotFound
		}
		return nil, fmt.Errorf("role_repository.GetByID: %w", err)
	}
	
	role, err := converter.ModelToRole(model)
	if err != nil {
		return nil, fmt.Errorf("role_repository.GetByID: %w", err)
	}
	
	return role, nil
}

// GetByName retrieves a role by its name
func (r *RoleRepository) GetByName(ctx context.Context, name string) (*account.Role, error) {
	model, err := r.queries.GetRoleByName(ctx, name)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, account.ErrRoleNotFound
		}
		return nil, fmt.Errorf("role_repository.GetByName: %w", err)
	}
	
	role, err := converter.ModelToRole(model)
	if err != nil {
		return nil, fmt.Errorf("role_repository.GetByName: %w", err)
	}
	
	return role, nil
}

// Update updates an existing role in the repository
func (r *RoleRepository) Update(ctx context.Context, role *account.Role) error {
	model := converter.RoleToModel(role)
	
	_, err := r.queries.UpdateRole(ctx, sqlc.UpdateRoleParams{
		ID:          model.ID,
		Name:        model.Name,
		Description: model.Description,
		IsActive:    model.IsActive,
		UpdatedAt:   converter.TimeToPgTimestamptz(time.Now()),
	})
	
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return account.ErrRoleNotFound
		}
		return fmt.Errorf("role_repository.Update: %w", err)
	}
	
	return nil
}

// Delete removes a role from the repository (soft delete)
func (r *RoleRepository) Delete(ctx context.Context, id account.RoleID) error {
	err := r.queries.DeleteRole(ctx, id.UUID())
	if err != nil {
		return fmt.Errorf("role_repository.Delete: %w", err)
	}
	
	return nil
}

// List retrieves a list of roles with pagination
func (r *RoleRepository) List(ctx context.Context, offset, limit int) ([]*account.Role, error) {
	models, err := r.queries.ListRoles(ctx, sqlc.ListRolesParams{
		Limit:  int32(limit),
		Offset: int32(offset),
	})
	
	if err != nil {
		return nil, fmt.Errorf("role_repository.List: %w", err)
	}
	
	roles := make([]*account.Role, 0, len(models))
	for _, model := range models {
		role, err := converter.ModelToRole(model)
		if err != nil {
			return nil, fmt.Errorf("role_repository.List: %w", err)
		}
		roles = append(roles, role)
	}
	
	return roles, nil
}

// Count returns the total number of roles
func (r *RoleRepository) Count(ctx context.Context) (int, error) {
	count, err := r.queries.CountRoles(ctx)
	if err != nil {
		return 0, fmt.Errorf("role_repository.Count: %w", err)
	}
	
	return int(count), nil
}

// ExistsByName checks if a role with the given name exists
func (r *RoleRepository) ExistsByName(ctx context.Context, name string) (bool, error) {
	exists, err := r.queries.ExistsRoleByName(ctx, name)
	if err != nil {
		return false, fmt.Errorf("role_repository.ExistsByName: %w", err)
	}
	
	return exists, nil
}

// GetRolePermissions retrieves all permissions for a specific role
func (r *RoleRepository) GetRolePermissions(ctx context.Context, roleID account.RoleID) ([]account.Permission, error) {
	models, err := r.queries.GetRolePermissions(ctx, roleID.UUID())
	if err != nil {
		return nil, fmt.Errorf("role_repository.GetRolePermissions: %w", err)
	}
	
	permissions := make([]account.Permission, 0, len(models))
	for _, model := range models {
		permission, err := converter.ModelToPermission(model)
		if err != nil {
			return nil, fmt.Errorf("role_repository.GetRolePermissions: %w", err)
		}
		permissions = append(permissions, *permission)
	}
	
	return permissions, nil
}

// AssignPermission assigns a permission to a role
func (r *RoleRepository) AssignPermission(ctx context.Context, roleID account.RoleID, permissionID account.PermissionID) error {
	err := r.queries.AssignRolePermission(ctx, sqlc.AssignRolePermissionParams{
		RoleID:       roleID.UUID(),
		PermissionID: permissionID.UUID(),
	})
	
	if err != nil {
		return fmt.Errorf("role_repository.AssignPermission: %w", err)
	}
	
	return nil
}

// RevokePermission revokes a permission from a role
func (r *RoleRepository) RevokePermission(ctx context.Context, roleID account.RoleID, permissionID account.PermissionID) error {
	err := r.queries.RevokeRolePermission(ctx, sqlc.RevokeRolePermissionParams{
		RoleID:       roleID.UUID(),
		PermissionID: permissionID.UUID(),
	})
	
	if err != nil {
		return fmt.Errorf("role_repository.RevokePermission: %w", err)
	}
	
	return nil
}

// GetRolesWithPermission retrieves all roles that have a specific permission
func (r *RoleRepository) GetRolesWithPermission(ctx context.Context, permissionID account.PermissionID) ([]*account.Role, error) {
	models, err := r.queries.GetRolesWithPermission(ctx, permissionID.UUID())
	if err != nil {
		return nil, fmt.Errorf("role_repository.GetRolesWithPermission: %w", err)
	}
	
	roles := make([]*account.Role, 0, len(models))
	for _, model := range models {
		role, err := converter.ModelToRole(model)
		if err != nil {
			return nil, fmt.Errorf("role_repository.GetRolesWithPermission: %w", err)
		}
		roles = append(roles, role)
	}
	
	return roles, nil
}

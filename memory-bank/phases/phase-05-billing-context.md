# Phase 5: Billing Bounded Context Implementation

## 1. Overview and Goals

This document outlines the fifth phase of refactoring the `payment-service`, focusing on the implementation of the `billing` bounded context. This phase builds directly on the foundational infrastructure established in Phase 1, the domain event mechanism from Phase 2, and integrates with the `organization` and `configuration` contexts from Phases 3 and 4.

**Goal for Phase 5:** To implement a secure, robust, and Clean Architecture-compliant `billing` bounded context, covering the core functionalities of Cash-In and Cash-Out transactions, including fee calculations, payment gateway interactions, and status management.

**Objectives for Phase 5:**
1.  Implement core functionalities for Cash-In transactions (creation, status updates, callbacks, refunds).
2.  Implement core functionalities for Cash-Out transactions (disbursement requests, processing, callbacks).
3.  Integrate with `organization` and `configuration` contexts for company, partner, product, and payment channel/provider data.
4.  Implement centralized fee calculation logic.
5.  Abstract payment gateway interactions behind common interfaces.
6.  Streamline status management using event sourcing for transactions.
7.  Ensure all implementations adhere to the defined coding guidelines and leverage the infrastructure from Phase 1 and domain events from Phase 2.
8.  Develop comprehensive unit, integration, and end-to-end tests for the `billing` context.

**Scope for Phase 5:**
*   Refactoring `modules/cash_in_transaction` and `modules/cash_out_transaction` into `internal/app/billing` and `internal/domain/billing`.
*   Updating database schemas for `cash_in_transactions`, `cash_in_transaction_items`, `cash_in_transaction_histories`, `cash_out_transactions`, `cash_out_transaction_details`, `cash_out_transaction_histories`, and `company_cash_flows` to use UUIDs and incorporate event sourcing tables (e.g., `cash_in_transaction_events`, `cash_out_transaction_events`).
*   Implementing `CashInService` and `CashOutService` for managing transactions.
*   Implementing a centralized fee calculation service within `billing`.
*   Defining and implementing `PaymentGateway` and `DisbursementGateway` interfaces and their adapters for external payment providers.
*   Implementing event sourcing for transaction status management, including snapshotting and compensating transactions.
*   Implementing reliable third-party notifications using an Outbox pattern.
*   Publishing domain events for significant transaction lifecycle changes (e.g., `CashInPaidEvent`, `CashOutDisbursedEvent`).
*   Developing API endpoints for Cash-In and Cash-Out transactions and their callbacks.
*   Creating initial unit, integration, and E2E tests for the `billing` context.

**Out of Scope for Phase 5:**
*   Reconciliation logic (this is a separate bounded context).
*   Advanced notification features (handled by the `notification` context).
*   OTP validation (handled by the `account` context).
*   Any other bounded contexts not directly related to core transaction processing.

## 2. Architectural Principles (Recap from Phase 1, 2, 3 & 4)

This refactoring effort formally adopts and enforces **Clean Architecture** and **Domain-Driven Design (DDD)** principles to create clear boundaries between different layers of the application and promote scalability through Bounded Contexts.

### 2.1. The Dependency Rule (Clean Architecture)

Dependencies must only point inwards: `Infrastructure` -> `Application` -> `Domain`.
*   **Domain Layer**: Contains business entities and rules. It has zero dependencies on any outer layer.
*   **Application Layer**: Contains use cases that orchestrate the business logic. It defines interfaces that the Infrastructure layer implements.
*   **Infrastructure Layer**: Contains all external-facing components (database, payment providers, etc.). It depends on the Application layer.

```mermaid
graph TD
    subgraph "Domain Layer"
        direction LR
        D_Entities[Entities]
        D_Interfaces[Repository Interfaces]
    end

    subgraph "Application Layer"
        direction LR
        A_UseCases[Use Cases] --> D_Interfaces
        A_Interfaces[Provider Interfaces]
    end

    subgraph "Infrastructure Layer"
        direction LR
        I_Handlers[HTTP Handlers] --> A_UseCases
        I_Adapters[Provider Adapters] -- implements --> A_Interfaces
        I_Repos[Repository Implementations] -- implements --> D_Interfaces
    end

    I_Handlers --> A_UseCases
    A_UseCases --> D_Entities
    I_Repos --> D_Entities
    I_Adapters -- implements --> A_Interfaces
    A_UseCases -- depends on --> A_Interfaces
```

### 2.2. Bounded Contexts (Domain-Driven Design)

The `billing` bounded context is the focus of this phase.

*   **`account`**: Manages identity and access control for internal system users (admins, support, etc.).
*   **`organization`**: Manages the external parties involved in transactions (Companies, Partners, Products).
*   **`billing`**: The core transactional context that handles the entire payment lifecycle (Transactions, TransactionItems, TransactionHistory).
*   **`configuration`**: Manages the "how" of payments, including providers and channels (PaymentProvider, PaymentChannel, ProviderChannelMapping).
*   **`notification`**: Responsible for all outgoing communications (e.g., webhooks, emails).
*   **`reconciliation`**: Responsible for matching internal transaction records with external financial data.

### 2.3. Presentation Layer: Ports & Adapters

The presentation layer will adopt a Ports and Adapters approach, living in `/internal/ports` and responsible for translating external requests into calls to the application layer.

*   **`/ports/rest/`**: Contains all HTTP handlers, separated by their "persona" or purpose.
    *   **`openapi/`**: For external client APIs.
    *   **`dashboard/`**: For the internal admin panel API.
    *   **`callback/`**: For incoming webhooks from third parties.
*   **Shared Use Cases**: All handlers will call the same use cases in the `/internal/app` layer, ensuring business logic is centralized and consistently applied.

### 2.4. Target Directory Structure

The new directory structure reflects the DDD approach and Clean Architecture principles. This phase will populate the `internal/app/billing` and `internal/domain/billing` directories.

```
/payment-service/
├── cmd/
│   └── api/
│       ├── server/                # Server setup, DI, and routing
│       │   ├── server.go
│       │   ├── dependencies.go
│       │   └── routes.go
│       └── main.go                # Lean application entry point
├── internal/
│   ├── app/                       # Application layer (use cases, services)
│   │   ├── account/
│   │   ├── organization/
│   │   ├── billing/               # <-- Populated in this phase
│   │   ├── configuration/
│   │   └── notification/
│   ├── domain/                    # Domain layer (entities, value objects, core business logic)
│   │   ├── account/
│   │   ├── organization/
│   │   ├── billing/               # <-- Populated in this phase
│   │   ├── configuration/
│   │   └── notification/
│   ├── ports/                     # Presentation Layer (Handlers)
│   │   └── rest/
│   │       ├── openapi/
│   │       ├── dashboard/
│   │       ├── callback/          # <-- Callbacks for billing will be here
│   │       └── ...
│   └── infrastructure/            # Infrastructure layer (external concerns)
│       ├── database/              # PostgreSQL repository implementations
│       ├── provider/              # Adapters for the gateway packages
│       ├── cache/                 # Redis implementation
│       ├── eventbus/              # Domain Event Bus implementation
│       └── locking/               # Distributed locking (e.g., Redis-based)
├── pkg/
│   ├── gateways/                  # External, reusable gateway packages
│   │   ├── xendit/
│   │   │   ├── client.go
│   │   │   └── go.mod
│   │   └── nicepay/
│   │       ├── client.go
│   │       └── go.mod
│   └── ... (other shared libraries)
├── api/                           # API definitions (e.g., OpenAPI/Swagger specs)
├── db/
│   └── migrations/                # Database migration .sql files
├── docs/                          # Project documentation
└── scripts/                       # Scripts for build, deployment, etc.
```

## 3. Key Architectural Decisions (Recap from Phase 1, 2, 3 & 4)

### 3.1. Manual Dependency Injection

To maintain full control over the application's startup process and avoid external dependencies, **manual dependency injection** will be used. All dependencies will be constructed and "wired" together in the `/cmd/api/server` package.

### 3.2. Externalized Gateway Packages

All third-party payment provider integrations (e.g., Xendit, NicePay) will be built as **standalone, reusable Go packages** located in `/pkg/gateways`.

### 3.3. Internationalization (i18n)

The application will support multiple languages, starting with English (en) and Indonesian (id). This will impact user-facing strings in API responses, validation messages, and notifications.

### 3.4. Domain Event Mechanism

The in-process domain event mechanism established in Phase 2 will be utilized for inter-context communication, allowing `billing` to publish events (e.g., `CashInPaidEvent`, `CashOutDisbursedEvent`) that other contexts (e.g., `notification`, `account`, `reconciliation`) can subscribe to. It will also subscribe to events from other contexts (e.g., `OrganizationCreatedEvent` if needed for initial setup).

### 3.5. Centralized Fee Calculation

A dedicated fee calculation service will reside within the `billing` bounded context. This service will interact with the `organization` context (for product, company, partner fees) and `configuration` context (for channel/provider fees) to determine the final applicable fees for transactions.

### 3.6. Event Sourcing for Transaction Status

Transaction status management will leverage an Event Sourcing pattern, as detailed in the comprehensive [`event_sourcing_strategy.md`](event_sourcing_strategy.md) document. Instead of directly updating a status field, events representing state changes (e.g., `TransactionCreated`, `PaymentReceived`, `RefundRequested`) will be appended to an immutable event stream. The current state will be derived from replaying these events or using snapshots for performance.

### 3.7. Reliable Third-Party Notifications (Outbox Pattern)

For critical notifications to external systems (e.g., partners), an Outbox pattern will be implemented. Events triggering notifications will be stored in an `outbox` table within the same database transaction as the domain event, ensuring atomicity. A separate background worker will then reliably send these notifications with retry mechanisms.

## 4. Standardized Libraries (Recap from Phase 1)

The following libraries will be standardized across the project:

*   **Database Driver:** `pgx/v5`
*   **SQL Code Generator:** `sqlc`
*   **HTTP Framework:** `Gin`
*   **Logger:** `Zerolog`
*   **Configuration:** `Koanf`
*   **HTTP Client:** `Resty v2`
*   **Message Broker:** `go-rabbitmq`
*   **Integration Testing:** `testcontainers-go`
*   **Database Migrations:** `golang-migrate/migrate`
*   **Error Reporting:** `sentry-go`
*   **Internationalization:** `go-i18n`

## 5. Coding Conventions & Guidelines (Recap from Phase 1)

Adherence to these guidelines is mandatory for all contributors to ensure a high-quality, consistent, and maintainable codebase.

### 5.1. General Principles & Idiomatic Go
*   **Rule 1.1 (Simplicity - KISS):** Prefer simple, clear solutions over complex ones.
*   **Rule 1.2 (Effective Go):** Follow the principles outlined in the official [Effective Go](https://go.dev/doc/effective_go) document.
*   **Rule 1.3 (Package Naming):** All package names MUST be short-lowercase.
*   **Rule 1.4 (Variable Naming):** All variable names MUST be `camelCase`.
*   **Rule 1.5 (Interfaces):** Define interfaces to decouple components.
*   **Rule 1.6 (Formatting):** All generated Go code MUST be formatted with `gofmt`.

### 5.2. Project Structure & Clean Architecture
*   **Rule 2.1 (Strict Layering):** The dependency direction `API` -> `Application` -> `Domain` MUST be strictly enforced.
*   **Rule 2.2 (Domain Purity):** The `internal/domain` package MUST NOT import any other project packages.
*   **Rule 2.3 (Thin Handlers):** API handlers (`internal/ports/rest`) MUST only parse requests, call a single application service method, and generate a standardized response.

### 5.3. Error Management Protocol
*   **Rule 3.1 (Error Wrapping):** Errors MUST be wrapped with context using `fmt.Errorf("package.function: %w", err)`.
*   **Rule 3.2 (Custom Domain Errors):** Use custom error variables for predictable business rule failures.
*   **Rule 3.3 (Centralized HTTP Error Mapping):** A dedicated Gin error-handling middleware is the only place where errors are translated into HTTP status codes.
*   **Rule 3.4 (Sentry for Unexpected Errors):** Use `webapi.ErrorWithSentry(c, err)` for all 5xx internal server errors.

### 5.4. Standardized API Responses (webapi package)
*   **Rule 4.1 (Mandatory Helpers):** All HTTP responses MUST be generated exclusively using helper functions from the `internal/ports/rest/webapi` package.
*   **Rule 4.2 (Success Structure):** The mandatory JSON structure for a successful response is: `{"status": "success", "data": { /* Actual response data */ }, "correlationId": "UUID_OF_REQUEST"}`.
*   **Rule 4.3 (Error Structure):** The mandatory JSON structure for an error response is: `{"status": "error", "error": {"code": "UNIQUE_ERROR_CODE", "message": "Human-readable error description."}, "correlationId": "UUID_OF_REQUEST"}`.
*   **Rule 4.4 (Helper Signatures and Usage):** Adhere to the defined signatures for `webapi.Success`, `webapi.SuccessPaginated`, `webapi.Error`, and `webapi.ErrorWithSentry`.

### 5.5. Logging Best Practices (zerolog)
*   **Rule 5.1 (Structured Logging):** All logging MUST be done using the global `zerolog` instance.
*   **Rule 5.2 (Contextual Logging):** Every log statement MUST include the `trace_id` from the context.
*   **Rule 5.3 (Error Logging):** When logging an error, the error object MUST be included using `.Err(err)`.

### 5.6. Concurrency and Graceful Shutdown
*   **Rule 6.1 (Panic Safety):** Any new goroutine MUST use a `defer...recover()` block, logging any recovered panic to Sentry.
*   **Rule 6.2 (Graceful Termination):** Any long-running goroutine MUST accept a `context.Context` and listen on `ctx.Done()`.
*   **Rule 6.3 (Server Shutdown):** `main.go` must implement graceful shutdown for the HTTP server.

### 5.7. Gin Middleware
*   **Rule 7.1 (Tracing Middleware):** MUST be the first middleware, generating a `trace_id`.
*   **Rule 7.2 (Logger Middleware):** MUST log request and response details.
*   **Rule 7.3 (Error Middleware):** MUST be the last middleware to centralize error handling.

### 5.8. Database Access (sqlc with pgx/v5)
*   **Rule 8.1 (SQL Files):** All SQL queries MUST be defined in `.sql` files.
*   **Rule 8.2 (sqlc.yaml Configuration):** The `sqlc.yaml` file MUST be configured to use `sql_package: "pgx/v5"` and appropriate type overrides.
*   **Rule 8.3 (Type Conversion):** All conversions between domain types and `pgtype` MUST be handled in a dedicated `internal/infrastructure/database/converters` package.
*   **Rule 8.4 (Transactions):** Repository methods that perform writes MUST accept `pgx.Tx` as a parameter.

### 5.9. Testing and CI/CD Strategy
*   **Rule 9.1 (Containerized Testing):** All integration and end-to-end tests that require external services (like a database or Redis) MUST use `testcontainers-go`.
*   **Rule 9.2 (Seeding Data for Tests):** For every new `.up.sql` migration file, a corresponding test data seed file MUST be created.
*   **Rule 9.3 (Test Coverage):** API handlers, application services, and repositories MUST have dedicated test files.
*   **Rule 9.4 (CI/CD Gate):** A pull request MUST NOT be allowed to merge into the main branch unless all tests (`go test ./...`) pass.

### 5.10. Internationalization (i18n)
*   **Rule 10.1 (Localization Support):** The application MUST support English (en) and Indonesian (id) locales.

## 6. Database Schemas for Phase 5

This phase will introduce and refine the `billing` related schemas, including event sourcing tables.

### 6.1. Payment Transaction Schemas (`billing` Bounded Context)

#### `cash_in_transactions` Table:
```sql
CREATE TABLE cash_in_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id),
    company_product_id UUID REFERENCES company_products(id),
    invoice_number VARCHAR(255) UNIQUE NOT NULL,
    payment_at BIGINT, -- Unix timestamp of payment
    manual_payment_at BIGINT, -- Unix timestamp if manually marked as paid
    customer_name VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(255) NOT NULL,
    customer_email VARCHAR(255) NOT NULL,
    payment_provider_id UUID NOT NULL REFERENCES payment_providers(id),
    payment_channel_id UUID NOT NULL REFERENCES payment_channels(id),
    total DECIMAL(18,2) NOT NULL,
    admin_fee DECIMAL(10,2) DEFAULT 0,
    discount DECIMAL(10,2) DEFAULT 0,
    voucher DECIMAL(10,2) DEFAULT 0,
    pg_delivery_fee DECIMAL(10,2) DEFAULT 0,
    payment_status VARCHAR(50) NOT NULL, -- Derived from events
    status VARCHAR(50) NOT NULL, -- Derived from events
    product_fee DECIMAL(10,2) DEFAULT 0,
    company_product_fee DECIMAL(10,2) DEFAULT 0,
    pg_reference_information TEXT, -- JSONB for provider-specific data
    expired_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### `cash_in_transaction_items` Table:
```sql
CREATE TABLE cash_in_transaction_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    cash_in_transaction_id UUID NOT NULL REFERENCES cash_in_transactions(id),
    ref_invoice_number VARCHAR(255),
    partner_id UUID NOT NULL REFERENCES partners(id),
    item_name VARCHAR(255) NOT NULL,
    amount DECIMAL(18,2) NOT NULL,
    status VARCHAR(50) NOT NULL, -- Derived from events
    note TEXT,
    is_cashout BOOLEAN NOT NULL DEFAULT FALSE,
    sla_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### `cash_in_transaction_events` Table (New - for Event Sourcing):
```sql
CREATE TABLE cash_in_transaction_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id UUID NOT NULL REFERENCES cash_in_transactions(id),
    event_type VARCHAR(255) NOT NULL, -- e.g., 'CREATED', 'PAID', 'EXPIRED', 'REFUND_REQUESTED'
    payload JSONB, -- Contains event-specific data (e.g., payment gateway callback)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### `cash_in_transaction_snapshots` Table (New - for Event Sourcing Performance):
```sql
CREATE TABLE cash_in_transaction_snapshots (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id UUID NOT NULL UNIQUE REFERENCES cash_in_transactions(id),
    state JSONB NOT NULL, -- Snapshot of the transaction's state
    last_event_id UUID NOT NULL REFERENCES cash_in_transaction_events(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### `cash_out_transactions` Table:
```sql
CREATE TABLE cash_out_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id),
    partner_id UUID NOT NULL REFERENCES partners(id),
    payment_provider_id UUID NOT NULL REFERENCES payment_providers(id),
    payment_channel_id UUID NOT NULL REFERENCES payment_channels(id),
    company_product_id UUID REFERENCES company_products(id),
    batch_number VARCHAR(255) UNIQUE NOT NULL,
    partner_name VARCHAR(255) NOT NULL,
    partner_bank_name VARCHAR(255) NOT NULL,
    partner_account_number VARCHAR(255) NOT NULL,
    partner_account_name VARCHAR(255) NOT NULL,
    total DECIMAL(18,2) NOT NULL,
    platform_fee DECIMAL(10,2) DEFAULT 0,
    admin_fee DECIMAL(10,2) DEFAULT 0,
    payment_at BIGINT, -- Unix timestamp of payment
    pg_reference_information TEXT, -- JSONB for provider-specific data
    disbursement_schedule_at BIGINT, -- Unix timestamp for scheduled disbursement
    status VARCHAR(50) NOT NULL, -- Derived from events
    requested_by UUID REFERENCES users(id),
    requested_at TIMESTAMP WITH TIME ZONE,
    approved_by UUID REFERENCES users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    fee_payment_status BOOLEAN NOT NULL DEFAULT FALSE,
    fee_payment_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### `cash_out_transaction_details` Table:
```sql
CREATE TABLE cash_out_transaction_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    cash_out_transaction_id UUID NOT NULL REFERENCES cash_out_transactions(id),
    cash_in_transaction_item_id UUID NOT NULL REFERENCES cash_in_transaction_items(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(cash_out_transaction_id, cash_in_transaction_item_id)
);
```

#### `cash_out_transaction_events` Table (New - for Event Sourcing):
```sql
CREATE TABLE cash_out_transaction_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id UUID NOT NULL REFERENCES cash_out_transactions(id),
    event_type VARCHAR(255) NOT NULL, -- e.g., 'REQUESTED', 'APPROVED', 'DISBURSED', 'FAILED'
    payload JSONB, -- Contains event-specific data (e.g., PG callback data, rejection reason)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID -- User ID or System ID that triggered the event
);
```

#### `cash_out_transaction_snapshots` Table (New - for Event Sourcing Performance):
```sql
CREATE TABLE cash_out_transaction_snapshots (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    transaction_id UUID NOT NULL UNIQUE REFERENCES cash_out_transactions(id),
    state JSONB NOT NULL, -- Snapshot of the transaction's state
    last_event_id UUID NOT NULL REFERENCES cash_out_transaction_events(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### `company_cash_flows` Table:
```sql
CREATE TABLE company_cash_flows (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id),
    cash_in_transaction_id UUID REFERENCES cash_in_transactions(id),
    cash_out_transaction_id UUID REFERENCES cash_out_transactions(id),
    debit DECIMAL(18,2) DEFAULT 0,
    credit DECIMAL(18,2) DEFAULT 0,
    description TEXT,
    additional_information JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### `outbox` Table (New - for Reliable Third-Party Notifications):
```sql
CREATE TABLE outbox (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    aggregate_type VARCHAR(255) NOT NULL, -- e.g., 'CashInTransaction', 'CashOutTransaction'
    aggregate_id UUID NOT NULL,
    event_type VARCHAR(255) NOT NULL, -- e.g., 'CashInPaid', 'CashOutDisbursed'
    payload JSONB NOT NULL, -- The event payload to be sent to third-party
    status VARCHAR(50) NOT NULL DEFAULT 'PENDING', -- PENDING, SENT, FAILED, RETRYING
    last_attempt_at TIMESTAMP WITH TIME ZONE,
    next_attempt_at TIMESTAMP WITH TIME ZONE,
    attempt_count INT DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

## 7. Detailed Tasks for Phase 5

### 7.1. Billing Domain & Repository Implementation
*   **Task 7.1.1: Define Billing Domain Entities**
    *   Create `internal/domain/billing/cash_in.go`, `cash_out.go`, `cash_flow.go`, `transaction_event.go`.
    *   Define structs for `CashInTransaction`, `CashInTransactionItem`, `CashOutTransaction`, `CashOutTransactionDetail`, `CompanyCashFlow`.
    *   Define structs for `CashInTransactionEvent`, `CashOutTransactionEvent`, `OutboxMessage`.
*   **Task 7.1.2: Update Database Migrations**
    *   Create a new migration `db/migrations/000005_billing_tables.up.sql` to define all `billing` related tables, including `cash_in_transaction_events`, `cash_in_transaction_snapshots`, `cash_out_transaction_events`, `cash_out_transaction_snapshots`, and `outbox`. Ensure UUIDs are used for primary keys.
    *   Create corresponding `000005_billing_tables.down.sql`.
*   **Task 7.1.3: Implement Billing Repositories**
    *   Create `internal/infrastructure/database/billing/cash_in_repository.go`, `cash_out_repository.go`, `cash_flow_repository.go`, `outbox_repository.go`.
    *   Implement repository interfaces for all `billing` entities using `sqlc`-generated code.
    *   Ensure all `pgtype` conversions are handled by `internal/infrastructure/database/converters`.

### 7.2. Billing Application Use Cases
*   **Task 7.2.1: Implement Centralized Fee Calculation Service**
    *   Create `internal/app/billing/fee_calculator.go`.
    *   Implement `CalculateFeesForTransaction` method that interacts with `organization` and `configuration` contexts to determine fees.
*   **Task 7.2.2: Implement Payment Gateway Adapters**
    *   Define `PaymentGateway` and `DisbursementGateway` interfaces in `internal/app/billing/gateway.go`.
    *   Create concrete implementations (adapters) for each payment provider (e.g., Xendit, NicePay) in `internal/infrastructure/provider`. These adapters will use the external `pkg/gateways` libraries.
*   **Task 7.2.3: Implement Cash-In Service**
    *   Create `internal/app/billing/cash_in_service.go`.
    *   Implement methods for `CreateCashInTransaction`, `UpdateCashInStatus` (using event sourcing), `ProcessCashInCallback`, `RefundCashIn`.
    *   Publish `CashInCreatedEvent`, `CashInPaidEvent`, `CashInRefundedEvent`, `CashInFailedEvent` using `EventPublisher`.
    *   Store `CashInPaidEvent` in `outbox` for reliable third-party notifications.
*   **Task 7.2.4: Implement Cash-Out Service**
    *   Create `internal/app/billing/cash_out_service.go`.
    *   Implement methods for `RequestDisbursement`, `ProcessDisbursement` (using event sourcing), `ProcessCashOutCallback`.
    *   Publish `CashOutRequestedEvent`, `CashOutApprovedEvent`, `CashOutDisbursedEvent`, `CashOutFailedEvent` using `EventPublisher`.
    *   Store `CashOutDisbursedEvent` in `outbox` for reliable third-party notifications.
*   **Task 7.2.5: Implement Outbox Processor**
    *   Create a background worker (e.g., a Go routine managed by `cmd/api/main.go`) that polls the `outbox` table and sends notifications to third-party systems with retry logic.

### 7.3. Billing Presentation Layer (API)
*   **Task 7.3.1: Implement Cash-In Handlers**
    *   Create `internal/ports/rest/openapi/cash_in_handler.go` and `internal/ports/rest/callback/cash_in_callback_handler.go`.
    *   Implement handlers for `POST /cashin`, `GET /cashin/{id}`, and various payment gateway callbacks (e.g., `/callbacks/xendit/va`).
*   **Task 7.3.2: Implement Cash-Out Handlers**
    *   Create `internal/ports/rest/dashboard/cash_out_handler.go` and `internal/ports/rest/callback/cash_out_callback_handler.go`.
    *   Implement handlers for `POST /cashout`, `GET /cashout/{id}`, and various disbursement callbacks.
*   **Task 7.3.3: Update Main Application Wiring**
    *   Modify `cmd/api/server/dependencies.go` to wire up the new `Billing` repositories and services, including payment gateway adapters and the `FeeCalculator`.
    *   Modify `cmd/api/server/routes.go` to define the new `billing` endpoints and callbacks, ensuring proper authentication and authorization middleware is applied.
    *   Register `billing` event handlers (if any) and start the `OutboxProcessor`.

### 7.4. Testing and Validation
*   **Task 7.4.1: Update Seed Data**
    *   Create `testdata/seeds/000005_seed_billing_data.sql` with sample data for all `billing` tables, including initial transaction events.
*   **Task 7.4.2: Implement Unit Tests**
    *   Write unit tests for `internal/app/billing` services (mocking repositories, payment gateway adapters, and `EventPublisher`).
    *   Write unit tests for `fee_calculator.go`.
*   **Task 7.4.3: Implement Integration Tests**
    *   Write integration tests for `internal/infrastructure/database/billing` repositories using `testcontainers-go` for PostgreSQL.
    *   Write integration tests for `internal/app/billing` services using `testcontainers-go` for PostgreSQL and mocked external services.
*   **Task 7.4.4: Implement End-to-End Tests**
    *   Write E2E tests for Cash-In and Cash-Out API endpoints using `SetupTestServer()` from Phase 1.
    *   Test full transaction lifecycles, including callbacks and refunds.
    *   Verify correct fee calculation.
    *   Verify that relevant domain events are published and `outbox` entries are created.
*   **Task 7.4.5: Update Postman Collection**
    *   Create `docs/postman/collections/billing.postman_collection.json` with requests for all new `billing` endpoints.

## 8. Phase 5 Deliverables

*   Fully implemented `billing` bounded context with core Cash-In and Cash-Out transaction functionalities.
*   Updated database schemas for all `billing` related tables, including event sourcing and outbox tables.
*   Centralized fee calculation service.
*   Abstracted payment gateway interactions.
*   Event sourcing implemented for transaction status management.
*   Reliable third-party notifications via Outbox pattern.
*   Publishing of relevant domain events for `billing` changes.
*   Comprehensive unit, integration, and E2E tests for the `billing` context.
*   Updated Postman collection for `billing` endpoints.
*   All code adhering to the defined coding guidelines.

## 9. Phase 5 Diagram:

```mermaid
graph TD
    subgraph "Phase 5: Billing Bounded Context Implementation"
        A[7.1. Billing Domain & Repository] --> B(7.2. Billing Application Use Cases)
        B --> C(7.3. Billing Presentation Layer)
        C --> D(7.4. Testing and Validation)
        A --> D
        B --> D
        C --> D
    end

    subgraph "7.1. Billing Domain & Repository"
        A1[7.1.1: Define Billing Domain Entities]
        A2[7.1.2: Update Database Migrations]
        A3[7.1.3: Implement Billing Repositories]
    end

    subgraph "7.2. Billing Application Use Cases"
        B1[7.2.1: Implement Centralized Fee Calculation Service]
        B2[7.2.2: Implement Payment Gateway Adapters]
        B3[7.2.3: Implement Cash-In Service]
        B4[7.2.4: Implement Cash-Out Service]
        B5[7.2.5: Implement Outbox Processor]
    end

    subgraph "7.3. Billing Presentation Layer (API)"
        C1[7.3.1: Implement Cash-In Handlers]
        C2[7.3.2: Implement Cash-Out Handlers]
        C3[7.3.3: Update Main Application Wiring]
    end

    subgraph "7.4. Testing and Validation"
        D1[7.4.1: Update Seed Data]
        D2[7.4.2: Implement Unit Tests]
        D3[7.4.3: Implement Integration Tests]
        D4[7.4.4: Implement End-to-End Tests]
        D5[7.4.5: Update Postman Collection]
    end

    style A1 fill:#f9f,stroke:#333,stroke-width:2px
    style A2 fill:#f9f,stroke:#333,stroke-width:2px
    style A3 fill:#f9f,stroke:#333,stroke-width:2px

    style B1 fill:#bbf,stroke:#333,stroke-width:2px
    style B2 fill:#bbf,stroke:#333,stroke-width:2px
    style B3 fill:#bbf,stroke:#333,stroke-width:2px
    style B4 fill:#bbf,stroke:#333,stroke-width:2px
    style B5 fill:#bbf,stroke:#333,stroke-width:2px

    style C1 fill:#ccf,stroke:#333,stroke-width:2px
    style C2 fill:#ccf,stroke:#333,stroke-width:2px
    style C3 fill:#ccf,stroke:#333,stroke-width:2px

    style D1 fill:#ddf,stroke:#333,stroke-width:2px
    style D2 fill:#ddf,stroke:#333,stroke-width:2px
    style D3 fill:#ddf,stroke:#333,stroke-width:2px
    style D4 fill:#ddf,stroke:#333,stroke-width:2px
    style D5 fill:#ddf,stroke:#333,stroke-width:2px

## 10. Phase Completion Checklist

This checklist serves as a verification tool to ensure all tasks, requirements, rules, and guidelines for Phase 5 have been successfully completed and adhered to. Before marking this phase as complete, all items below must be checked off.

### 10.1. General Completion & Quality
*   [ ] All tasks outlined in Section 7 ("Detailed Tasks for Phase 5") have been implemented.
*   [ ] The project builds successfully without errors (`go build ./...`).
*   [ ] All tests pass (`go test ./...`).
*   [ ] Code has been formatted with `gofmt` (`gofmt -s -w .`).
*   [ ] No new warnings or errors are introduced by linters (e.g., `golangci-lint run`).
*   [ ] All dependencies are properly managed (`go mod tidy`).

### 10.2. Architectural Adherence
*   [ ] The `internal/app/billing` and `internal/domain/billing` directories are populated as per the target structure.
*   [ ] Dependencies within the `billing` context strictly follow the Clean Architecture rule.
*   [ ] Domain Event Mechanism is correctly utilized for publishing events from the `billing` context.
*   [ ] Integration with `organization` and `configuration` contexts is functional.

### 10.3. Core Functionality Verification
*   [ ] Core functionalities for Cash-In transactions (creation, status updates, callbacks, refunds) are functional.
*   [ ] Core functionalities for Cash-Out transactions (disbursement requests, processing, callbacks) are functional.
*   [ ] Centralized fee calculation logic is implemented and functional.
*   [ ] Payment gateway interactions are abstracted behind common interfaces.
*   [ ] Event sourcing is implemented for transaction status management.
*   [ ] Reliable third-party notifications via Outbox pattern are implemented.
*   [ ] Relevant domain events (`CashInPaidEvent`, `CashOutDisbursedEvent`) are published.
*   [ ] API endpoints for Cash-In and Cash-Out transactions and their callbacks are functional.

### 10.4. Testing & Validation
*   [ ] All unit tests for `billing` services are passing.
*   [ ] All integration tests for `billing` repositories and services are passing.
*   [ ] All E2E tests for `billing` API endpoints are passing, covering full transaction lifecycles.
*   [ ] E2E tests verify correct fee calculation.
*   [ ] E2E tests verify that relevant domain events are published and `outbox` entries are created.
*   [ ] Postman collection for `billing` endpoints is updated and functional.
*   [ ] Seed data for `billing` tables is correctly applied in tests.

### 10.5. Coding Guidelines Compliance (Self-Assessment)
*   [ ] **Rule 1 (General):** `gofmt`, `camelCase`, short-lowercase package names, interface-based design are followed.
*   [ ] **Rule 2 (Architecture):** `API` -> `Application` -> `Domain` dependency rule and thin handlers are enforced.
*   [ ] **Rule 3 (Error Handling):** Error wrapping, custom errors, centralized mapping, and Sentry integration are used.
*   [ ] **Rule 4 (API Responses):** `webapi` helpers are used for all responses, and error messages are localized.
*   [ ] **Rule 5 (Logging):** Structured, contextual logging with `zerolog` is implemented.
*   [ ] **Rule 6 (Concurrency):** Panic safety and graceful termination are considered.
*   [ ] **Rule 7 (Middleware):** The exact middleware order is followed.
*   [ ] **Rule 8 (Database):** `.sql` files for `sqlc`, `pgx/v5` configuration, centralized type converters, and transactional repositories are used.
*   [ ] **Rule 9 (Testing & CI/CD):** `testcontainers-go`, DDL/DML separation, and test coverage are adhered to.
*   [ ] **Rule 10 (Internationalization):** Localization support is correctly implemented.
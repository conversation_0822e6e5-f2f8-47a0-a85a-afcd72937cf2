package webapi

import (
	"context"
	"net/http"
	"time"

	"github.com/getsentry/sentry-go"
	"github.com/gin-gonic/gin"

	"github.com/wongpinter/payment-gateway/internal/infrastructure/logger"
)

// Response represents the standard API response structure
// This adheres to Rule 4.1 (Standardized API Responses)
type Response struct {
	Success       bool        `json:"success"`
	Message       string      `json:"message"`
	Data          interface{} `json:"data,omitempty"`
	Error         *ErrorInfo  `json:"error,omitempty"`
	Meta          *Meta       `json:"meta,omitempty"`
	CorrelationID string      `json:"correlation_id"`
	Timestamp     time.Time   `json:"timestamp"`
}

// ErrorInfo represents error information in API responses
type ErrorInfo struct {
	Code     string      `json:"code"`
	Message  string      `json:"message"`
	Details  interface{} `json:"details,omitempty"`
	SentryID string      `json:"sentry_id,omitempty"`
}

// Meta represents metadata for paginated responses
type Meta struct {
	Page       int   `json:"page"`
	PerPage    int   `json:"per_page"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
}

// PaginationParams represents pagination parameters
type PaginationParams struct {
	Page    int `form:"page" binding:"min=1"`
	PerPage int `form:"per_page" binding:"min=1,max=100"`
}

// GetDefaultPagination returns default pagination parameters
func GetDefaultPagination() PaginationParams {
	return PaginationParams{
		Page:    1,
		PerPage: 20,
	}
}

// CalculateTotalPages calculates total pages based on total records and per page
func CalculateTotalPages(total int64, perPage int) int {
	if perPage <= 0 {
		return 0
	}
	return int((total + int64(perPage) - 1) / int64(perPage))
}

// getCorrelationID extracts correlation ID from context or generates a new one
func getCorrelationID(ctx context.Context) string {
	if correlationID, ok := ctx.Value(logger.CorrelationIDKey).(string); ok && correlationID != "" {
		return correlationID
	}
	// Fallback: generate a new correlation ID if not found
	return generateCorrelationID()
}

// generateCorrelationID generates a new correlation ID
func generateCorrelationID() string {
	// Use a simple timestamp-based ID for now
	// In production, you might want to use UUID or other unique identifier
	return time.Now().Format("20060102150405.000000")
}

// Success sends a successful response with data
// This function adheres to Rule 4.1 (Standardized API Responses)
func Success(c *gin.Context, message string, data interface{}) {
	ctx := c.Request.Context()
	correlationID := getCorrelationID(ctx)

	response := Response{
		Success:       true,
		Message:       message,
		Data:          data,
		CorrelationID: correlationID,
		Timestamp:     time.Now(),
	}

	// Set correlation ID in response header
	c.Header("X-Correlation-ID", correlationID)

	logger.LogInfo(ctx, "API response sent successfully",
		"status_code", http.StatusOK,
		"message", message,
		"correlation_id", correlationID,
	)

	c.JSON(http.StatusOK, response)
}

// SuccessWithStatus sends a successful response with custom status code and data
// This function adheres to Rule 4.1 (Standardized API Responses)
func SuccessWithStatus(c *gin.Context, statusCode int, message string, data interface{}) {
	ctx := c.Request.Context()
	correlationID := getCorrelationID(ctx)

	response := Response{
		Success:       true,
		Message:       message,
		Data:          data,
		CorrelationID: correlationID,
		Timestamp:     time.Now(),
	}

	// Set correlation ID in response header
	c.Header("X-Correlation-ID", correlationID)

	logger.LogInfo(ctx, "API response sent successfully",
		"status_code", statusCode,
		"message", message,
		"correlation_id", correlationID,
	)

	c.JSON(statusCode, response)
}

// SuccessPaginated sends a successful paginated response
// This function adheres to Rule 4.1 (Standardized API Responses)
func SuccessPaginated(c *gin.Context, message string, data interface{}, pagination PaginationParams, total int64) {
	ctx := c.Request.Context()
	correlationID := getCorrelationID(ctx)

	meta := &Meta{
		Page:       pagination.Page,
		PerPage:    pagination.PerPage,
		Total:      total,
		TotalPages: CalculateTotalPages(total, pagination.PerPage),
	}

	response := Response{
		Success:       true,
		Message:       message,
		Data:          data,
		Meta:          meta,
		CorrelationID: correlationID,
		Timestamp:     time.Now(),
	}

	// Set correlation ID in response header
	c.Header("X-Correlation-ID", correlationID)

	logger.LogInfo(ctx, "API paginated response sent successfully",
		"status_code", http.StatusOK,
		"message", message,
		"page", pagination.Page,
		"per_page", pagination.PerPage,
		"total", total,
		"correlation_id", correlationID,
	)

	c.JSON(http.StatusOK, response)
}

// Error sends an error response
// This function adheres to Rule 4.1 (Standardized API Responses)
func Error(c *gin.Context, statusCode int, errorCode, message string, details interface{}) {
	ctx := c.Request.Context()
	correlationID := getCorrelationID(ctx)

	errorInfo := &ErrorInfo{
		Code:    errorCode,
		Message: message,
		Details: details,
	}

	response := Response{
		Success:       false,
		Message:       "Request failed",
		Error:         errorInfo,
		CorrelationID: correlationID,
		Timestamp:     time.Now(),
	}

	// Set correlation ID in response header
	c.Header("X-Correlation-ID", correlationID)

	logger.LogError(ctx, nil, "API error response sent",
		"status_code", statusCode,
		"error_code", errorCode,
		"message", message,
		"correlation_id", correlationID,
	)

	c.JSON(statusCode, response)
}

// ErrorWithSentry sends an error response and reports to Sentry
// This function adheres to Rule 4.1 (Standardized API Responses)
func ErrorWithSentry(c *gin.Context, statusCode int, errorCode, message string, err error, details interface{}) {
	ctx := c.Request.Context()
	correlationID := getCorrelationID(ctx)

	// Report to Sentry
	var sentryID string
	if err != nil {
		// Configure Sentry scope with additional context
		sentry.WithScope(func(scope *sentry.Scope) {
			scope.SetTag("correlation_id", correlationID)
			scope.SetTag("error_code", errorCode)
			scope.SetContext("request", map[string]interface{}{
				"method":         c.Request.Method,
				"url":            c.Request.URL.String(),
				"user_agent":     c.Request.UserAgent(),
				"remote_addr":    c.ClientIP(),
				"correlation_id": correlationID,
			})

			if details != nil {
				scope.SetExtra("error_details", details)
			}

			eventID := sentry.CaptureException(err)
			if eventID != nil {
				sentryID = string(*eventID)
			}
		})
	}

	errorInfo := &ErrorInfo{
		Code:     errorCode,
		Message:  message,
		Details:  details,
		SentryID: sentryID,
	}

	response := Response{
		Success:       false,
		Message:       "Request failed",
		Error:         errorInfo,
		CorrelationID: correlationID,
		Timestamp:     time.Now(),
	}

	// Set correlation ID in response header
	c.Header("X-Correlation-ID", correlationID)

	logger.LogError(ctx, err, "API error response sent with Sentry reporting",
		"status_code", statusCode,
		"error_code", errorCode,
		"message", message,
		"sentry_id", sentryID,
		"correlation_id", correlationID,
	)

	c.JSON(statusCode, response)
}

// ValidationError sends a validation error response
func ValidationError(c *gin.Context, details interface{}) {
	Error(c, http.StatusBadRequest, "VALIDATION_ERROR", "Validation failed", details)
}

// NotFound sends a not found error response
func NotFound(c *gin.Context, resource string) {
	Error(c, http.StatusNotFound, "NOT_FOUND", resource+" not found", nil)
}

// Unauthorized sends an unauthorized error response
func Unauthorized(c *gin.Context, message string) {
	if message == "" {
		message = "Unauthorized access"
	}
	Error(c, http.StatusUnauthorized, "UNAUTHORIZED", message, nil)
}

// Forbidden sends a forbidden error response
func Forbidden(c *gin.Context, message string) {
	if message == "" {
		message = "Access forbidden"
	}
	Error(c, http.StatusForbidden, "FORBIDDEN", message, nil)
}

// InternalServerError sends an internal server error response with Sentry reporting
func InternalServerError(c *gin.Context, err error) {
	ErrorWithSentry(c, http.StatusInternalServerError, "INTERNAL_ERROR", "Internal server error", err, nil)
}

// BadRequest sends a bad request error response
func BadRequest(c *gin.Context, message string, details interface{}) {
	if message == "" {
		message = "Bad request"
	}
	Error(c, http.StatusBadRequest, "BAD_REQUEST", message, details)
}

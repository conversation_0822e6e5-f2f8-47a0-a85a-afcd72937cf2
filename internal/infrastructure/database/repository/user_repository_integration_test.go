package repository

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"os"
	"path/filepath"

	"github.com/wongpinter/payment-gateway/internal/domain/account"
	testingpkg "github.com/wongpinter/payment-gateway/internal/infrastructure/testing"
)

// findTestDataPath finds the testdata directory by looking for go.mod
func findTestDataPath() (string, error) {
	// Start from current directory and walk up to find go.mod
	currentDir, err := os.Getwd()
	if err != nil {
		return "", fmt.Errorf("failed to get current directory: %w", err)
	}

	// Walk up the directory tree to find go.mod
	for {
		goModPath := filepath.Join(currentDir, "go.mod")
		if _, err := os.Stat(goModPath); err == nil {
			// Found go.mod, testdata should be in testdata/seeds
			testDataPath := filepath.Join(currentDir, "testdata", "seeds")
			if _, err := os.Stat(testDataPath); err == nil {
				return testDataPath, nil
			}
			return "", fmt.Errorf("testdata directory not found at %s", testDataPath)
		}

		// Move up one directory
		parentDir := filepath.Dir(currentDir)
		if parentDir == currentDir {
			// Reached root directory
			break
		}
		currentDir = parentDir
	}

	return "", fmt.Errorf("could not find project root (go.mod not found)")
}

func TestUserRepository_Integration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	ctx := context.Background()

	// Initialize test containers
	err := testingpkg.InitTestContainers(ctx)
	require.NoError(t, err)
	defer func() {
		if err := testingpkg.CleanupTestContainers(); err != nil {
			t.Logf("Failed to cleanup test containers: %v", err)
		}
	}()

	// Setup test database
	dbSetup, err := testingpkg.NewTestDatabaseSetup(ctx, testingpkg.GetTestDatabaseConfig())
	require.NoError(t, err)
	defer dbSetup.Cleanup(ctx)

	// Create repository
	repo := NewUserRepository(dbSetup.DB.Pool)

	t.Run("Create and GetByID", func(t *testing.T) {
		// Create test user
		email, err := account.NewEmail("<EMAIL>")
		require.NoError(t, err)

		passwordHash := account.NewPasswordHash("hashedpassword")
		user := account.NewUser(email, passwordHash, "Test User")

		// Create user
		err = repo.Create(ctx, user)
		require.NoError(t, err)

		// Retrieve user by ID
		retrievedUser, err := repo.GetByID(ctx, user.ID())
		require.NoError(t, err)
		assert.Equal(t, user.ID().String(), retrievedUser.ID().String())
		assert.Equal(t, email.String(), retrievedUser.Email().String())
		assert.Equal(t, "Test User", retrievedUser.Name())
		assert.True(t, retrievedUser.IsActive())
	})

	t.Run("GetByEmail", func(t *testing.T) {
		// Create test user
		email, err := account.NewEmail("<EMAIL>")
		require.NoError(t, err)

		passwordHash := account.NewPasswordHash("hashedpassword")
		user := account.NewUser(email, passwordHash, "Email Test User")

		// Create user
		err = repo.Create(ctx, user)
		require.NoError(t, err)

		// Retrieve user by email
		retrievedUser, err := repo.GetByEmail(ctx, email)
		require.NoError(t, err)
		assert.Equal(t, user.ID().String(), retrievedUser.ID().String())
		assert.Equal(t, email.String(), retrievedUser.Email().String())
		assert.Equal(t, "Email Test User", retrievedUser.Name())
	})

	t.Run("Update", func(t *testing.T) {
		// Create test user
		email, err := account.NewEmail("<EMAIL>")
		require.NoError(t, err)

		passwordHash := account.NewPasswordHash("hashedpassword")
		user := account.NewUser(email, passwordHash, "Original Name")

		// Create user
		err = repo.Create(ctx, user)
		require.NoError(t, err)

		// Update user
		user.UpdateName("Updated Name")
		err = repo.Update(ctx, user)
		require.NoError(t, err)

		// Retrieve and verify update
		retrievedUser, err := repo.GetByID(ctx, user.ID())
		require.NoError(t, err)
		assert.Equal(t, "Updated Name", retrievedUser.Name())
	})

	t.Run("ExistsByEmail", func(t *testing.T) {
		// Create test user
		email, err := account.NewEmail("<EMAIL>")
		require.NoError(t, err)

		passwordHash := account.NewPasswordHash("hashedpassword")
		user := account.NewUser(email, passwordHash, "Exists Test User")

		// Create user
		err = repo.Create(ctx, user)
		require.NoError(t, err)

		// Check if user exists
		exists, err := repo.ExistsByEmail(ctx, email)
		require.NoError(t, err)
		assert.True(t, exists)

		// Check non-existent email
		nonExistentEmail, err := account.NewEmail("<EMAIL>")
		require.NoError(t, err)

		exists, err = repo.ExistsByEmail(ctx, nonExistentEmail)
		require.NoError(t, err)
		assert.False(t, exists)
	})

	t.Run("List and Count", func(t *testing.T) {
		// Create multiple test users
		users := make([]*account.User, 3)
		for i := 0; i < 3; i++ {
			email, err := account.NewEmail(fmt.Sprintf("<EMAIL>", i))
			require.NoError(t, err)

			passwordHash := account.NewPasswordHash("hashedpassword")
			user := account.NewUser(email, passwordHash, fmt.Sprintf("List Test User %d", i))
			users[i] = user

			err = repo.Create(ctx, user)
			require.NoError(t, err)
		}

		// Test List with pagination
		retrievedUsers, err := repo.List(ctx, 0, 2)
		require.NoError(t, err)
		assert.GreaterOrEqual(t, len(retrievedUsers), 2)

		// Test Count
		count, err := repo.Count(ctx)
		require.NoError(t, err)
		assert.GreaterOrEqual(t, count, 3)
	})

	t.Run("Delete", func(t *testing.T) {
		// Create test user
		email, err := account.NewEmail("<EMAIL>")
		require.NoError(t, err)

		passwordHash := account.NewPasswordHash("hashedpassword")
		user := account.NewUser(email, passwordHash, "Delete Test User")

		// Create user
		err = repo.Create(ctx, user)
		require.NoError(t, err)

		// Verify user exists
		_, err = repo.GetByID(ctx, user.ID())
		require.NoError(t, err)

		// Delete user
		err = repo.Delete(ctx, user.ID())
		require.NoError(t, err)

		// Verify user is deleted
		_, err = repo.GetByID(ctx, user.ID())
		assert.ErrorIs(t, err, account.ErrUserNotFound)
	})

	t.Run("Error Cases", func(t *testing.T) {
		// Test GetByID with non-existent ID
		nonExistentID := account.NewUserID()
		_, err := repo.GetByID(ctx, nonExistentID)
		assert.ErrorIs(t, err, account.ErrUserNotFound)

		// Test GetByEmail with non-existent email
		nonExistentEmail, err := account.NewEmail("<EMAIL>")
		require.NoError(t, err)

		_, err = repo.GetByEmail(ctx, nonExistentEmail)
		assert.ErrorIs(t, err, account.ErrUserNotFound)

		// Test Update with non-existent user
		email, err := account.NewEmail("<EMAIL>")
		require.NoError(t, err)

		passwordHash := account.NewPasswordHash("hashedpassword")
		user := account.NewUser(email, passwordHash, "Non-existent User")

		err = repo.Update(ctx, user)
		assert.ErrorIs(t, err, account.ErrUserNotFound)
	})

	t.Run("Duplicate Email", func(t *testing.T) {
		// Create first user
		email, err := account.NewEmail("<EMAIL>")
		require.NoError(t, err)

		passwordHash := account.NewPasswordHash("hashedpassword")
		user1 := account.NewUser(email, passwordHash, "First User")

		err = repo.Create(ctx, user1)
		require.NoError(t, err)

		// Try to create second user with same email
		user2 := account.NewUser(email, passwordHash, "Second User")

		err = repo.Create(ctx, user2)
		assert.Error(t, err) // Should fail due to unique constraint
	})
}

func TestUserRepository_RoleOperations_Integration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	ctx := context.Background()

	// Initialize test containers
	err := testingpkg.InitTestContainers(ctx)
	require.NoError(t, err)
	defer func() {
		if err := testingpkg.CleanupTestContainers(); err != nil {
			t.Logf("Failed to cleanup test containers: %v", err)
		}
	}()

	// Setup test database with seeds
	dbSetup, err := testingpkg.NewTestDatabaseSetup(ctx, testingpkg.GetTestDatabaseConfig())
	require.NoError(t, err)
	defer dbSetup.Cleanup(ctx)

	// Load seed data for roles - find the correct testdata path
	seedPath, err := findTestDataPath()
	require.NoError(t, err)

	seedSetup := testingpkg.NewTestSeedSetup(dbSetup.DB, seedPath)
	err = seedSetup.LoadBasicSeeds(ctx)
	require.NoError(t, err)

	// Create repositories
	userRepo := NewUserRepository(dbSetup.DB.Pool)
	roleRepo := NewRoleRepository(dbSetup.DB.Pool)

	t.Run("AssignRole and GetUserRoles", func(t *testing.T) {
		// Create test user
		email, err := account.NewEmail("<EMAIL>")
		require.NoError(t, err)

		passwordHash := account.NewPasswordHash("hashedpassword")
		user := account.NewUser(email, passwordHash, "Role Test User")

		err = userRepo.Create(ctx, user)
		require.NoError(t, err)

		// Get a role from seed data
		role, err := roleRepo.GetByName(ctx, "Regular User")
		require.NoError(t, err)

		// Assign role to user
		err = userRepo.AssignRole(ctx, user.ID(), role.ID())
		require.NoError(t, err)

		// Get user roles
		roles, err := userRepo.GetUserRoles(ctx, user.ID())
		require.NoError(t, err)
		assert.Len(t, roles, 1)
		assert.Equal(t, role.ID().String(), roles[0].ID().String())
		assert.Equal(t, "Regular User", roles[0].Name())
	})

	t.Run("RevokeRole", func(t *testing.T) {
		// Create test user
		email, err := account.NewEmail("<EMAIL>")
		require.NoError(t, err)

		passwordHash := account.NewPasswordHash("hashedpassword")
		user := account.NewUser(email, passwordHash, "Revoke Test User")

		err = userRepo.Create(ctx, user)
		require.NoError(t, err)

		// Get a role from seed data
		role, err := roleRepo.GetByName(ctx, "Regular User")
		require.NoError(t, err)

		// Assign role to user
		err = userRepo.AssignRole(ctx, user.ID(), role.ID())
		require.NoError(t, err)

		// Verify role is assigned
		roles, err := userRepo.GetUserRoles(ctx, user.ID())
		require.NoError(t, err)
		assert.Len(t, roles, 1)

		// Revoke role from user
		err = userRepo.RevokeRole(ctx, user.ID(), role.ID())
		require.NoError(t, err)

		// Verify role is revoked
		roles, err = userRepo.GetUserRoles(ctx, user.ID())
		require.NoError(t, err)
		assert.Len(t, roles, 0)
	})

	t.Run("GetUsersWithRole", func(t *testing.T) {
		// Get a role from seed data
		role, err := roleRepo.GetByName(ctx, "admin")
		require.NoError(t, err)

		// Create multiple test users
		userIDs := make([]account.UserID, 2)
		for i := 0; i < 2; i++ {
			email, err := account.NewEmail(fmt.Sprintf("<EMAIL>", i))
			require.NoError(t, err)

			passwordHash := account.NewPasswordHash("hashedpassword")
			user := account.NewUser(email, passwordHash, fmt.Sprintf("User With Role %d", i))
			userIDs[i] = user.ID()

			err = userRepo.Create(ctx, user)
			require.NoError(t, err)

			// Assign role to user
			err = userRepo.AssignRole(ctx, user.ID(), role.ID())
			require.NoError(t, err)
		}

		// Get users with role
		users, err := userRepo.GetUsersWithRole(ctx, role.ID())
		require.NoError(t, err)
		assert.GreaterOrEqual(t, len(users), 2) // At least our 2 test users, might have more from seeds

		// Verify our test users are in the result
		userIDStrings := make([]string, len(users))
		for i, user := range users {
			userIDStrings[i] = user.ID().String()
		}

		for _, userID := range userIDs {
			assert.Contains(t, userIDStrings, userID.String())
		}
	})
}

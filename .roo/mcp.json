{"mcpServers": {"postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "postgres://payment:payment_dev_password@localhost:5432/payment"]}, "git": {"command": "uvx", "args": ["mcp-server-git", "--repository", "/home/<USER>/Workspaces/go/wongpinter/payment-gateway"], "disabled": false, "alwaysAllow": []}, "exa": {"command": "npx", "args": ["-y", "exa-mcp-server"], "env": {"EXA_API_KEY": "88cf2020-39d2-4de3-a00e-624c340cfd78"}}}}
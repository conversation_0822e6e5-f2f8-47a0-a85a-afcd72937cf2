package service

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/wongpinter/payment-gateway/internal/domain/account"
)

func TestUserService_RegisterUser(t *testing.T) {
	t.Run("successful user registration", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}

		// Create service
		userService := NewUserService(userRepo, roleRepo, passwordHasher, eventPublisher)

		// Setup expectations
		userRepo.On("ExistsByEmail", mock.Anything, mock.AnythingOfType("account.Email")).Return(false, nil)
		passwordHasher.On("Hash", "password123").Return("hashed_password", nil)
		userRepo.On("Create", mock.Anything, mock.AnythingOfType("*account.User")).Return(nil)
		eventPublisher.On("Publish", mock.Anything, mock.AnythingOfType("*account.UserRegisteredEvent")).Return(nil)

		// Execute
		req := RegisterUserRequest{
			Email:    "<EMAIL>",
			Password: "password123",
			Name:     "Test User",
		}

		resp, err := userService.RegisterUser(context.Background(), req)

		// Assert
		require.NoError(t, err)
		assert.NotEmpty(t, resp.UserID)
		assert.Equal(t, "<EMAIL>", resp.Email)
		assert.Equal(t, "Test User", resp.Name)
		assert.True(t, resp.IsActive)

		// Verify expectations
		userRepo.AssertExpectations(t)
		passwordHasher.AssertExpectations(t)
		eventPublisher.AssertExpectations(t)
	})

	t.Run("invalid email format", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}

		// Create service
		userService := NewUserService(userRepo, roleRepo, passwordHasher, eventPublisher)

		// Execute
		req := RegisterUserRequest{
			Email:    "invalid-email",
			Password: "password123",
			Name:     "Test User",
		}

		_, err := userService.RegisterUser(context.Background(), req)

		// Assert
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid email format")
	})

	t.Run("email already exists", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}

		// Create service
		userService := NewUserService(userRepo, roleRepo, passwordHasher, eventPublisher)

		// Setup expectations
		userRepo.On("ExistsByEmail", mock.Anything, mock.AnythingOfType("account.Email")).Return(true, nil)

		// Execute
		req := RegisterUserRequest{
			Email:    "<EMAIL>",
			Password: "password123",
			Name:     "Test User",
		}

		_, err := userService.RegisterUser(context.Background(), req)

		// Assert
		assert.Error(t, err)
		assert.Equal(t, account.ErrEmailAlreadyExists, err)

		// Verify expectations
		userRepo.AssertExpectations(t)
	})

	t.Run("password hashing fails", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}

		// Create service
		userService := NewUserService(userRepo, roleRepo, passwordHasher, eventPublisher)

		// Setup expectations
		userRepo.On("ExistsByEmail", mock.Anything, mock.AnythingOfType("account.Email")).Return(false, nil)
		passwordHasher.On("Hash", "password123").Return("", errors.New("hashing failed"))

		// Execute
		req := RegisterUserRequest{
			Email:    "<EMAIL>",
			Password: "password123",
			Name:     "Test User",
		}

		_, err := userService.RegisterUser(context.Background(), req)

		// Assert
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "hashing failed")

		// Verify expectations
		userRepo.AssertExpectations(t)
		passwordHasher.AssertExpectations(t)
	})
}

func TestUserService_GetUserByID(t *testing.T) {
	t.Run("successful user retrieval", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}

		// Create service
		userService := NewUserService(userRepo, roleRepo, passwordHasher, eventPublisher)

		// Setup test data
		userID := account.NewUserID()
		email, _ := account.NewEmail("<EMAIL>")
		passwordHash := account.NewPasswordHash("hashed_password")
		user := account.NewUserWithID(userID, email, passwordHash, "Test User", true, time.Now(), time.Now())

		// Setup expectations
		userRepo.On("GetByID", mock.Anything, userID).Return(user, nil)

		// Execute
		resp, err := userService.GetUserByID(context.Background(), userID.String())

		// Assert
		require.NoError(t, err)
		assert.Equal(t, userID.String(), resp.ID)
		assert.Equal(t, "<EMAIL>", resp.Email)
		assert.Equal(t, "Test User", resp.Name)
		assert.True(t, resp.IsActive)

		// Verify expectations
		userRepo.AssertExpectations(t)
	})

	t.Run("invalid user ID format", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}

		// Create service
		userService := NewUserService(userRepo, roleRepo, passwordHasher, eventPublisher)

		// Execute
		_, err := userService.GetUserByID(context.Background(), "invalid-uuid")

		// Assert
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid user ID format")
	})

	t.Run("user not found", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}

		// Create service
		userService := NewUserService(userRepo, roleRepo, passwordHasher, eventPublisher)

		// Setup test data
		userID := account.NewUserID()

		// Setup expectations
		userRepo.On("GetByID", mock.Anything, userID).Return(nil, account.ErrUserNotFound)

		// Execute
		_, err := userService.GetUserByID(context.Background(), userID.String())

		// Assert
		assert.Error(t, err)
		assert.Equal(t, account.ErrUserNotFound, err)

		// Verify expectations
		userRepo.AssertExpectations(t)
	})
}

func TestUserService_UpdateUser(t *testing.T) {
	t.Run("successful user update", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}

		// Create service
		userService := NewUserService(userRepo, roleRepo, passwordHasher, eventPublisher)

		// Setup test data
		userID := account.NewUserID()
		email, _ := account.NewEmail("<EMAIL>")
		passwordHash := account.NewPasswordHash("hashed_password")
		user := account.NewUserWithID(userID, email, passwordHash, "Old Name", true, time.Now(), time.Now())

		// Setup expectations
		userRepo.On("GetByID", mock.Anything, userID).Return(user, nil)
		userRepo.On("Update", mock.Anything, user).Return(nil)

		// Execute
		req := UpdateUserRequest{
			UserID: userID.String(),
			Name:   "New Name",
		}

		resp, err := userService.UpdateUser(context.Background(), req)

		// Assert
		require.NoError(t, err)
		assert.Equal(t, userID.String(), resp.ID)
		assert.Equal(t, "New Name", resp.Name)

		// Verify expectations
		userRepo.AssertExpectations(t)
	})

	t.Run("user not found", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}

		// Create service
		userService := NewUserService(userRepo, roleRepo, passwordHasher, eventPublisher)

		// Setup test data
		userID := account.NewUserID()

		// Setup expectations
		userRepo.On("GetByID", mock.Anything, userID).Return(nil, account.ErrUserNotFound)

		// Execute
		req := UpdateUserRequest{
			UserID: userID.String(),
			Name:   "New Name",
		}

		_, err := userService.UpdateUser(context.Background(), req)

		// Assert
		assert.Error(t, err)
		assert.Equal(t, account.ErrUserNotFound, err)

		// Verify expectations
		userRepo.AssertExpectations(t)
	})
}

func TestUserService_ChangePassword(t *testing.T) {
	t.Run("successful password change", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}

		// Create service
		userService := NewUserService(userRepo, roleRepo, passwordHasher, eventPublisher)

		// Setup test data
		userID := account.NewUserID()
		email, _ := account.NewEmail("<EMAIL>")
		passwordHash := account.NewPasswordHash("old_hashed_password")
		user := account.NewUserWithID(userID, email, passwordHash, "Test User", true, time.Now(), time.Now())

		// Setup expectations
		userRepo.On("GetByID", mock.Anything, userID).Return(user, nil)
		passwordHasher.On("Verify", "old_password", "old_hashed_password").Return(nil)
		passwordHasher.On("Hash", "new_password").Return("new_hashed_password", nil)
		userRepo.On("Update", mock.Anything, user).Return(nil)
		eventPublisher.On("Publish", mock.Anything, mock.AnythingOfType("*account.UserPasswordChangedEvent")).Return(nil)

		// Execute
		req := ChangePasswordRequest{
			UserID:      userID.String(),
			OldPassword: "old_password",
			NewPassword: "new_password",
		}

		err := userService.ChangePassword(context.Background(), req)

		// Assert
		require.NoError(t, err)

		// Verify expectations
		userRepo.AssertExpectations(t)
		passwordHasher.AssertExpectations(t)
		eventPublisher.AssertExpectations(t)
	})

	t.Run("invalid old password", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}

		// Create service
		userService := NewUserService(userRepo, roleRepo, passwordHasher, eventPublisher)

		// Setup test data
		userID := account.NewUserID()
		email, _ := account.NewEmail("<EMAIL>")
		passwordHash := account.NewPasswordHash("old_hashed_password")
		user := account.NewUserWithID(userID, email, passwordHash, "Test User", true, time.Now(), time.Now())

		// Setup expectations
		userRepo.On("GetByID", mock.Anything, userID).Return(user, nil)
		passwordHasher.On("Verify", "wrong_password", "old_hashed_password").Return(errors.New("password mismatch"))

		// Execute
		req := ChangePasswordRequest{
			UserID:      userID.String(),
			OldPassword: "wrong_password",
			NewPassword: "new_password",
		}

		err := userService.ChangePassword(context.Background(), req)

		// Assert
		assert.Error(t, err)
		assert.Equal(t, account.ErrInvalidCredentials, err)

		// Verify expectations
		userRepo.AssertExpectations(t)
		passwordHasher.AssertExpectations(t)
	})
}

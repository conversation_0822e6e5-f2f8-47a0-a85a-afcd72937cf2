package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"

	"github.com/wongpinter/payment-gateway/internal/infrastructure/config"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/database"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/logger"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/middleware"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/redis"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/sentry"
)

func main() {
	// Initialize context
	ctx := context.Background()

	// Load configuration
	cfg, err := config.Load("configs/config.yaml")
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize logger
	appLogger := logger.New(&cfg.Logger)
	appLogger.Info().Msg("Starting Payment Gateway API Server")

	// Initialize Sentry for error reporting
	if err := sentry.Init(&cfg.Sentry); err != nil {
		appLogger.Error().Err(err).Msg("Failed to initialize Sentry")
	}
	defer sentry.Flush(2 * time.Second)

	// Initialize database
	appLogger.Info().Msg("Connecting to database...")
	db, err := database.New(ctx, &cfg.Database)
	if err != nil {
		appLogger.Fatal().Err(err).Msg("Failed to connect to database")
	}
	defer db.Close()
	appLogger.Info().Msg("Database connection established")

	// Initialize Redis
	appLogger.Info().Msg("Connecting to Redis...")
	redisClient, err := redis.New(&cfg.Redis)
	if err != nil {
		appLogger.Fatal().Err(err).Msg("Failed to connect to Redis")
	}
	defer redisClient.Close()
	appLogger.Info().Msg("Redis connection established")

	// Test Redis connection
	if err := redisClient.Ping(ctx).Err(); err != nil {
		appLogger.Fatal().Err(err).Msg("Failed to ping Redis")
	}

	// Set Gin mode based on logger level
	if cfg.Logger.Level == "debug" || cfg.Logger.Level == "trace" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// Create Gin router
	router := gin.New()

	// Setup middleware chain
	middleware.SetupMiddlewareChain(router, cfg)

	// Setup dependencies and routes
	if err := setupDependencies(router, cfg, db, redisClient, appLogger); err != nil {
		appLogger.Fatal().Err(err).Msg("Failed to setup dependencies")
	}

	// Create HTTP server
	server := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
		IdleTimeout:  cfg.Server.IdleTimeout,
	}

	// Start server in a goroutine
	go func() {
		appLogger.Info().
			Str("host", cfg.Server.Host).
			Int("port", cfg.Server.Port).
			Msg("Starting HTTP server")

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			appLogger.Fatal().Err(err).Msg("Failed to start server")
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	appLogger.Info().Msg("Shutting down server...")

	// Create shutdown context with timeout
	shutdownCtx, cancel := context.WithTimeout(context.Background(), cfg.Server.ShutdownTimeout)
	defer cancel()

	// Shutdown server
	if err := server.Shutdown(shutdownCtx); err != nil {
		appLogger.Error().Err(err).Msg("Server forced to shutdown")
	} else {
		appLogger.Info().Msg("Server shutdown complete")
	}
}

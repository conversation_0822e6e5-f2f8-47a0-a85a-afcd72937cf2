package logger

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"strings"
	"testing"

	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/config"
)

func TestInit(t *testing.T) {
	tests := []struct {
		name   string
		config *config.LoggerConfig
	}{
		{
			name: "json format",
			config: &config.LoggerConfig{
				Level:  "info",
				Format: "json",
			},
		},
		{
			name: "console format",
			config: &config.LoggerConfig{
				Level:  "debug",
				Format: "console",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := Init(tt.config)
			assert.NoError(t, err)
		})
	}
}

func TestLogLevels(t *testing.T) {
	// Capture log output
	var buf bytes.Buffer
	logger := zerolog.New(&buf).With().Timestamp().Logger()
	
	// Set global logger for testing
	zerolog.SetGlobalLevel(zerolog.DebugLevel)
	globalLogger = &logger

	ctx := context.Background()

	tests := []struct {
		name     string
		logFunc  func(ctx context.Context, msg string, fields ...interface{})
		level    string
		message  string
	}{
		{
			name:     "debug log",
			logFunc:  LogDebug,
			level:    "debug",
			message:  "debug message",
		},
		{
			name:     "info log",
			logFunc:  LogInfo,
			level:    "info",
			message:  "info message",
		},
		{
			name:     "warn log",
			logFunc:  LogWarn,
			level:    "warn",
			message:  "warn message",
		},
		{
			name:     "trace log",
			logFunc:  LogTrace,
			level:    "trace",
			message:  "trace message",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			buf.Reset()
			
			tt.logFunc(ctx, tt.message, "key", "value")
			
			output := buf.String()
			assert.Contains(t, output, tt.message)
			assert.Contains(t, output, tt.level)
			assert.Contains(t, output, "key")
			assert.Contains(t, output, "value")
		})
	}
}

func TestLogError(t *testing.T) {
	var buf bytes.Buffer
	logger := zerolog.New(&buf).With().Timestamp().Logger()
	globalLogger = &logger

	ctx := context.Background()
	testErr := errors.New("test error")

	LogError(ctx, testErr, "error occurred", "user_id", "123")

	output := buf.String()
	assert.Contains(t, output, "error occurred")
	assert.Contains(t, output, "test error")
	assert.Contains(t, output, "user_id")
	assert.Contains(t, output, "123")
	assert.Contains(t, output, "error")
}

func TestLogFatal(t *testing.T) {
	var buf bytes.Buffer
	logger := zerolog.New(&buf).With().Timestamp().Logger()
	globalLogger = &logger

	ctx := context.Background()
	testErr := errors.New("fatal error")

	// Note: LogFatal calls os.Exit(1), so we can't test the actual exit
	// We'll just test that it logs the message before exiting
	// In a real test, you might want to use a different approach
	
	// For testing purposes, let's create a non-fatal version
	logFatalTest := func(ctx context.Context, err error, msg string, fields ...interface{}) {
		logger := GetLogger(ctx)
		event := logger.Fatal().Err(err)
		
		// Add fields
		for i := 0; i < len(fields); i += 2 {
			if i+1 < len(fields) {
				event = event.Interface(fields[i].(string), fields[i+1])
			}
		}
		
		// Just log without calling os.Exit for testing
		event.Msg(msg)
	}

	logFatalTest(ctx, testErr, "fatal error occurred", "component", "test")

	output := buf.String()
	assert.Contains(t, output, "fatal error occurred")
	assert.Contains(t, output, "fatal error")
	assert.Contains(t, output, "component")
	assert.Contains(t, output, "test")
	assert.Contains(t, output, "fatal")
}

func TestGetLogger(t *testing.T) {
	// Test without context
	logger := GetLogger(context.Background())
	assert.NotNil(t, logger)

	// Test with context containing logger
	ctx := context.Background()
	customLogger := zerolog.New(nil).With().Str("custom", "true").Logger()
	ctx = context.WithValue(ctx, loggerKey, &customLogger)
	
	retrievedLogger := GetLogger(ctx)
	assert.NotNil(t, retrievedLogger)
}

func TestWithLogger(t *testing.T) {
	ctx := context.Background()
	customLogger := zerolog.New(nil).With().Str("custom", "true").Logger()
	
	newCtx := WithLogger(ctx, &customLogger)
	
	retrievedLogger := GetLogger(newCtx)
	assert.NotNil(t, retrievedLogger)
}

func TestContextualLogging(t *testing.T) {
	var buf bytes.Buffer
	logger := zerolog.New(&buf).With().Timestamp().Logger()
	globalLogger = &logger

	// Create context with trace_id and correlation_id
	ctx := context.Background()
	ctx = context.WithValue(ctx, "trace_id", "trace-123")
	ctx = context.WithValue(ctx, "correlation_id", "corr-456")
	ctx = context.WithValue(ctx, "user_id", "user-789")

	LogInfo(ctx, "test message with context")

	output := buf.String()
	
	// Parse JSON output to verify fields
	var logEntry map[string]interface{}
	err := json.Unmarshal([]byte(strings.TrimSpace(output)), &logEntry)
	require.NoError(t, err)

	assert.Equal(t, "test message with context", logEntry["message"])
	assert.Equal(t, "info", logEntry["level"])
	assert.Contains(t, logEntry, "time")
}

func TestLogWithFields(t *testing.T) {
	var buf bytes.Buffer
	logger := zerolog.New(&buf).With().Timestamp().Logger()
	globalLogger = &logger

	ctx := context.Background()

	LogInfo(ctx, "test with fields", 
		"string_field", "string_value",
		"int_field", 42,
		"bool_field", true,
		"float_field", 3.14,
	)

	output := buf.String()
	
	// Parse JSON output
	var logEntry map[string]interface{}
	err := json.Unmarshal([]byte(strings.TrimSpace(output)), &logEntry)
	require.NoError(t, err)

	assert.Equal(t, "string_value", logEntry["string_field"])
	assert.Equal(t, float64(42), logEntry["int_field"]) // JSON numbers are float64
	assert.Equal(t, true, logEntry["bool_field"])
	assert.Equal(t, 3.14, logEntry["float_field"])
}

func TestInvalidLogLevel(t *testing.T) {
	config := &config.LoggerConfig{
		Level:  "invalid",
		Format: "json",
	}

	err := Init(config)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid log level")
}

func TestLoggerInitialization(t *testing.T) {
	// Test that logger is properly initialized
	config := &config.LoggerConfig{
		Level:  "info",
		Format: "json",
	}

	err := Init(config)
	require.NoError(t, err)

	// Test that global logger is set
	assert.NotNil(t, globalLogger)

	// Test logging works
	var buf bytes.Buffer
	logger := zerolog.New(&buf).With().Timestamp().Logger()
	globalLogger = &logger

	ctx := context.Background()
	LogInfo(ctx, "initialization test")

	output := buf.String()
	assert.Contains(t, output, "initialization test")
	assert.Contains(t, output, "info")
}

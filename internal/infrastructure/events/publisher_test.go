package events

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/wongpinter/payment-gateway/internal/domain/account"
)

// MockDomainEvent implements the DomainEvent interface for testing
type MockDomainEvent struct {
	id          string
	name        string
	timestamp   time.Time
	aggregateID string
}

func (m *MockDomainEvent) EventID() string {
	return m.id
}

func (m *MockDomainEvent) EventName() string {
	return m.name
}

func (m *MockDomainEvent) EventTimestamp() time.Time {
	return m.timestamp
}

func (m *MockDomainEvent) AggregateID() string {
	return m.aggregateID
}

func NewMockDomainEvent(eventName string, aggregateID string) *MockDomainEvent {
	return &MockDomainEvent{
		id:          account.NewUserID().String(), // Reuse UUID generation
		name:        eventName,
		timestamp:   time.Now(),
		aggregateID: aggregateID,
	}
}

func TestInMemoryEventPublisher_Publish(t *testing.T) {
	logger := zerolog.New(os.Stdout).With().Timestamp().Logger()
	publisher := NewInMemoryEventPublisher(&logger)

	t.Run("successful event publishing", func(t *testing.T) {
		ctx := context.Background()
		event := NewMockDomainEvent("test.event", "test-aggregate-123")

		err := publisher.Publish(ctx, event)

		require.NoError(t, err)
	})

	t.Run("publish nil event", func(t *testing.T) {
		ctx := context.Background()

		err := publisher.Publish(ctx, nil)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "event cannot be nil")
	})
}

func TestInMemoryEventPublisher_PublishBatch(t *testing.T) {
	logger := zerolog.New(os.Stdout).With().Timestamp().Logger()
	publisher := NewInMemoryEventPublisher(&logger)

	t.Run("successful batch publishing", func(t *testing.T) {
		ctx := context.Background()
		events := []account.DomainEvent{
			NewMockDomainEvent("test.event1", "aggregate-1"),
			NewMockDomainEvent("test.event2", "aggregate-2"),
		}

		err := publisher.PublishBatch(ctx, events)

		require.NoError(t, err)
	})

	t.Run("publish batch with nil event", func(t *testing.T) {
		ctx := context.Background()
		events := []account.DomainEvent{
			NewMockDomainEvent("test.event1", "aggregate-1"),
			nil, // nil event in batch
		}

		err := publisher.PublishBatch(ctx, events)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "event at index 1 cannot be nil")
	})
}

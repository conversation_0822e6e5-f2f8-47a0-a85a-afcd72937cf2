package converters

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
)

// UUIDToPgUUID converts a uuid.UUID to pgtype.UUID
func UUIDToPgUUID(u uuid.UUID) pgtype.UUID {
	return pgtype.UUID{
		Bytes: u,
		Valid: true,
	}
}

// PgUUIDToUUID converts a pgtype.UUID to uuid.UUID
func PgUUIDToUUID(pgUUID pgtype.UUID) (uuid.UUID, error) {
	if !pgUUID.Valid {
		return uuid.Nil, fmt.Errorf("converters.PgUUIDToUUID: invalid UUID")
	}
	return pgUUID.Bytes, nil
}

// NullableUUIDToPgUUID converts a nullable uuid.UUID to pgtype.UUID
func NullableUUIDToPgUUID(u *uuid.UUID) pgtype.UUID {
	if u == nil {
		return pgtype.UUID{Valid: false}
	}
	return pgtype.UUID{
		Bytes: *u,
		Valid: true,
	}
}

// PgUUIDToNullableUUID converts a pgtype.UUID to nullable uuid.UUID
func PgUUIDToNullableUUID(pgUUID pgtype.UUID) *uuid.UUID {
	if !pgUUID.Valid {
		return nil
	}
	u := pgUUID.Bytes
	return &u
}

// TimeToPgTimestamp converts a time.Time to pgtype.Timestamp
func TimeToPgTimestamp(t time.Time) pgtype.Timestamp {
	return pgtype.Timestamp{
		Time:  t,
		Valid: true,
	}
}

// PgTimestampToTime converts a pgtype.Timestamp to time.Time
func PgTimestampToTime(pgTime pgtype.Timestamp) (time.Time, error) {
	if !pgTime.Valid {
		return time.Time{}, fmt.Errorf("converters.PgTimestampToTime: invalid timestamp")
	}
	return pgTime.Time, nil
}

// NullableTimeToPgTimestamp converts a nullable time.Time to pgtype.Timestamp
func NullableTimeToPgTimestamp(t *time.Time) pgtype.Timestamp {
	if t == nil {
		return pgtype.Timestamp{Valid: false}
	}
	return pgtype.Timestamp{
		Time:  *t,
		Valid: true,
	}
}

// PgTimestampToNullableTime converts a pgtype.Timestamp to nullable time.Time
func PgTimestampToNullableTime(pgTime pgtype.Timestamp) *time.Time {
	if !pgTime.Valid {
		return nil
	}
	return &pgTime.Time
}

// StringToPgText converts a string to pgtype.Text
func StringToPgText(s string) pgtype.Text {
	return pgtype.Text{
		String: s,
		Valid:  true,
	}
}

// PgTextToString converts a pgtype.Text to string
func PgTextToString(pgText pgtype.Text) (string, error) {
	if !pgText.Valid {
		return "", fmt.Errorf("converters.PgTextToString: invalid text")
	}
	return pgText.String, nil
}

// NullableStringToPgText converts a nullable string to pgtype.Text
func NullableStringToPgText(s *string) pgtype.Text {
	if s == nil {
		return pgtype.Text{Valid: false}
	}
	return pgtype.Text{
		String: *s,
		Valid:  true,
	}
}

// PgTextToNullableString converts a pgtype.Text to nullable string
func PgTextToNullableString(pgText pgtype.Text) *string {
	if !pgText.Valid {
		return nil
	}
	return &pgText.String
}

// Int64ToPgInt8 converts an int64 to pgtype.Int8
func Int64ToPgInt8(i int64) pgtype.Int8 {
	return pgtype.Int8{
		Int64: i,
		Valid: true,
	}
}

// PgInt8ToInt64 converts a pgtype.Int8 to int64
func PgInt8ToInt64(pgInt pgtype.Int8) (int64, error) {
	if !pgInt.Valid {
		return 0, fmt.Errorf("converters.PgInt8ToInt64: invalid int64")
	}
	return pgInt.Int64, nil
}

// NullableInt64ToPgInt8 converts a nullable int64 to pgtype.Int8
func NullableInt64ToPgInt8(i *int64) pgtype.Int8 {
	if i == nil {
		return pgtype.Int8{Valid: false}
	}
	return pgtype.Int8{
		Int64: *i,
		Valid: true,
	}
}

// PgInt8ToNullableInt64 converts a pgtype.Int8 to nullable int64
func PgInt8ToNullableInt64(pgInt pgtype.Int8) *int64 {
	if !pgInt.Valid {
		return nil
	}
	return &pgInt.Int64
}

// BoolToPgBool converts a bool to pgtype.Bool
func BoolToPgBool(b bool) pgtype.Bool {
	return pgtype.Bool{
		Bool:  b,
		Valid: true,
	}
}

// PgBoolToBool converts a pgtype.Bool to bool
func PgBoolToBool(pgBool pgtype.Bool) (bool, error) {
	if !pgBool.Valid {
		return false, fmt.Errorf("converters.PgBoolToBool: invalid bool")
	}
	return pgBool.Bool, nil
}

// JSONToPgJSONB converts a JSON object to pgtype.JSONB
func JSONToPgJSONB(data interface{}) (pgtype.JSONB, error) {
	if data == nil {
		return pgtype.JSONB{Valid: false}, nil
	}

	bytes, err := json.Marshal(data)
	if err != nil {
		return pgtype.JSONB{}, fmt.Errorf("converters.JSONToPgJSONB: failed to marshal JSON: %w", err)
	}

	return pgtype.JSONB{
		Bytes: bytes,
		Valid: true,
	}, nil
}

// PgJSONBToJSON converts a pgtype.JSONB to a JSON object
func PgJSONBToJSON(pgJSON pgtype.JSONB, target interface{}) error {
	if !pgJSON.Valid {
		return fmt.Errorf("converters.PgJSONBToJSON: invalid JSONB")
	}

	if err := json.Unmarshal(pgJSON.Bytes, target); err != nil {
		return fmt.Errorf("converters.PgJSONBToJSON: failed to unmarshal JSON: %w", err)
	}

	return nil
}

// UnixMillisToPgInt8 converts Unix milliseconds timestamp to pgtype.Int8
func UnixMillisToPgInt8(millis int64) pgtype.Int8 {
	return pgtype.Int8{
		Int64: millis,
		Valid: true,
	}
}

// PgInt8ToUnixMillis converts pgtype.Int8 to Unix milliseconds timestamp
func PgInt8ToUnixMillis(pgInt pgtype.Int8) (int64, error) {
	if !pgInt.Valid {
		return 0, fmt.Errorf("converters.PgInt8ToUnixMillis: invalid timestamp")
	}
	return pgInt.Int64, nil
}

// TimeToUnixMillis converts time.Time to Unix milliseconds
func TimeToUnixMillis(t time.Time) int64 {
	return t.UnixMilli()
}

// UnixMillisToTime converts Unix milliseconds to time.Time
func UnixMillisToTime(millis int64) time.Time {
	return time.UnixMilli(millis)
}

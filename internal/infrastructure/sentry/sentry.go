package sentry

import (
	"time"

	"github.com/getsentry/sentry-go"

	"github.com/wongpinter/payment-gateway/internal/infrastructure/config"
)

// Init initializes Sentry for error reporting
func Init(cfg *config.SentryConfig) error {
	if cfg.DSN == "" {
		// Sentry not configured, skip initialization
		return nil
	}

	return sentry.Init(sentry.ClientOptions{
		Dsn:              cfg.DSN,
		Environment:      cfg.Environment,
		Debug:            cfg.Debug,
		SampleRate:       cfg.SampleRate,
		TracesSampleRate: cfg.TracesSampleRate,
		AttachStacktrace: true,
		BeforeSend: func(event *sentry.Event, hint *sentry.EventHint) *sentry.Event {
			// Add custom logic here if needed
			return event
		},
	})
}

// Flush waits for all events to be sent to Sentry
func Flush(timeout time.Duration) bool {
	return sentry.Flush(timeout)
}

// CaptureException captures an exception and sends it to Sentry
func CaptureException(err error) *sentry.EventID {
	return sentry.CaptureException(err)
}

// CaptureMessage captures a message and sends it to Sentry
func CaptureMessage(message string) *sentry.EventID {
	return sentry.CaptureMessage(message)
}

// AddBreadcrumb adds a breadcrumb to the current scope
func AddBreadcrumb(breadcrumb *sentry.Breadcrumb) {
	sentry.AddBreadcrumb(breadcrumb)
}

// ConfigureScope configures the current scope
func ConfigureScope(f func(scope *sentry.Scope)) {
	sentry.ConfigureScope(f)
}

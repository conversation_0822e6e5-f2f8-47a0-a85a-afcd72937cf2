package handler

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/wongpinter/payment-gateway/internal/application/account/service"
	"github.com/wongpinter/payment-gateway/internal/domain/account"
)

// Mock UserService for testing
type MockUserService struct {
	mock.Mock
}

func (m *MockUserService) RegisterUser(ctx context.Context, email, password, name string) (*account.User, error) {
	args := m.Called(ctx, email, password, name)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*account.User), args.Error(1)
}

func (m *MockUserService) GetUserByID(ctx context.Context, userID account.UserID) (*account.User, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*account.User), args.Error(1)
}

func (m *MockUserService) UpdateUser(ctx context.Context, userID account.UserID, name, email string) (*account.User, error) {
	args := m.Called(ctx, userID, name, email)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*account.User), args.Error(1)
}

func (m *MockUserService) ChangePassword(ctx context.Context, userID account.UserID, currentPassword, newPassword string) error {
	args := m.Called(ctx, userID, currentPassword, newPassword)
	return args.Error(0)
}

func (m *MockUserService) DeactivateUser(ctx context.Context, userID account.UserID) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockUserService) ActivateUser(ctx context.Context, userID account.UserID) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

// Mock AuthService for testing
type MockAuthService struct {
	mock.Mock
}

func (m *MockAuthService) Login(ctx context.Context, req service.LoginRequest) (*service.LoginResponse, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*service.LoginResponse), args.Error(1)
}

func (m *MockAuthService) RefreshToken(ctx context.Context, req service.RefreshTokenRequest) (*service.LoginResponse, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*service.LoginResponse), args.Error(1)
}

func (m *MockAuthService) Logout(ctx context.Context, refreshToken string) error {
	args := m.Called(ctx, refreshToken)
	return args.Error(0)
}

func (m *MockAuthService) LogoutWithAccessToken(ctx context.Context, refreshToken, accessToken string) error {
	args := m.Called(ctx, refreshToken, accessToken)
	return args.Error(0)
}

func (m *MockAuthService) HasPermission(ctx context.Context, userID string, permission string) (bool, error) {
	args := m.Called(ctx, userID, permission)
	return args.Bool(0), args.Error(1)
}

func TestAuthHandler_Login(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("successful login", func(t *testing.T) {
		// Setup mocks
		mockUserService := &MockUserService{}
		mockAuthService := &MockAuthService{}
		handler := NewAuthHandler(mockUserService, mockAuthService)

		// Setup test data
		loginReq := LoginRequest{
			Email:    "<EMAIL>",
			Password: "password123",
		}

		serviceReq := service.LoginRequest{
			Email:    "<EMAIL>",
			Password: "password123",
		}

		serviceResp := &service.LoginResponse{
			AccessToken:  "access_token",
			RefreshToken: "refresh_token",
			ExpiresAt:    time.Now().Add(time.Hour),
			User: service.UserInfo{
				ID:       "user-id",
				Email:    "<EMAIL>",
				Name:     "Test User",
				IsActive: true,
				Roles:    []string{"user"},
			},
		}

		// Setup expectations
		mockAuthService.On("Login", mock.Anything, serviceReq).Return(serviceResp, nil)

		// Create request
		reqBody, _ := json.Marshal(loginReq)
		req := httptest.NewRequest(http.MethodPost, "/auth/login", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")

		// Create response recorder
		w := httptest.NewRecorder()

		// Create Gin context
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// Execute
		handler.Login(c)

		// Assert
		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, true, response["success"])
		assert.Equal(t, "Login successful", response["message"])
		assert.NotNil(t, response["data"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "access_token", data["access_token"])
		assert.Equal(t, "refresh_token", data["refresh_token"])
		assert.NotNil(t, data["user"])

		// Verify expectations
		mockAuthService.AssertExpectations(t)
	})

	t.Run("invalid JSON", func(t *testing.T) {
		// Setup mocks
		mockUserService := &MockUserService{}
		mockAuthService := &MockAuthService{}
		handler := NewAuthHandler(mockUserService, mockAuthService)

		// Create request with invalid JSON
		req := httptest.NewRequest(http.MethodPost, "/auth/login", bytes.NewBuffer([]byte("invalid json")))
		req.Header.Set("Content-Type", "application/json")

		// Create response recorder
		w := httptest.NewRecorder()

		// Create Gin context
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// Execute
		handler.Login(c)

		// Assert
		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, false, response["success"])
		assert.Equal(t, "Request failed", response["message"])
		assert.NotNil(t, response["error"])

		errorInfo := response["error"].(map[string]interface{})
		assert.Contains(t, errorInfo["message"], "Invalid request format")
	})

	t.Run("validation error", func(t *testing.T) {
		// Setup mocks
		mockUserService := &MockUserService{}
		mockAuthService := &MockAuthService{}
		handler := NewAuthHandler(mockUserService, mockAuthService)

		// Setup test data with missing email
		loginReq := LoginRequest{
			Password: "password123",
		}

		// Create request
		reqBody, _ := json.Marshal(loginReq)
		req := httptest.NewRequest(http.MethodPost, "/auth/login", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")

		// Create response recorder
		w := httptest.NewRecorder()

		// Create Gin context
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// Execute
		handler.Login(c)

		// Assert
		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, false, response["success"])
		assert.Equal(t, "Request failed", response["message"])
		assert.NotNil(t, response["error"])

		errorInfo := response["error"].(map[string]interface{})
		assert.Contains(t, errorInfo["message"], "Validation failed")
	})

	t.Run("invalid credentials", func(t *testing.T) {
		// Setup mocks
		mockUserService := &MockUserService{}
		mockAuthService := &MockAuthService{}
		handler := NewAuthHandler(mockUserService, mockAuthService)

		// Setup test data
		loginReq := LoginRequest{
			Email:    "<EMAIL>",
			Password: "wrong_password",
		}

		serviceReq := service.LoginRequest{
			Email:    "<EMAIL>",
			Password: "wrong_password",
		}

		// Setup expectations
		mockAuthService.On("Login", mock.Anything, serviceReq).Return(nil, account.ErrInvalidCredentials)

		// Create request
		reqBody, _ := json.Marshal(loginReq)
		req := httptest.NewRequest(http.MethodPost, "/auth/login", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")

		// Create response recorder
		w := httptest.NewRecorder()

		// Create Gin context
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// Execute
		handler.Login(c)

		// Assert
		assert.Equal(t, http.StatusUnauthorized, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, false, response["success"])
		assert.Equal(t, "Request failed", response["message"])
		assert.NotNil(t, response["error"])

		errorInfo := response["error"].(map[string]interface{})
		assert.Contains(t, errorInfo["message"], "Invalid email or password")

		// Verify expectations
		mockAuthService.AssertExpectations(t)
	})

	t.Run("service error", func(t *testing.T) {
		// Setup mocks
		mockUserService := &MockUserService{}
		mockAuthService := &MockAuthService{}
		handler := NewAuthHandler(mockUserService, mockAuthService)

		// Setup test data
		loginReq := LoginRequest{
			Email:    "<EMAIL>",
			Password: "password123",
		}

		serviceReq := service.LoginRequest{
			Email:    "<EMAIL>",
			Password: "password123",
		}

		// Setup expectations
		mockAuthService.On("Login", mock.Anything, serviceReq).Return(nil, errors.New("database error"))

		// Create request
		reqBody, _ := json.Marshal(loginReq)
		req := httptest.NewRequest(http.MethodPost, "/auth/login", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")

		// Create response recorder
		w := httptest.NewRecorder()

		// Create Gin context
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// Execute
		handler.Login(c)

		// Assert
		assert.Equal(t, http.StatusInternalServerError, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, false, response["success"])
		assert.Equal(t, "Request failed", response["message"])
		assert.NotNil(t, response["error"])

		errorInfo := response["error"].(map[string]interface{})
		assert.Contains(t, errorInfo["message"], "Login failed")

		// Verify expectations
		mockAuthService.AssertExpectations(t)
	})
}

func TestAuthHandler_RefreshToken(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("successful token refresh", func(t *testing.T) {
		// Setup mocks
		mockUserService := &MockUserService{}
		mockAuthService := &MockAuthService{}
		handler := NewAuthHandler(mockUserService, mockAuthService)

		// Setup test data
		refreshReq := RefreshTokenRequest{
			RefreshToken: "refresh_token",
		}

		serviceReq := service.RefreshTokenRequest{
			RefreshToken: "refresh_token",
		}

		serviceResp := &service.LoginResponse{
			AccessToken:  "new_access_token",
			RefreshToken: "new_refresh_token",
			ExpiresAt:    time.Now().Add(time.Hour),
			User: service.UserInfo{
				ID:       "user-id",
				Email:    "<EMAIL>",
				Name:     "Test User",
				IsActive: true,
				Roles:    []string{"user"},
			},
		}

		// Setup expectations
		mockAuthService.On("RefreshToken", mock.Anything, serviceReq).Return(serviceResp, nil)

		// Create request
		reqBody, _ := json.Marshal(refreshReq)
		req := httptest.NewRequest(http.MethodPost, "/auth/refresh", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")

		// Create response recorder
		w := httptest.NewRecorder()

		// Create Gin context
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// Execute
		handler.RefreshToken(c)

		// Assert
		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, true, response["success"])
		assert.Equal(t, "Token refreshed successfully", response["message"])
		assert.NotNil(t, response["data"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "new_access_token", data["access_token"])
		assert.Equal(t, "new_refresh_token", data["refresh_token"])

		// Verify expectations
		mockAuthService.AssertExpectations(t)
	})

	t.Run("invalid refresh token", func(t *testing.T) {
		// Setup mocks
		mockUserService := &MockUserService{}
		mockAuthService := &MockAuthService{}
		handler := NewAuthHandler(mockUserService, mockAuthService)

		// Setup test data
		refreshReq := RefreshTokenRequest{
			RefreshToken: "invalid_token",
		}

		serviceReq := service.RefreshTokenRequest{
			RefreshToken: "invalid_token",
		}

		// Setup expectations
		mockAuthService.On("RefreshToken", mock.Anything, serviceReq).Return(nil, errors.New("invalid token"))

		// Create request
		reqBody, _ := json.Marshal(refreshReq)
		req := httptest.NewRequest(http.MethodPost, "/auth/refresh", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")

		// Create response recorder
		w := httptest.NewRecorder()

		// Create Gin context
		c, _ := gin.CreateTestContext(w)
		c.Request = req

		// Execute
		handler.RefreshToken(c)

		// Assert
		assert.Equal(t, http.StatusInternalServerError, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, false, response["success"])
		assert.Equal(t, "Request failed", response["message"])
		assert.NotNil(t, response["error"])

		// Verify expectations
		mockAuthService.AssertExpectations(t)
	})
}

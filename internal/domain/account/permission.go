package account

import (
	"errors"
	"time"

	"github.com/google/uuid"
)

// Permission represents a permission in the system
type Permission struct {
	id          PermissionID
	name        string
	description string
	resource    string
	action      string
	isActive    bool
	createdAt   time.Time
	updatedAt   time.Time
}

// PermissionID is a value object representing a permission identifier
type PermissionID struct {
	value uuid.UUID
}

// NewPermissionID creates a new PermissionID
func NewPermissionID() PermissionID {
	return PermissionID{value: uuid.New()}
}

// PermissionIDFromString creates a PermissionID from a string
func PermissionIDFromString(s string) (PermissionID, error) {
	id, err := uuid.Parse(s)
	if err != nil {
		return PermissionID{}, errors.New("invalid permission ID format")
	}
	return PermissionID{value: id}, nil
}

// String returns the string representation of PermissionID
func (p PermissionID) String() string {
	return p.value.String()
}

// UUID returns the underlying UUID
func (p PermissionID) UUID() uuid.UUID {
	return p.value
}

// NewPermission creates a new Permission entity
func NewPermission(name, description, resource, action string) *Permission {
	now := time.Now()
	return &Permission{
		id:          NewPermissionID(),
		name:        name,
		description: description,
		resource:    resource,
		action:      action,
		isActive:    true,
		createdAt:   now,
		updatedAt:   now,
	}
}

// NewPermissionWithID creates a Permission entity with all fields specified (for repository use)
func NewPermissionWithID(id PermissionID, name, description, resource, action string, isActive bool, createdAt, updatedAt time.Time) *Permission {
	return &Permission{
		id:          id,
		name:        name,
		description: description,
		resource:    resource,
		action:      action,
		isActive:    isActive,
		createdAt:   createdAt,
		updatedAt:   updatedAt,
	}
}

// ID returns the permission's ID
func (p *Permission) ID() PermissionID {
	return p.id
}

// Name returns the permission's name
func (p *Permission) Name() string {
	return p.name
}

// Description returns the permission's description
func (p *Permission) Description() string {
	return p.description
}

// Resource returns the permission's resource
func (p *Permission) Resource() string {
	return p.resource
}

// Action returns the permission's action
func (p *Permission) Action() string {
	return p.action
}

// IsActive returns whether the permission is active
func (p *Permission) IsActive() bool {
	return p.isActive
}

// CreatedAt returns when the permission was created
func (p *Permission) CreatedAt() time.Time {
	return p.createdAt
}

// UpdatedAt returns when the permission was last updated
func (p *Permission) UpdatedAt() time.Time {
	return p.updatedAt
}

// UpdateName updates the permission's name
func (p *Permission) UpdateName(newName string) {
	p.name = newName
	p.updatedAt = time.Now()
}

// UpdateDescription updates the permission's description
func (p *Permission) UpdateDescription(newDescription string) {
	p.description = newDescription
	p.updatedAt = time.Now()
}

// UpdateResource updates the permission's resource
func (p *Permission) UpdateResource(newResource string) {
	p.resource = newResource
	p.updatedAt = time.Now()
}

// UpdateAction updates the permission's action
func (p *Permission) UpdateAction(newAction string) {
	p.action = newAction
	p.updatedAt = time.Now()
}

// Deactivate deactivates the permission
func (p *Permission) Deactivate() {
	p.isActive = false
	p.updatedAt = time.Now()
}

// Activate activates the permission
func (p *Permission) Activate() {
	p.isActive = true
	p.updatedAt = time.Now()
}

// Equals checks if two permissions are equal
func (p *Permission) Equals(other Permission) bool {
	return p.id == other.id
}

// FullName returns the full permission name in the format "resource:action"
func (p *Permission) FullName() string {
	return p.resource + ":" + p.action
}

// Common permission constants for the payment gateway system
var (
	// User management permissions
	PermissionUserCreate = &Permission{
		id:          PermissionID{value: uuid.MustParse("00000000-0000-0000-0000-000000000001")},
		name:        "user.create",
		description: "Create new users",
		resource:    "user",
		action:      "create",
		isActive:    true,
	}

	PermissionUserRead = &Permission{
		id:          PermissionID{value: uuid.MustParse("00000000-0000-0000-0000-000000000002")},
		name:        "user.read",
		description: "Read user information",
		resource:    "user",
		action:      "read",
		isActive:    true,
	}

	PermissionUserUpdate = &Permission{
		id:          PermissionID{value: uuid.MustParse("00000000-0000-0000-0000-000000000003")},
		name:        "user.update",
		description: "Update user information",
		resource:    "user",
		action:      "update",
		isActive:    true,
	}

	PermissionUserDelete = &Permission{
		id:          PermissionID{value: uuid.MustParse("00000000-0000-0000-0000-000000000004")},
		name:        "user.delete",
		description: "Delete users",
		resource:    "user",
		action:      "delete",
		isActive:    true,
	}

	// Payment management permissions
	PermissionPaymentCreate = &Permission{
		id:          PermissionID{value: uuid.MustParse("00000000-0000-0000-0000-000000000005")},
		name:        "payment.create",
		description: "Create new payments",
		resource:    "payment",
		action:      "create",
		isActive:    true,
	}

	PermissionPaymentRead = &Permission{
		id:          PermissionID{value: uuid.MustParse("00000000-0000-0000-0000-000000000006")},
		name:        "payment.read",
		description: "Read payment information",
		resource:    "payment",
		action:      "read",
		isActive:    true,
	}

	// Organization management permissions
	PermissionOrganizationCreate = &Permission{
		id:          PermissionID{value: uuid.MustParse("00000000-0000-0000-0000-000000000007")},
		name:        "organization.create",
		description: "Create new organizations",
		resource:    "organization",
		action:      "create",
		isActive:    true,
	}

	PermissionOrganizationRead = &Permission{
		id:          PermissionID{value: uuid.MustParse("00000000-0000-0000-0000-000000000008")},
		name:        "organization.read",
		description: "Read organization information",
		resource:    "organization",
		action:      "read",
		isActive:    true,
	}
)

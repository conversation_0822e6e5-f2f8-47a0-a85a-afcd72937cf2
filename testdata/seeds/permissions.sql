-- Test seed data for permissions table
-- This file contains test data for the permissions table used in integration tests
-- These permissions are for testing purposes and may include additional test-specific permissions

-- Clear existing test permissions (if any)
DELETE FROM permissions WHERE id::text LIKE '11111111-%';

-- Insert test permissions for the payment gateway system
INSERT INTO permissions (id, name, description, resource, action, is_active) VALUES
    -- User management permissions
    ('11111111-1111-1111-1111-111111111001', 'user.create', 'Create new users', 'user', 'create', TRUE),
    ('11111111-1111-1111-1111-111111111002', 'user.read', 'Read user information', 'user', 'read', TRUE),
    ('11111111-1111-1111-1111-111111111003', 'user.update', 'Update user information', 'user', 'update', TRUE),
    ('11111111-1111-1111-1111-111111111004', 'user.delete', 'Delete users', 'user', 'delete', TRUE),
    ('11111111-1111-1111-1111-111111111005', 'user.list', 'List all users', 'user', 'list', TRUE),
    
    -- Role management permissions
    ('11111111-1111-1111-1111-111111111006', 'role.create', 'Create new roles', 'role', 'create', TRUE),
    ('11111111-1111-1111-1111-111111111007', 'role.read', 'Read role information', 'role', 'read', TRUE),
    ('11111111-1111-1111-1111-111111111008', 'role.update', 'Update role information', 'role', 'update', TRUE),
    ('11111111-1111-1111-1111-111111111009', 'role.delete', 'Delete roles', 'role', 'delete', TRUE),
    ('11111111-1111-1111-1111-111111111010', 'role.list', 'List all roles', 'role', 'list', TRUE),
    
    -- Permission management permissions
    ('11111111-1111-1111-1111-111111111011', 'permission.read', 'Read permission information', 'permission', 'read', TRUE),
    ('11111111-1111-1111-1111-111111111012', 'permission.list', 'List all permissions', 'permission', 'list', TRUE),
    
    -- Payment management permissions (for future phases)
    ('11111111-1111-1111-1111-111111111013', 'payment.create', 'Create new payments', 'payment', 'create', TRUE),
    ('11111111-1111-1111-1111-111111111014', 'payment.read', 'Read payment information', 'payment', 'read', TRUE),
    ('11111111-1111-1111-1111-111111111015', 'payment.update', 'Update payment information', 'payment', 'update', TRUE),
    ('11111111-1111-1111-1111-111111111016', 'payment.delete', 'Delete payments', 'payment', 'delete', TRUE),
    ('11111111-1111-1111-1111-111111111017', 'payment.list', 'List all payments', 'payment', 'list', TRUE),
    
    -- Organization management permissions (for future phases)
    ('11111111-1111-1111-1111-111111111018', 'organization.create', 'Create new organizations', 'organization', 'create', TRUE),
    ('11111111-1111-1111-1111-111111111019', 'organization.read', 'Read organization information', 'organization', 'read', TRUE),
    ('11111111-1111-1111-1111-111111111020', 'organization.update', 'Update organization information', 'organization', 'update', TRUE),
    ('11111111-1111-1111-1111-111111111021', 'organization.delete', 'Delete organizations', 'organization', 'delete', TRUE),
    ('11111111-1111-1111-1111-111111111022', 'organization.list', 'List all organizations', 'organization', 'list', TRUE),
    
    -- System administration permissions
    ('11111111-1111-1111-1111-111111111023', 'system.admin', 'Full system administration access', 'system', 'admin', TRUE),
    ('11111111-1111-1111-1111-111111111024', 'system.config', 'System configuration access', 'system', 'config', TRUE),
    ('11111111-1111-1111-1111-111111111025', 'system.audit', 'System audit log access', 'system', 'audit', TRUE),
    
    -- Test-specific permissions
    ('11111111-1111-1111-1111-111111111026', 'test.permission', 'Test permission for integration tests', 'test', 'permission', TRUE),
    ('11111111-1111-1111-1111-111111111027', 'inactive.permission', 'Inactive test permission', 'test', 'inactive', FALSE);

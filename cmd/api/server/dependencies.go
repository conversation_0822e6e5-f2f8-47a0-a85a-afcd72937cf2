package main

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog"

	"github.com/wongpinter/payment-gateway/internal/application/account/service"
	"github.com/wongpinter/payment-gateway/internal/domain/account"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/config"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/database"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/database/repository"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/events"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/security"
	"github.com/wongpinter/payment-gateway/internal/interfaces/http/handler"
	"github.com/wongpinter/payment-gateway/internal/interfaces/http/middleware"
	"github.com/wongpinter/payment-gateway/internal/interfaces/http/routes"
)

// Dependencies holds all application dependencies
type Dependencies struct {
	// Infrastructure
	DB          *database.DB
	RedisClient *redis.Client
	Logger      *zerolog.Logger

	// Domain Services
	PasswordHasher account.PasswordHasher
	EventPublisher account.EventPublisher

	// Security
	JWTManager   *security.JWTManager
	JWTBlacklist *security.JWTBlacklist

	// Repositories
	UserRepo       account.UserRepository
	RoleRepo       account.RoleRepository
	PermissionRepo account.PermissionRepository
	SessionRepo    account.SessionRepository

	// Application Services
	UserService *service.UserService
	AuthService *service.AuthService

	// HTTP Layer
	AuthHandler    *handler.AuthHandler
	AuthMiddleware *middleware.AuthMiddleware
}

// setupDependencies initializes all application dependencies and routes
func setupDependencies(router *gin.Engine, cfg *config.Config, db *database.DB, redisClient *redis.Client, logger *zerolog.Logger) error {
	deps := &Dependencies{
		DB:          db,
		RedisClient: redisClient,
		Logger:      logger,
	}

	// Initialize infrastructure dependencies
	if err := deps.initInfrastructure(cfg); err != nil {
		return err
	}

	// Initialize repositories
	if err := deps.initRepositories(); err != nil {
		return err
	}

	// Initialize application services
	if err := deps.initApplicationServices(); err != nil {
		return err
	}

	// Initialize HTTP layer
	if err := deps.initHTTPLayer(); err != nil {
		return err
	}

	// Setup routes
	deps.setupRoutes(router)

	return nil
}

// initInfrastructure initializes infrastructure dependencies
func (d *Dependencies) initInfrastructure(cfg *config.Config) error {
	// Initialize password hasher
	d.PasswordHasher = security.NewArgon2PasswordHasher()

	// Initialize event publisher (simple in-memory for now)
	d.EventPublisher = events.NewInMemoryEventPublisher(d.Logger)

	// Initialize JWT blacklist
	d.JWTBlacklist = security.NewJWTBlacklist(d.RedisClient)

	// Initialize JWT manager
	jwtConfig := security.JWTManagerConfig{
		Secret:          cfg.JWT.Secret,
		AccessTokenTTL:  cfg.JWT.AccessTokenDuration,
		RefreshTokenTTL: cfg.JWT.RefreshTokenDuration,
	}
	d.JWTManager = security.NewJWTManager(jwtConfig, d.JWTBlacklist)

	return nil
}

// initRepositories initializes repository dependencies
func (d *Dependencies) initRepositories() error {
	// Initialize account repositories
	d.UserRepo = repository.NewUserRepository(d.DB)
	d.RoleRepo = repository.NewRoleRepository(d.DB)
	d.PermissionRepo = repository.NewPermissionRepository(d.DB)
	d.SessionRepo = repository.NewSessionRepository(d.RedisClient)

	return nil
}

// initApplicationServices initializes application service dependencies
func (d *Dependencies) initApplicationServices() error {
	// Initialize user service
	d.UserService = service.NewUserService(
		d.UserRepo,
		d.RoleRepo,
		d.PasswordHasher,
		d.EventPublisher,
	)

	// Initialize auth service
	d.AuthService = service.NewAuthService(
		d.UserRepo,
		d.RoleRepo,
		d.PermissionRepo,
		d.SessionRepo,
		d.PasswordHasher,
		d.EventPublisher,
		d.JWTManager,
	)

	return nil
}

// initHTTPLayer initializes HTTP layer dependencies
func (d *Dependencies) initHTTPLayer() error {
	// Initialize handlers
	d.AuthHandler = handler.NewAuthHandler(d.UserService, d.AuthService)

	// Initialize middleware
	d.AuthMiddleware = middleware.NewAuthMiddleware(d.AuthService)

	return nil
}

// setupRoutes sets up all application routes
func (d *Dependencies) setupRoutes(router *gin.Engine) {
	// Health check endpoint
	router.GET("/health", d.healthCheck)

	// API v1 routes
	v1 := router.Group("/api/v1")

	// Authentication routes
	routes.AuthRoutes(v1, d.AuthHandler, d.AuthMiddleware)

	// TODO: Add other route groups here as they are implemented
	// routes.OrganizationRoutes(v1, d.OrganizationHandler, d.AuthMiddleware)
	// routes.BillingRoutes(v1, d.BillingHandler, d.AuthMiddleware)
}

// healthCheck handles the health check endpoint
func (d *Dependencies) healthCheck(c *gin.Context) {
	// Test database connection
	if err := d.DB.Ping(c.Request.Context()); err != nil {
		c.JSON(500, gin.H{
			"status":    "error",
			"message":   "Database connection failed",
			"timestamp": time.Now().Unix(),
		})
		return
	}

	// Test Redis connection
	if err := d.RedisClient.Ping(c.Request.Context()).Err(); err != nil {
		c.JSON(500, gin.H{
			"status":    "error",
			"message":   "Redis connection failed",
			"timestamp": time.Now().Unix(),
		})
		return
	}

	c.JSON(200, gin.H{
		"status":    "ok",
		"message":   "All systems operational",
		"timestamp": time.Now().Unix(),
		"version":   "1.0.0", // TODO: Get from build info
	})
}

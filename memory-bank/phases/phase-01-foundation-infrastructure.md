# Phase 1: Foundation & Core Infrastructure

## 1. Overview and Goals

This document outlines the first phase of refactoring the `payment-service` into a modular, testable, and maintainable system based on Clean Architecture and Domain-Driven Design principles.

**Goal for Phase 1:** To establish a robust, Clean Architecture-compliant project foundation, including the new directory structure, core infrastructure components (configuration, logging, database setup), standardized API responses, and a comprehensive testing framework. This phase will integrate the successful elements from previous project phases (`tasks/phase-01.md`, `tasks/phase-02.1md`, `tasks/phase-02.2.md`, and `tasks/phase-02.3.md`) into the new, self-contained structure.

**Objectives for Phase 1:**
1.  Implement the new Clean Architecture directory structure.
2.  Set up standardized tooling and libraries.
3.  Establish core infrastructure components: configuration, logging, database connection, and web API helpers.
4.  Implement a robust, containerized testing framework.
5.  Ensure all foundational elements adhere to the defined coding guidelines.

**Scope for Phase 1:**
*   Project scaffolding and initial Go module setup.
*   Integration of `koanf` for configuration management.
*   Integration of `zerolog` for structured logging.
*   Database setup with `pgx/v5` and `sqlc` for type-safe queries.
*   Implementation of `golang-migrate` for database migrations.
*   Standardized API response and error handling using `webapi` helpers.
*   Setup of Gin middleware chain (Tracing, Logging, Localization, Error).
*   Internationalization (i18n) setup for English and Indonesian.
*   Containerized testing environment using `testcontainers-go` for PostgreSQL and Redis.
*   Separation of DDL and DML for test data seeding.
*   Centralized test server setup for DRY handler tests.
*   Initial `main.go` wiring for graceful shutdown and dependency injection.

**Out of Scope for Phase 1:**
*   Implementation of any specific bounded contexts (e.g., `account`, `billing`, `organization`).
*   Business logic implementation beyond basic infrastructure setup.
*   Detailed API endpoint definitions for specific features (only `/health` and basic auth endpoints for testing infrastructure).

## 2. Architectural Principles

This refactoring effort formally adopts and enforces **Clean Architecture** and **Domain-Driven Design (DDD)** principles to create clear boundaries between different layers of the application and promote scalability through Bounded Contexts.

### 2.1. The Dependency Rule (Clean Architecture)

Dependencies must only point inwards: `Infrastructure` -> `Application` -> `Domain`.
*   **Domain Layer**: Contains business entities and rules. It has zero dependencies on any outer layer.
*   **Application Layer**: Contains use cases that orchestrate the business logic. It defines interfaces that the Infrastructure layer implements.
*   **Infrastructure Layer**: Contains all external-facing components (database, payment providers, etc.). It depends on the Application layer.

```mermaid
graph TD
    subgraph "Domain Layer"
        direction LR
        D_Entities[Entities]
        D_Interfaces[Repository Interfaces]
    end

    subgraph "Application Layer"
        direction LR
        A_UseCases[Use Cases] --> D_Interfaces
        A_Interfaces[Provider Interfaces]
    end

    subgraph "Infrastructure Layer"
        direction LR
        I_Handlers[HTTP Handlers] --> A_UseCases
        I_Adapters[Provider Adapters] -- implements --> A_Interfaces
        I_Repos[Repository Implementations] -- implements --> D_Interfaces
    end

    I_Handlers --> A_UseCases
    A_UseCases --> D_Entities
    I_Repos --> D_Entities
    I_Adapters -- implements --> A_Interfaces
    A_UseCases -- depends on --> A_Interfaces
```

### 2.2. Bounded Contexts (Domain-Driven Design)

To ensure a clear separation of concerns and promote scalability, the `domain` layer will be structured using Bounded Contexts. Each context is a self-contained unit responsible for a specific business capability. While specific bounded contexts will be implemented in later phases, the directory structure in Phase 1 will reflect this future organization.

*   **`account`**: Manages identity and access control for internal system users (admins, support, etc.).
*   **`organization`**: Manages the external parties involved in transactions (Companies, Partners, Products).
*   **`billing`**: The core transactional context that handles the entire payment lifecycle (Transactions, TransactionItems, TransactionHistory).
*   **`configuration`**: Manages the "how" of payments, including providers and channels (PaymentProvider, PaymentChannel, ProviderChannelMapping).
*   **`notification`**: Responsible for all outgoing communications (e.g., webhooks, emails).
*   **`reconciliation`**: Responsible for matching internal transaction records with external financial data.

### 2.3. Presentation Layer: Ports & Adapters

The presentation layer will adopt a Ports and Adapters approach, living in `/internal/ports` and responsible for translating external requests into calls to the application layer.

*   **`/ports/rest/`**: Contains all HTTP handlers, separated by their "persona" or purpose.
    *   **`openapi/`**: For external client APIs.
    *   **`dashboard/`**: For the internal admin panel API.
    *   **`callback/`**: For incoming webhooks from third parties.
*   **Shared Use Cases**: All handlers will call the same use cases in the `/internal/app` layer, ensuring business logic is centralized and consistently applied.

### 2.4. Target Directory Structure

The new directory structure reflects the DDD approach and Clean Architecture principles:

```
/payment-service/
├── cmd/
│   └── api/
│       ├── server/                # Server setup, DI, and routing
│       │   ├── server.go
│       │   ├── dependencies.go
│       │   └── routes.go
│       └── main.go                # Lean application entry point
├── internal/
│   ├── app/                       # Application layer (use cases, services)
│   │   ├── account/
│   │   ├── organization/
│   │   └── billing/
│   ├── domain/                    # Domain layer (entities, value objects, core business logic)
│   │   ├── account/
│   │   ├── organization/
│   │   ├── billing/
│   │   ├── configuration/
│   │   └── notification/
│   ├── ports/                     # Presentation Layer (Handlers)
│   │   └── rest/
│   │       ├── openapi/
│   │       ├── dashboard/
│   │       └── callback/
│   └── infrastructure/            # Infrastructure layer (external concerns)
│       ├── database/              # PostgreSQL repository implementations
│       ├── provider/              # Adapters for the gateway packages
│       ├── cache/                 # Redis implementation
│       └── eventbus/              # Domain Event Bus implementation
├── pkg/
│   ├── gateways/                  # External, reusable gateway packages
│   │   ├── xendit/
│   │   │   ├── client.go
│   │   │   └── go.mod
│   │   └── nicepay/
│   │       ├── client.go
│   │       └── go.mod
│   └── ... (other shared libraries)
├── api/                           # API definitions (e.g., OpenAPI/Swagger specs)
├── db/
│   └── migrations/                # Database migration .sql files
├── docs/                          # Project documentation
└── scripts/                       # Scripts for build, deployment, etc.
```

## 3. Key Architectural Decisions

### 3.1. Manual Dependency Injection

To maintain full control over the application's startup process and avoid external dependencies, **manual dependency injection** will be used. All dependencies will be constructed and "wired" together in the `/cmd/api/server` package. This makes the flow of control explicit and easy to trace.

### 3.2. Externalized Gateway Packages

All third-party payment provider integrations (e.g., Xendit, NicePay) will be built as **standalone, reusable Go packages** located in `/pkg/gateways`.
*   **Standard Interface**: The application will define a common `PaymentGateway` interface.
*   **Adapters**: Each gateway package will have a corresponding adapter in `/internal/infrastructure/provider` that implements the `PaymentGateway` interface. This decouples the core application from the specific implementation details of any single provider.

**Draft `PaymentGateway` Interface:**
```go
package billing

// Defines the contract for all payment provider integrations.
type PaymentGateway interface {
    CreatePayment(ctx context.Context, req CreatePaymentRequest) (*PaymentResult, error)
    GetPaymentStatus(ctx context.Context, providerTransactionID string) (*PaymentResult, error)
    // ... other common gateway methods
}
```

### 3.3. Internationalization (i18n)

The application will support multiple languages, starting with English (en) and Indonesian (id). This will impact user-facing strings in API responses, validation messages, and notifications.
*   **Mechanism:** A robust i18n mechanism will be implemented to manage translations.
*   **Locale Resolution:** The API will determine the preferred locale from the `Accept-Language` HTTP header or a dedicated query parameter/header.
*   **Fallback:** A default locale (English) will be defined for cases where the requested locale is not supported or not provided.
*   **Impact on API Responses:** Error and success messages in API responses will be localized based on the resolved locale.
*   **Impact on Notifications:** All outgoing notifications (emails, webhooks) will attempt to use the recipient's preferred language if known, or fall back to a default.

## 4. Standardized Libraries

The following libraries will be standardized across the project:

*   **Database Driver:** `pgx/v5`
*   **SQL Code Generator:** `sqlc`
*   **HTTP Framework:** `Gin`
*   **Logger:** `Zerolog`
*   **Configuration:** `Koanf`
*   **HTTP Client:** `Resty v2`
*   **Message Broker:** `go-rabbitmq`
*   **Integration Testing:** `testcontainers-go`
*   **Database Migrations:** `golang-migrate/migrate`
*   **Error Reporting:** `sentry-go`
*   **Internationalization:** `go-i18n`

## 5. Coding Conventions & Guidelines

Adherence to these guidelines is mandatory for all contributors to ensure a high-quality, consistent, and maintainable codebase.

### 5.1. General Principles & Idiomatic Go
*   **Rule 1.1 (Simplicity - KISS):** Prefer simple, clear solutions over complex ones. Avoid unnecessary abstractions or over-engineering.
*   **Rule 1.2 (Effective Go):** Follow the principles outlined in the official [Effective Go](https://go.dev/doc/effective_go) document.
*   **Rule 1.3 (Package Naming):** All package names MUST be short-lowercase. They should reflect the package's purpose (e.g., `account`, `billing`, `config`, `database`, `provider`).
*   **Rule 1.4 (Variable Naming):** All variable names MUST be `camelCase`. Acronyms should be all caps (e.g., `userID`, `httpStatus`, `jwtToken`).
*   **Rule 1.5 (Interfaces):** Define interfaces to decouple components. Functions MUST accept interfaces where possible and return concrete struct types.
*   **Rule 1.6 (Formatting):** All generated Go code MUST be formatted with `gofmt`.

### 5.2. Project Structure & Clean Architecture
*   **Rule 2.1 (Strict Layering):** The dependency direction `API` -> `Application` -> `Domain` MUST be strictly enforced. No layer should depend on an outer layer.
*   **Rule 2.2 (Domain Purity):** The `internal/domain` package MUST NOT import any other project packages. It should only contain pure business logic and data structures.
*   **Rule 2.3 (Thin Handlers):** API handlers (`internal/ports/rest`) MUST only parse requests, call a single application service method (from `internal/app`), and generate a standardized response. They MUST NOT contain business logic.

### 5.3. Error Management Protocol
*   **Rule 3.1 (Error Wrapping):** Errors MUST be wrapped with context using `fmt.Errorf("package.function: %w", err)`.
*   **Rule 3.2 (Custom Domain Errors):** Use custom error variables for predictable business rule failures (e.g., `ErrUserNotFound`, `ErrInvalidCredentials`). These should be defined in the `internal/domain` package of the relevant bounded context.
*   **Rule 3.3 (Centralized HTTP Error Mapping):** A dedicated Gin error-handling middleware is the only place where errors are translated into HTTP status codes.
*   **Rule 3.4 (Sentry for Unexpected Errors):** Use `webapi.ErrorWithSentry(c, err)` for all 5xx internal server errors.

### 5.4. Standardized API Responses (webapi package)
*   **Rule 4.1 (Mandatory Helpers):** All HTTP responses MUST be generated exclusively using helper functions from the `internal/ports/rest/webapi` package.
*   **Rule 4.2 (Success Structure):** The mandatory JSON structure for a successful response is:
    ```json
    {
      "status": "success",
      "data": { /* Actual response data */ },
      "correlationId": "UUID_OF_REQUEST"
    }
    ```
*   **Rule 4.3 (Error Structure):** The mandatory JSON structure for an error response is:
    ```json
    {
      "status": "error",
      "error": {
        "code": "UNIQUE_ERROR_CODE",
        "message": "Human-readable error description."
      },
      "correlationId": "UUID_OF_REQUEST"
    }
    ```
*   **Rule 4.4 (Helper Signatures and Usage):** Adhere to the defined signatures for `webapi.Success`, `webapi.SuccessPaginated`, `webapi.Error`, and `webapi.ErrorWithSentry`.

### 5.5. Logging Best Practices (zerolog)
*   **Rule 5.1 (Structured Logging):** All logging MUST be done using the global `zerolog` instance.
*   **Rule 5.2 (Contextual Logging):** Every log statement MUST include the `trace_id` from the context.
*   **Rule 5.3 (Error Logging):** When logging an error, the error object MUST be included using `.Err(err)`.

### 5.6. Concurrency and Graceful Shutdown
*   **Rule 6.1 (Panic Safety):** Any new goroutine MUST use a `defer...recover()` block, logging any recovered panic to Sentry.
*   **Rule 6.2 (Graceful Termination):** Any long-running goroutine MUST accept a `context.Context` and listen on `ctx.Done()`.
*   **Rule 6.3 (Server Shutdown):** `main.go` must implement graceful shutdown for the HTTP server.

### 5.7. Gin Middleware
*   **Rule 7.1 (Tracing Middleware):** MUST be the first middleware, generating a `trace_id`.
*   **Rule 7.2 (Logger Middleware):** MUST log request and response details.
*   **Rule 7.3 (Error Middleware):** MUST be the last middleware to centralize error handling.

### 5.8. Database Access (sqlc with pgx/v5)
*   **Rule 8.1 (SQL Files):** All SQL queries MUST be defined in `.sql` files.
*   **Rule 8.2 (sqlc.yaml Configuration):** The `sqlc.yaml` file MUST be configured to use `sql_package: "pgx/v5"` and appropriate type overrides.
*   **Rule 8.3 (Type Conversion):** All conversions between domain types and `pgtype` MUST be handled in a dedicated `internal/infrastructure/database/converters` package.
*   **Rule 8.4 (Transactions):** Repository methods that perform writes MUST accept `pgx.Tx` as a parameter.

### 5.9. Testing and CI/CD Strategy
*   **Rule 9.1 (Containerized Testing):** All integration and end-to-end tests that require external services (like a database or Redis) MUST use `testcontainers-go`.
*   **Rule 9.2 (Seeding Data for Tests):** For every new `.up.sql` migration file, a corresponding test data seed file (e.g., `testdata/seeds/000002_seed_roles.sql`) MUST be created. The test setup MUST apply these seeds to the containerized database.
*   **Rule 9.3 (Test Coverage):** API handlers, application services, and repositories MUST have dedicated test files. Aim for high test coverage.
*   **Rule 9.4 (CI/CD Gate):** A pull request MUST NOT be allowed to merge into the main branch unless all tests (`go test ./...`) pass.

### 5.10. Internationalization (i18n)
*   **Rule 10.1 (Localization Support):** The application MUST support English (en) and Indonesian (id) locales. All user-facing strings, including API response messages, validation errors, and notification content, MUST be localizable.

## 6. Database Migration Strategy

The project will migrate from **Alembic** to **`golang-migrate/migrate`**.

**Transition Plan (Baselining):**
1.  **Generate Schema Dump:** A single `.sql` file representing the current, complete database schema will be generated.
2.  **Create First Go Migration:** This schema dump will become the first migration file (e.g., `000001_initial_schema.sql`).
3.  **Baseline Database:** A record will be manually inserted into the `schema_migrations` table to mark the initial schema as "applied" for existing databases.
4.  **New Migrations:** All future schema changes will be new, sequentially numbered `.sql` files managed by `golang-migrate/migrate`.

The existing `alembic/` directory will be removed after the transition is complete.

## 7. Detailed Tasks for Phase 1

### 7.1. Project Scaffolding & Tooling Setup
*   **Task 7.1.1: Create New Directory Structure**
    *   Create the root directory `/home/<USER>/Workspaces/go/wongpinter/uang/payment-service-v2`.
    *   Establish the Clean Architecture directory structure as defined in Section 2.4.
*   **Task 7.1.2: Initialize Go Module**
    *   `go mod init github.com/your-org/payment-service-v2` (replace `github.com/your-org/payment-service-v2` with your actual module path).
*   **Task 7.1.3: Install Standardized Libraries**
    *   Install all Go packages listed in Section 4.
*   **Task 7.1.4: Configure `.gitignore`**
    *   Create a `.gitignore` file with standard Go, IDE, and environment file exclusions.

### 7.2. Core Infrastructure Implementation
*   **Task 7.2.1: Configuration Management (`koanf`)**
    *   Create `configs/config.yaml` with sections for `server`, `logger`, `database`, `redis`, and `jwt`.
    *   Implement `internal/infrastructure/config/config.go` for loading configuration using `koanf`.
*   **Task 7.2.2: Structured Logging (`zerolog`)**
    *   Implement `internal/infrastructure/logger/logger.go` with an `Init` function that configures `zerolog` for console output in development and JSON for production, adhering to Rule 5.1.
*   **Task 7.2.3: Database Setup (`pgx/v5`, `sqlc`, `golang-migrate`)**
    *   Implement `internal/infrastructure/database/database.go` for `pgxpool.Pool` connection.
    *   Create `sqlc.yaml` configured for `pgx/v5` and outputting to `internal/infrastructure/database/sqlc`.
    *   Create the initial migration `db/migrations/000001_initial_schema.up.sql` with basic tables (e.g., `users`, `roles`) and `db/migrations/000001_initial_schema.down.sql`. Ensure UUIDs are used for primary keys and BIGINT for monetary values, as per Section 8.
    *   Create `internal/infrastructure/database/sqlc/queries/placeholder.sql` for initial `sqlc generate`.
    *   Implement `internal/infrastructure/database/converters/` for `pgtype` to domain type conversions, adhering to Rule 8.3.
*   **Task 7.2.4: Standardized API Responses (`webapi`)**
    *   Implement `internal/ports/rest/webapi/response.go` and `error.go` with `Success`, `SuccessPaginated`, `Error`, and `ErrorWithSentry` helpers, adhering to Rule 4.1.
    *   Ensure `correlationId` is included in success and error responses.
*   **Task 7.2.5: Gin Middleware Chain**
    *   Implement `internal/ports/rest/middleware/tracing.go` (first middleware, Rule 7.1).
    *   Implement `internal/ports/rest/middleware/logger.go` (second middleware, Rule 7.2).
    *   Implement `internal/ports/rest/middleware/localization.go` (third middleware, Rule 10.1).
    *   Implement `internal/ports/rest/middleware/error.go` (last middleware, Rule 7.3), using `errors.Is()` for type-safe error handling and localization.
*   **Task 7.2.6: Internationalization (i18n)**
    *   Create `configs/lang/en.json` and `configs/lang/id.json` with initial error messages, adhering to Rule 10.1.

### 7.3. Testing Framework Setup
*   **Task 7.3.1: Containerized Testing (`testcontainers-go`)**
    *   Implement `internal/infrastructure/testhelpers/database.go` and `redis.go` with `SetupTestDatabase` and `SetupTestRedis` functions, adhering to Rule 9.1.
*   **Task 7.3.2: Schema vs. Seed Separation**
    *   Create `testdata/seeds/` directory.
    *   Ensure initial migration `000001_initial_schema.up.sql` contains only DDL.
    *   Create `testdata/seeds/000001_seed_initial_data.sql` for any initial test data.
    *   Modify `SetupTestDatabase` to apply seeds after migrations, adhering to Rule 9.2.
*   **Task 7.3.3: Centralized Test Server Setup**
    *   Implement `internal/infrastructure/testhelpers/server.go` with `SetupTestServer()` for DRY handler test setup, adhering to Rule 9.5.
*   **Task 7.3.4: Initial Test Coverage**
    *   Write basic unit tests for `config`, `logger`, `webapi` helpers, and `converters`, adhering to Rule 9.3.
    *   Write a simple E2E test for the `/health` endpoint using `SetupTestServer()`.

### 7.4. Application Entry Point (`main.go`)
*   **Task 7.4.1: Implement `cmd/api/main.go`**
    *   Initialize `config`, `logger`, `Sentry`.
    *   Initialize `database` connection pool and `Redis` client.
    *   Set up Gin router with the defined middleware chain.
    *   Implement graceful server shutdown, adhering to Rule 6.3.
    *   Add a `/health` endpoint that pings the database.
*   **Task 7.4.2: Implement `cmd/api/server/dependencies.go` and `routes.go`**
    *   Set up manual dependency injection for core components, adhering to Section 3.1.
    *   Define initial routes, including the `/health` endpoint.

## 8. Database Schemas for Phase 1

This section outlines the proposed database schemas for foundational tables that will be created in Phase 1. These schemas incorporate security best practices and align with the `account` bounded context.

### 8.1. Authentication and User Management Schemas (`account` Bounded Context)

#### `users` Table:
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL, -- Stores securely hashed passwords
    role_id INT NOT NULL,
    last_login_at BIGINT,
    status BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);
```
*   **`password_hash`**: Replaces insecure password storage. Will store hashes generated by a strong, one-way algorithm (e.g., bcrypt or Argon2).
*   **`id`**: Changed to `UUID` with `gen_random_uuid()` default for better distributed system compatibility and security.

#### `roles` Table:
```sql
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);
```
*   **`id`**: Changed to `UUID` with `gen_random_uuid()` default.

#### `role_permissions` Table:
```sql
CREATE TABLE role_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role_id UUID NOT NULL REFERENCES roles(id),
    menu_id INT NOT NULL, -- Assuming 'menus' table exists for menu items
    permissions JSONB NOT NULL, -- Stores permissions as JSON (e.g., {"view": true, "create": false})
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(role_id, menu_id)
);
```
*   **`id`**: Changed to `UUID` with `gen_random_uuid()` default.
*   **`role_id`**: References `roles(id)` which is now `UUID`.

#### `user_companies` Table (for user-company relationships):
```sql
CREATE TABLE user_companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    company_id UUID NOT NULL, -- Assuming 'companies' table exists (will be in organization context)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, company_id)
);
```
*   **`id`**: Changed to `UUID` with `gen_random_uuid()` default.
*   **`user_id`**: References `users(id)` which is now `UUID`.
*   **`company_id`**: Will eventually reference `companies(id)` from the `organization` bounded context.

## 9. Phase 1 Deliverables

*   A Go project with the new Clean Architecture directory structure.
*   Working configuration, logging, and database connection.
*   Standardized API response and error handling.
*   A functional Gin server with the defined middleware chain.
*   A robust testing framework capable of running containerized integration tests.
*   Initial database schema and migration setup.
*   All code adhering to the defined coding guidelines.

## 10. Phase 1 Diagram:

```mermaid
graph TD
    subgraph "Phase 1: Foundation & Core Infrastructure"
        A["7.1. Project Scaffolding & Tooling"] --> B("7.2. Core Infrastructure Implementation")
        B --> C("7.3. Testing Framework Setup")
        B --> D("7.4. Application Entry Point")
        C --> D
    end

    subgraph "7.1. Project Scaffolding & Tooling"
        A1["7.1.1: Create New Directory Structure"]
        A2["7.1.2: Initialize Go Module"]
        A3["7.1.3: Install Standardized Libraries"]
        A4["7.1.4: Configure .gitignore"]
    end

    subgraph "7.2. Core Infrastructure Implementation"
        B1["7.2.1: Configuration Management - koanf"]
        B2["7.2.2: Structured Logging - zerolog"]
        B3["7.2.3: Database Setup - pgx/v5, sqlc, golang-migrate"]
        B4["7.2.4: Standardized API Responses - webapi"]
        B5["7.2.5: Gin Middleware Chain"]
        B6["7.2.6: Internationalization - i18n"]
    end

    subgraph "7.3. Testing Framework Setup"
        C1["7.3.1: Containerized Testing - testcontainers-go"]
        C2["7.3.2: Schema vs. Seed Separation"]
        C3["7.3.3: Centralized Test Server Setup"]
        C4["7.3.4: Initial Test Coverage"]
    end

    subgraph "7.4. Application Entry Point"
        D1["7.4.1: Implement cmd/api/main.go"]
        D2["7.4.2: Implement cmd/api/server/dependencies.go & routes.go"]
    end

    style A1 fill:#f9f,stroke:#333,stroke-width:2px
    style A2 fill:#f9f,stroke:#333,stroke-width:2px
    style A3 fill:#f9f,stroke:#333,stroke-width:2px
    style A4 fill:#f9f,stroke:#333,stroke-width:2px

    style B1 fill:#bbf,stroke:#333,stroke-width:2px
    style B2 fill:#bbf,stroke:#333,stroke-width:2px
    style B3 fill:#bbf,stroke:#333,stroke-width:2px
    style B4 fill:#bbf,stroke:#333,stroke-width:2px
    style B5 fill:#bbf,stroke:#333,stroke-width:2px
    style B6 fill:#bbf,stroke:#333,stroke-width:2px

    style C1 fill:#ccf,stroke:#333,stroke-width:2px
    style C2 fill:#ccf,stroke:#333,stroke-width:2px
    style C3 fill:#ccf,stroke:#333,stroke-width:2px
    style C4 fill:#ccf,stroke:#333,stroke-width:2px

    style D1 fill:#ddf,stroke:#333,stroke-width:2px
    style D2 fill:#ddf,stroke:#333,stroke-width:2px

## 11. Phase Completion Checklist

This checklist serves as a verification tool to ensure all tasks, requirements, rules, and guidelines for Phase 1 have been successfully completed and adhered to. Before marking this phase as complete, all items below must be checked off.

### 11.1. General Completion & Quality
*   [ ] All tasks outlined in Section 7 ("Detailed Tasks for Phase 1") have been implemented.
*   [ ] The project builds successfully without errors (`go build ./...`).
*   [ ] All tests pass (`go test ./...`).
*   [ ] Code has been formatted with `gofmt` (`gofmt -s -w .`).
*   [ ] No new warnings or errors are introduced by linters (e.g., `golangci-lint run`).
*   [ ] All dependencies are properly managed (`go mod tidy`).

### 11.2. Architectural Adherence
*   [ ] The new directory structure (Section 2.4) is fully implemented.
*   [ ] Dependencies strictly follow the Clean Architecture rule (Infrastructure -> Application -> Domain).
*   [ ] Manual Dependency Injection is used for wiring components.
*   [ ] Standardized libraries (Section 4) are consistently used.

### 11.3. Core Infrastructure Verification
*   [ ] Configuration loading (`koanf`) is functional and correctly reads from `config.yaml` and environment variables.
*   [ ] Structured logging (`zerolog`) is configured for both development (console) and production (JSON) modes.
*   [ ] Database connection (`pgx/v5`) is established and functional.
*   [ ] `sqlc` is configured and generates type-safe code correctly.
*   [ ] `golang-migrate` is set up, and the initial migration (`000001_initial_schema.up.sql`) applies successfully.
*   [ ] Standardized API responses (`webapi` helpers) are correctly implemented for both success and error cases.
*   [ ] The Gin middleware chain (Tracing, Logger, Localization, Error) is correctly ordered and functional.
*   [ ] Internationalization (i18n) is set up with `en.json` and `id.json` and correctly localizes error messages.

### 11.4. Testing Framework Verification
*   [ ] `testcontainers-go` is successfully used for containerized PostgreSQL and Redis in tests.
*   [ ] DDL and DML are strictly separated, with test data in `testdata/seeds/`.
*   [ ] `SetupTestServer()` is implemented and used for E2E tests.
*   [ ] Basic unit tests for core components (config, logger, webapi, converters) are present and passing.
*   [ ] The `/health` endpoint E2E test is passing.

### 11.5. Coding Guidelines Compliance (Self-Assessment)
*   [ ] **Rule 1 (General):** `gofmt`, `camelCase`, short-lowercase package names, interface-based design are followed.
*   [ ] **Rule 2 (Architecture):** `API` -> `Application` -> `Domain` dependency rule and thin handlers are enforced.
*   [ ] **Rule 3 (Error Handling):** Error wrapping, custom errors, centralized mapping, and Sentry integration are used.
*   [ ] **Rule 4 (API Responses):** `webapi` helpers are used for all responses, and error messages are localized.
*   [ ] **Rule 5 (Logging):** Structured, contextual logging with `zerolog` is implemented.
*   [ ] **Rule 6 (Concurrency):** Panic safety and graceful termination are considered.
*   [ ] **Rule 7 (Middleware):** The exact middleware order is followed.
*   [ ] **Rule 8 (Database):** `.sql` files for `sqlc`, `pgx/v5` configuration, centralized type converters, and transactional repositories are used.
*   [ ] **Rule 9 (Testing & CI/CD):** `testcontainers-go`, DDL/DML separation, and test coverage are adhered to.
*   [ ] **Rule 10 (Internationalization):** Localization support is correctly implemented.

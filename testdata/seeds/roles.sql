-- Test seed data for roles table
-- This file contains test data for the roles table used in integration tests

INSERT INTO roles (id, code, name, description, status, created_at, updated_at) VALUES
    ('550e8400-e29b-41d4-a716-446655440001', 'SUPER_ADMIN', 'Super Administrator', 'Full system access with all permissions', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('550e8400-e29b-41d4-a716-446655440002', 'ADMIN', 'Administrator', 'Administrative access with most permissions', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('550e8400-e29b-41d4-a716-446655440003', 'MANAGER', 'Manager', 'Management access with limited permissions', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('550e8400-e29b-41d4-a716-446655440004', 'USER', 'Regular User', 'Standard user access with basic permissions', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('550e8400-e29b-41d4-a716-446655440005', 'VIEWER', 'Viewer', 'Read-only access to most resources', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('550e8400-e29b-41d4-a716-446655440006', 'INACTIVE_ROLE', 'Inactive Role', 'Test role that is inactive', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert role permissions for testing
INSERT INTO role_permissions (id, role_id, menu_id, permissions, created_at, updated_at) VALUES
    ('660e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', 1, '{"view": true, "create": true, "update": true, "delete": true}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('660e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', 2, '{"view": true, "create": true, "update": true, "delete": true}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('660e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440002', 1, '{"view": true, "create": true, "update": true, "delete": false}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('660e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440002', 2, '{"view": true, "create": true, "update": true, "delete": false}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('660e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440004', 1, '{"view": true, "create": false, "update": false, "delete": false}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('660e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-446655440005', 1, '{"view": true, "create": false, "update": false, "delete": false}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

package account

import (
	"context"
)

// UserRepository defines the interface for user persistence operations
type UserRepository interface {
	// Create saves a new user to the repository
	Create(ctx context.Context, user *User) error

	// GetByID retrieves a user by their ID
	GetByID(ctx context.Context, id UserID) (*User, error)

	// GetByEmail retrieves a user by their email address
	GetByEmail(ctx context.Context, email Email) (*User, error)

	// Update updates an existing user in the repository
	Update(ctx context.Context, user *User) error

	// Delete removes a user from the repository
	Delete(ctx context.Context, id UserID) error

	// List retrieves a list of users with pagination
	List(ctx context.Context, offset, limit int) ([]*User, error)

	// Count returns the total number of users
	Count(ctx context.Context) (int, error)

	// ExistsByEmail checks if a user with the given email exists
	ExistsByEmail(ctx context.Context, email Email) (bool, error)

	// GetUserRoles retrieves all roles for a specific user
	GetUserRoles(ctx context.Context, userID UserID) ([]Role, error)

	// AssignRole assigns a role to a user
	AssignRole(ctx context.Context, userID UserID, roleID RoleID) error

	// RevokeRole revokes a role from a user
	RevokeRole(ctx context.Context, userID UserID, roleID RoleID) error

	// GetUsersWithRole retrieves all users that have a specific role
	GetUsersWithRole(ctx context.Context, roleID RoleID) ([]*User, error)
}

// RoleRepository defines the interface for role persistence operations
type RoleRepository interface {
	// Create saves a new role to the repository
	Create(ctx context.Context, role *Role) error

	// GetByID retrieves a role by its ID
	GetByID(ctx context.Context, id RoleID) (*Role, error)

	// GetByName retrieves a role by its name
	GetByName(ctx context.Context, name string) (*Role, error)

	// Update updates an existing role in the repository
	Update(ctx context.Context, role *Role) error

	// Delete removes a role from the repository
	Delete(ctx context.Context, id RoleID) error

	// List retrieves a list of roles with pagination
	List(ctx context.Context, offset, limit int) ([]*Role, error)

	// Count returns the total number of roles
	Count(ctx context.Context) (int, error)

	// ExistsByName checks if a role with the given name exists
	ExistsByName(ctx context.Context, name string) (bool, error)

	// GetRolePermissions retrieves all permissions for a specific role
	GetRolePermissions(ctx context.Context, roleID RoleID) ([]Permission, error)

	// AssignPermission assigns a permission to a role
	AssignPermission(ctx context.Context, roleID RoleID, permissionID PermissionID) error

	// RevokePermission revokes a permission from a role
	RevokePermission(ctx context.Context, roleID RoleID, permissionID PermissionID) error

	// GetRolesWithPermission retrieves all roles that have a specific permission
	GetRolesWithPermission(ctx context.Context, permissionID PermissionID) ([]*Role, error)
}

// PermissionRepository defines the interface for permission persistence operations
type PermissionRepository interface {
	// Create saves a new permission to the repository
	Create(ctx context.Context, permission *Permission) error

	// GetByID retrieves a permission by its ID
	GetByID(ctx context.Context, id PermissionID) (*Permission, error)

	// GetByName retrieves a permission by its name
	GetByName(ctx context.Context, name string) (*Permission, error)

	// Update updates an existing permission in the repository
	Update(ctx context.Context, permission *Permission) error

	// Delete removes a permission from the repository
	Delete(ctx context.Context, id PermissionID) error

	// List retrieves a list of permissions with pagination
	List(ctx context.Context, offset, limit int) ([]*Permission, error)

	// Count returns the total number of permissions
	Count(ctx context.Context) (int, error)

	// ExistsByName checks if a permission with the given name exists
	ExistsByName(ctx context.Context, name string) (bool, error)

	// GetByResource retrieves all permissions for a specific resource
	GetByResource(ctx context.Context, resource string) ([]*Permission, error)

	// GetByResourceAndAction retrieves a permission by resource and action
	GetByResourceAndAction(ctx context.Context, resource, action string) (*Permission, error)
}

// SessionRepository defines the interface for session persistence operations
type SessionRepository interface {
	// Create saves a new session to the repository
	Create(ctx context.Context, session *Session) error

	// GetByID retrieves a session by its ID
	GetByID(ctx context.Context, id SessionID) (*Session, error)

	// GetByRefreshToken retrieves a session by its refresh token
	GetByRefreshToken(ctx context.Context, refreshToken string) (*Session, error)

	// Update updates an existing session in the repository
	Update(ctx context.Context, session *Session) error

	// Delete removes a session from the repository
	Delete(ctx context.Context, id SessionID) error

	// DeleteByUserID removes all sessions for a specific user
	DeleteByUserID(ctx context.Context, userID UserID) error

	// DeleteExpired removes all expired sessions
	DeleteExpired(ctx context.Context) error

	// GetByUserID retrieves all sessions for a specific user
	GetByUserID(ctx context.Context, userID UserID) ([]*Session, error)
}

// EventPublisher defines the interface for publishing domain events
type EventPublisher interface {
	// Publish publishes a domain event
	Publish(ctx context.Context, event DomainEvent) error
}

// PasswordHasher defines the interface for password hashing operations
type PasswordHasher interface {
	// Hash generates a hash from a plain text password
	Hash(password string) (string, error)

	// Verify checks if a plain text password matches a hash
	Verify(password, hash string) error
}

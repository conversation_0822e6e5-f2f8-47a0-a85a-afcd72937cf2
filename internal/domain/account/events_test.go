package account

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestUserRegisteredEvent(t *testing.T) {
	t.Run("NewUserRegisteredEvent creates valid event", func(t *testing.T) {
		userID := NewUserID()
		email, _ := NewEmail("<EMAIL>")
		name := "Test User"

		event := NewUserRegisteredEvent(userID, email, name)

		assert.Equal(t, "UserRegistered", event.EventName())
		assert.NotZero(t, event.EventTimestamp())
		assert.NotEmpty(t, event.EventID())

		// Check event data
		assert.Equal(t, userID.String(), event.UserID.String())
		assert.Equal(t, email.String(), event.Email.String())
		assert.Equal(t, name, event.Name)
	})

	t.Run("UserRegisteredEvent implements DomainEvent interface", func(t *testing.T) {
		userID := NewUserID()
		email, _ := NewEmail("<EMAIL>")
		name := "Test User"

		event := NewUserRegisteredEvent(userID, email, name)

		// Verify it implements DomainEvent interface
		var domainEvent DomainEvent = event
		assert.NotNil(t, domainEvent)
	})

	t.Run("UserRegisteredEvent has unique event IDs", func(t *testing.T) {
		userID := NewUserID()
		email, _ := NewEmail("<EMAIL>")
		name := "Test User"

		event1 := NewUserRegisteredEvent(userID, email, name)
		event2 := NewUserRegisteredEvent(userID, email, name)

		assert.NotEqual(t, event1.EventID(), event2.EventID())
	})

	t.Run("UserRegisteredEvent occurred at is recent", func(t *testing.T) {
		userID := NewUserID()
		email, _ := NewEmail("<EMAIL>")
		name := "Test User"

		before := time.Now()
		event := NewUserRegisteredEvent(userID, email, name)
		after := time.Now()

		assert.True(t, event.EventTimestamp().After(before) || event.EventTimestamp().Equal(before))
		assert.True(t, event.EventTimestamp().Before(after) || event.EventTimestamp().Equal(after))
	})
}

func TestRoleAssignedEvent(t *testing.T) {
	t.Run("NewRoleAssignedEvent creates valid event", func(t *testing.T) {
		userID := NewUserID()
		roleID := NewRoleID()
		roleName := "admin"

		event := NewRoleAssignedEvent(userID, roleID, roleName)

		assert.Equal(t, "RoleAssigned", event.EventName())
		assert.NotZero(t, event.EventTimestamp())
		assert.NotEmpty(t, event.EventID())

		// Check event data
		assert.Equal(t, userID.String(), event.UserID.String())
		assert.Equal(t, roleID.String(), event.RoleID.String())
		assert.Equal(t, roleName, event.RoleName)
	})

	t.Run("RoleAssignedEvent implements DomainEvent interface", func(t *testing.T) {
		userID := NewUserID()
		roleID := NewRoleID()
		roleName := "admin"

		event := NewRoleAssignedEvent(userID, roleID, roleName)

		// Verify it implements DomainEvent interface
		var domainEvent DomainEvent = event
		assert.NotNil(t, domainEvent)
	})
}

func TestRoleRevokedEvent(t *testing.T) {
	t.Run("NewRoleRevokedEvent creates valid event", func(t *testing.T) {
		userID := NewUserID()
		roleID := NewRoleID()
		roleName := "admin"

		event := NewRoleRevokedEvent(userID, roleID, roleName)

		assert.Equal(t, "RoleRevoked", event.EventName())
		assert.NotZero(t, event.EventTimestamp())
		assert.NotEmpty(t, event.EventID())

		// Check event data
		assert.Equal(t, userID.String(), event.UserID.String())
		assert.Equal(t, roleID.String(), event.RoleID.String())
		assert.Equal(t, roleName, event.RoleName)
	})

	t.Run("RoleRevokedEvent implements DomainEvent interface", func(t *testing.T) {
		userID := NewUserID()
		roleID := NewRoleID()
		roleName := "admin"

		event := NewRoleRevokedEvent(userID, roleID, roleName)

		// Verify it implements DomainEvent interface
		var domainEvent DomainEvent = event
		assert.NotNil(t, domainEvent)
	})
}

func TestUserPasswordChangedEvent(t *testing.T) {
	t.Run("NewUserPasswordChangedEvent creates valid event", func(t *testing.T) {
		userID := NewUserID()
		email, _ := NewEmail("<EMAIL>")

		event := NewUserPasswordChangedEvent(userID, email)

		assert.Equal(t, "UserPasswordChanged", event.EventName())
		assert.NotZero(t, event.EventTimestamp())
		assert.NotEmpty(t, event.EventID())

		// Check event data
		assert.Equal(t, userID.String(), event.UserID.String())
		assert.Equal(t, email.String(), event.Email.String())
	})

	t.Run("UserPasswordChangedEvent implements DomainEvent interface", func(t *testing.T) {
		userID := NewUserID()
		email, _ := NewEmail("<EMAIL>")

		event := NewUserPasswordChangedEvent(userID, email)

		// Verify it implements DomainEvent interface
		var domainEvent DomainEvent = event
		assert.NotNil(t, domainEvent)
	})
}

func TestUserDeactivatedEvent(t *testing.T) {
	t.Run("NewUserDeactivatedEvent creates valid event", func(t *testing.T) {
		userID := NewUserID()
		email, _ := NewEmail("<EMAIL>")
		reason := "Account suspended"

		event := NewUserDeactivatedEvent(userID, email, reason)

		assert.Equal(t, "UserDeactivated", event.EventName())
		assert.NotZero(t, event.EventTimestamp())
		assert.NotEmpty(t, event.EventID())

		// Check event data
		assert.Equal(t, userID.String(), event.UserID.String())
		assert.Equal(t, email.String(), event.Email.String())
		assert.Equal(t, reason, event.Reason)
	})

	t.Run("UserDeactivatedEvent implements DomainEvent interface", func(t *testing.T) {
		userID := NewUserID()
		email, _ := NewEmail("<EMAIL>")
		reason := "Account suspended"

		event := NewUserDeactivatedEvent(userID, email, reason)

		// Verify it implements DomainEvent interface
		var domainEvent DomainEvent = event
		assert.NotNil(t, domainEvent)
	})
}

func TestUserActivatedEvent(t *testing.T) {
	t.Run("NewUserActivatedEvent creates valid event", func(t *testing.T) {
		userID := NewUserID()
		email, _ := NewEmail("<EMAIL>")

		event := NewUserActivatedEvent(userID, email)

		assert.Equal(t, "UserActivated", event.EventName())
		assert.NotZero(t, event.EventTimestamp())
		assert.NotEmpty(t, event.EventID())

		// Check event data
		assert.Equal(t, userID.String(), event.UserID.String())
		assert.Equal(t, email.String(), event.Email.String())
	})

	t.Run("UserActivatedEvent implements DomainEvent interface", func(t *testing.T) {
		userID := NewUserID()
		email, _ := NewEmail("<EMAIL>")

		event := NewUserActivatedEvent(userID, email)

		// Verify it implements DomainEvent interface
		var domainEvent DomainEvent = event
		assert.NotNil(t, domainEvent)
	})
}

func TestEventDataIntegrity(t *testing.T) {
	t.Run("All events have required fields", func(t *testing.T) {
		userID := NewUserID()
		roleID := NewRoleID()
		email, _ := NewEmail("<EMAIL>")

		events := []DomainEvent{
			NewUserRegisteredEvent(userID, email, "Test User"),
			NewRoleAssignedEvent(userID, roleID, "admin"),
			NewRoleRevokedEvent(userID, roleID, "admin"),
			NewUserPasswordChangedEvent(userID, email),
			NewUserDeactivatedEvent(userID, email, "Test reason"),
			NewUserActivatedEvent(userID, email),
		}

		for _, event := range events {
			assert.NotEmpty(t, event.EventID(), "Event ID should not be empty for %s", event.EventName())
			assert.NotEmpty(t, event.EventName(), "Event name should not be empty")
			assert.NotZero(t, event.EventTimestamp(), "EventTimestamp should not be zero for %s", event.EventName())
			assert.NotEmpty(t, event.AggregateID(), "AggregateID should not be empty for %s", event.EventName())
		}
	})
}

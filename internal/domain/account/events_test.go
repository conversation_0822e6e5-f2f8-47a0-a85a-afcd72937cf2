package account

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestUserRegisteredEvent(t *testing.T) {
	t.Run("NewUserRegisteredEvent creates valid event", func(t *testing.T) {
		userID := NewUserID()
		email, _ := NewEmail("<EMAIL>")
		name := "Test User"

		event := NewUserRegisteredEvent(userID, email, name)

		assert.Equal(t, "user.registered", event.EventType())
		assert.NotZero(t, event.OccurredAt())
		assert.NotEmpty(t, event.EventID())

		// Check event data
		data := event.Data()
		assert.Equal(t, userID.String(), data["user_id"])
		assert.Equal(t, email.String(), data["email"])
		assert.Equal(t, name, data["name"])
	})

	t.Run("UserRegisteredEvent implements DomainEvent interface", func(t *testing.T) {
		userID := NewUserID()
		email, _ := NewEmail("<EMAIL>")
		name := "Test User"

		event := NewUserRegisteredEvent(userID, email, name)

		// Verify it implements DomainEvent interface
		var domainEvent DomainEvent = event
		assert.NotNil(t, domainEvent)
	})

	t.Run("UserRegisteredEvent has unique event IDs", func(t *testing.T) {
		userID := NewUserID()
		email, _ := NewEmail("<EMAIL>")
		name := "Test User"

		event1 := NewUserRegisteredEvent(userID, email, name)
		event2 := NewUserRegisteredEvent(userID, email, name)

		assert.NotEqual(t, event1.EventID(), event2.EventID())
	})

	t.Run("UserRegisteredEvent occurred at is recent", func(t *testing.T) {
		userID := NewUserID()
		email, _ := NewEmail("<EMAIL>")
		name := "Test User"

		before := time.Now()
		event := NewUserRegisteredEvent(userID, email, name)
		after := time.Now()

		assert.True(t, event.OccurredAt().After(before) || event.OccurredAt().Equal(before))
		assert.True(t, event.OccurredAt().Before(after) || event.OccurredAt().Equal(after))
	})
}

func TestRoleAssignedEvent(t *testing.T) {
	t.Run("NewRoleAssignedEvent creates valid event", func(t *testing.T) {
		userID := NewUserID()
		roleID := NewRoleID()
		roleName := "admin"

		event := NewRoleAssignedEvent(userID, roleID, roleName)

		assert.Equal(t, "role.assigned", event.EventType())
		assert.NotZero(t, event.OccurredAt())
		assert.NotEmpty(t, event.EventID())

		// Check event data
		data := event.Data()
		assert.Equal(t, userID.String(), data["user_id"])
		assert.Equal(t, roleID.String(), data["role_id"])
		assert.Equal(t, roleName, data["role_name"])
	})

	t.Run("RoleAssignedEvent implements DomainEvent interface", func(t *testing.T) {
		userID := NewUserID()
		roleID := NewRoleID()
		roleName := "admin"

		event := NewRoleAssignedEvent(userID, roleID, roleName)

		// Verify it implements DomainEvent interface
		var domainEvent DomainEvent = event
		assert.NotNil(t, domainEvent)
	})
}

func TestRoleRevokedEvent(t *testing.T) {
	t.Run("NewRoleRevokedEvent creates valid event", func(t *testing.T) {
		userID := NewUserID()
		roleID := NewRoleID()
		roleName := "admin"

		event := NewRoleRevokedEvent(userID, roleID, roleName)

		assert.Equal(t, "role.revoked", event.EventType())
		assert.NotZero(t, event.OccurredAt())
		assert.NotEmpty(t, event.EventID())

		// Check event data
		data := event.Data()
		assert.Equal(t, userID.String(), data["user_id"])
		assert.Equal(t, roleID.String(), data["role_id"])
		assert.Equal(t, roleName, data["role_name"])
	})

	t.Run("RoleRevokedEvent implements DomainEvent interface", func(t *testing.T) {
		userID := NewUserID()
		roleID := NewRoleID()
		roleName := "admin"

		event := NewRoleRevokedEvent(userID, roleID, roleName)

		// Verify it implements DomainEvent interface
		var domainEvent DomainEvent = event
		assert.NotNil(t, domainEvent)
	})
}

func TestUserPasswordChangedEvent(t *testing.T) {
	t.Run("NewUserPasswordChangedEvent creates valid event", func(t *testing.T) {
		userID := NewUserID()
		email, _ := NewEmail("<EMAIL>")

		event := NewUserPasswordChangedEvent(userID, email)

		assert.Equal(t, "user.password_changed", event.EventType())
		assert.NotZero(t, event.OccurredAt())
		assert.NotEmpty(t, event.EventID())

		// Check event data
		data := event.Data()
		assert.Equal(t, userID.String(), data["user_id"])
		assert.Equal(t, email.String(), data["email"])
	})

	t.Run("UserPasswordChangedEvent implements DomainEvent interface", func(t *testing.T) {
		userID := NewUserID()
		email, _ := NewEmail("<EMAIL>")

		event := NewUserPasswordChangedEvent(userID, email)

		// Verify it implements DomainEvent interface
		var domainEvent DomainEvent = event
		assert.NotNil(t, domainEvent)
	})
}

func TestUserDeactivatedEvent(t *testing.T) {
	t.Run("NewUserDeactivatedEvent creates valid event", func(t *testing.T) {
		userID := NewUserID()
		email, _ := NewEmail("<EMAIL>")
		reason := "Account suspended"

		event := NewUserDeactivatedEvent(userID, email, reason)

		assert.Equal(t, "user.deactivated", event.EventType())
		assert.NotZero(t, event.OccurredAt())
		assert.NotEmpty(t, event.EventID())

		// Check event data
		data := event.Data()
		assert.Equal(t, userID.String(), data["user_id"])
		assert.Equal(t, email.String(), data["email"])
		assert.Equal(t, reason, data["reason"])
	})

	t.Run("UserDeactivatedEvent implements DomainEvent interface", func(t *testing.T) {
		userID := NewUserID()
		email, _ := NewEmail("<EMAIL>")
		reason := "Account suspended"

		event := NewUserDeactivatedEvent(userID, email, reason)

		// Verify it implements DomainEvent interface
		var domainEvent DomainEvent = event
		assert.NotNil(t, domainEvent)
	})
}

func TestUserActivatedEvent(t *testing.T) {
	t.Run("NewUserActivatedEvent creates valid event", func(t *testing.T) {
		userID := NewUserID()
		email, _ := NewEmail("<EMAIL>")

		event := NewUserActivatedEvent(userID, email)

		assert.Equal(t, "user.activated", event.EventType())
		assert.NotZero(t, event.OccurredAt())
		assert.NotEmpty(t, event.EventID())

		// Check event data
		data := event.Data()
		assert.Equal(t, userID.String(), data["user_id"])
		assert.Equal(t, email.String(), data["email"])
	})

	t.Run("UserActivatedEvent implements DomainEvent interface", func(t *testing.T) {
		userID := NewUserID()
		email, _ := NewEmail("<EMAIL>")

		event := NewUserActivatedEvent(userID, email)

		// Verify it implements DomainEvent interface
		var domainEvent DomainEvent = event
		assert.NotNil(t, domainEvent)
	})
}

func TestEventDataIntegrity(t *testing.T) {
	t.Run("All events have required fields", func(t *testing.T) {
		userID := NewUserID()
		roleID := NewRoleID()
		email, _ := NewEmail("<EMAIL>")

		events := []DomainEvent{
			NewUserRegisteredEvent(userID, email, "Test User"),
			NewRoleAssignedEvent(userID, roleID, "admin"),
			NewRoleRevokedEvent(userID, roleID, "admin"),
			NewUserPasswordChangedEvent(userID, email),
			NewUserDeactivatedEvent(userID, email, "Test reason"),
			NewUserActivatedEvent(userID, email),
		}

		for _, event := range events {
			assert.NotEmpty(t, event.EventID(), "Event ID should not be empty for %s", event.EventType())
			assert.NotEmpty(t, event.EventType(), "Event type should not be empty")
			assert.NotZero(t, event.OccurredAt(), "OccurredAt should not be zero for %s", event.EventType())
			assert.NotNil(t, event.Data(), "Data should not be nil for %s", event.EventType())
		}
	})
}

package account

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestRoleID(t *testing.T) {
	t.Run("NewRoleID creates valid UUID", func(t *testing.T) {
		roleID := NewRoleID()
		assert.NotEqual(t, uuid.Nil, roleID.UUID())
		assert.NotEmpty(t, roleID.String())
	})

	t.Run("RoleIDFromString with valid UUID", func(t *testing.T) {
		validUUID := "550e8400-e29b-41d4-a716-************"
		roleID, err := RoleIDFromString(validUUID)
		
		require.NoError(t, err)
		assert.Equal(t, validUUID, roleID.String())
	})

	t.Run("RoleIDFromString with invalid UUID", func(t *testing.T) {
		invalidUUID := "invalid-uuid"
		_, err := RoleIDFromString(invalidUUID)
		
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid role ID format")
	})
}

func TestRole(t *testing.T) {
	t.Run("NewR<PERSON> creates valid role", func(t *testing.T) {
		name := "admin"
		description := "Administrator role"

		role := NewRole(name, description)

		assert.NotEqual(t, RoleID{}, role.ID())
		assert.Equal(t, name, role.Name())
		assert.Equal(t, description, role.Description())
		assert.True(t, role.IsActive())
		assert.NotZero(t, role.CreatedAt())
		assert.NotZero(t, role.UpdatedAt())
		assert.Empty(t, role.Permissions())
	})

	t.Run("NewRoleWithID creates role with specified fields", func(t *testing.T) {
		roleID := NewRoleID()
		name := "admin"
		description := "Administrator role"
		isActive := false
		createdAt := time.Now().Add(-24 * time.Hour)
		updatedAt := time.Now().Add(-1 * time.Hour)

		role := NewRoleWithID(roleID, name, description, isActive, createdAt, updatedAt)

		assert.Equal(t, roleID, role.ID())
		assert.Equal(t, name, role.Name())
		assert.Equal(t, description, role.Description())
		assert.Equal(t, isActive, role.IsActive())
		assert.Equal(t, createdAt, role.CreatedAt())
		assert.Equal(t, updatedAt, role.UpdatedAt())
	})

	t.Run("UpdateName updates name and timestamp", func(t *testing.T) {
		role := NewRole("old_name", "Description")
		
		oldUpdatedAt := role.UpdatedAt()
		time.Sleep(1 * time.Millisecond) // Ensure timestamp difference
		
		newName := "new_name"
		role.UpdateName(newName)

		assert.Equal(t, newName, role.Name())
		assert.True(t, role.UpdatedAt().After(oldUpdatedAt))
	})

	t.Run("UpdateDescription updates description and timestamp", func(t *testing.T) {
		role := NewRole("name", "old_description")
		
		oldUpdatedAt := role.UpdatedAt()
		time.Sleep(1 * time.Millisecond)
		
		newDescription := "new_description"
		role.UpdateDescription(newDescription)

		assert.Equal(t, newDescription, role.Description())
		assert.True(t, role.UpdatedAt().After(oldUpdatedAt))
	})

	t.Run("Deactivate sets role inactive and updates timestamp", func(t *testing.T) {
		role := NewRole("admin", "Administrator role")
		
		assert.True(t, role.IsActive())
		oldUpdatedAt := role.UpdatedAt()
		time.Sleep(1 * time.Millisecond)
		
		role.Deactivate()

		assert.False(t, role.IsActive())
		assert.True(t, role.UpdatedAt().After(oldUpdatedAt))
	})

	t.Run("Activate sets role active and updates timestamp", func(t *testing.T) {
		role := NewRoleWithID(NewRoleID(), "admin", "Administrator role", false, time.Now(), time.Now())
		
		assert.False(t, role.IsActive())
		oldUpdatedAt := role.UpdatedAt()
		time.Sleep(1 * time.Millisecond)
		
		role.Activate()

		assert.True(t, role.IsActive())
		assert.True(t, role.UpdatedAt().After(oldUpdatedAt))
	})

	t.Run("AddPermission adds permission to role", func(t *testing.T) {
		role := NewRole("admin", "Administrator role")
		
		permission := NewPermission("user.create", "Create users", "user", "create")
		role.AddPermission(*permission)

		assert.Len(t, role.Permissions(), 1)
		assert.Equal(t, permission.ID(), role.Permissions()[0].ID())
	})

	t.Run("AddPermission does not add duplicate permission", func(t *testing.T) {
		role := NewRole("admin", "Administrator role")
		
		permission := NewPermission("user.create", "Create users", "user", "create")
		role.AddPermission(*permission)
		role.AddPermission(*permission) // Add same permission again

		assert.Len(t, role.Permissions(), 1)
	})

	t.Run("RemovePermission removes permission from role", func(t *testing.T) {
		role := NewRole("admin", "Administrator role")
		
		permission1 := NewPermission("user.create", "Create users", "user", "create")
		permission2 := NewPermission("user.read", "Read users", "user", "read")
		role.AddPermission(*permission1)
		role.AddPermission(*permission2)

		assert.Len(t, role.Permissions(), 2)
		
		role.RemovePermission(permission1.ID())

		assert.Len(t, role.Permissions(), 1)
		assert.Equal(t, permission2.ID(), role.Permissions()[0].ID())
	})

	t.Run("HasPermission returns true for existing permission", func(t *testing.T) {
		role := NewRole("admin", "Administrator role")
		
		permission := NewPermission("user.create", "Create users", "user", "create")
		role.AddPermission(*permission)

		assert.True(t, role.HasPermission(permission.ID()))
	})

	t.Run("HasPermission returns false for non-existing permission", func(t *testing.T) {
		role := NewRole("admin", "Administrator role")
		
		permission := NewPermission("user.create", "Create users", "user", "create")

		assert.False(t, role.HasPermission(permission.ID()))
	})

	t.Run("HasPermissionByName returns true for existing permission", func(t *testing.T) {
		role := NewRole("admin", "Administrator role")
		
		permission := NewPermission("user.create", "Create users", "user", "create")
		role.AddPermission(*permission)

		assert.True(t, role.HasPermissionByName("user.create"))
	})

	t.Run("HasPermissionByName returns false for non-existing permission", func(t *testing.T) {
		role := NewRole("admin", "Administrator role")

		assert.False(t, role.HasPermissionByName("user.create"))
	})

	t.Run("GetPermissionNames returns all permission names", func(t *testing.T) {
		role := NewRole("admin", "Administrator role")
		
		permission1 := NewPermission("user.create", "Create users", "user", "create")
		permission2 := NewPermission("user.read", "Read users", "user", "read")
		role.AddPermission(*permission1)
		role.AddPermission(*permission2)

		names := role.GetPermissionNames()

		assert.Len(t, names, 2)
		assert.Contains(t, names, "user.create")
		assert.Contains(t, names, "user.read")
	})

	t.Run("GetPermissionNames returns empty slice for role without permissions", func(t *testing.T) {
		role := NewRole("admin", "Administrator role")

		names := role.GetPermissionNames()

		assert.Empty(t, names)
	})
}

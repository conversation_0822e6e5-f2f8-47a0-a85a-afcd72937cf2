package service

import (
	"context"
	"fmt"

	"github.com/wongpinter/payment-gateway/internal/domain/account"
)

// UserService provides application layer services for user management
type UserService struct {
	userRepo       account.UserRepository
	roleRepo       account.RoleRepository
	passwordHasher account.PasswordHasher
	eventPublisher account.EventPublisher
}

// NewUserService creates a new UserService
func NewUserService(
	userRepo account.UserRepository,
	roleRepo account.RoleRepository,
	passwordHasher account.PasswordHasher,
	eventPublisher account.EventPublisher,
) *UserService {
	return &UserService{
		userRepo:       userRepo,
		roleRepo:       roleRepo,
		passwordHasher: passwordHasher,
		eventPublisher: eventPublisher,
	}
}

// RegisterUser registers a new user with the given email, password, and name
func (s *UserService) RegisterUser(ctx context.Context, email, password, name string) (*account.User, error) {
	// Validate email format
	emailVO, err := account.NewEmail(email)
	if err != nil {
		return nil, fmt.Errorf("user_service.RegisterUser: %w", err)
	}

	// Check if user already exists
	exists, err := s.userRepo.ExistsByEmail(ctx, emailVO)
	if err != nil {
		return nil, fmt.Errorf("user_service.RegisterUser: %w", err)
	}
	if exists {
		return nil, account.ErrUserAlreadyExists
	}

	// Hash the password
	hashedPassword, err := s.passwordHasher.Hash(password)
	if err != nil {
		return nil, fmt.Errorf("user_service.RegisterUser: %w", err)
	}

	passwordHash := account.NewPasswordHash(hashedPassword)

	// Create new user
	user := account.NewUser(emailVO, passwordHash, name)

	// Save user to repository
	if err := s.userRepo.Create(ctx, user); err != nil {
		return nil, fmt.Errorf("user_service.RegisterUser: %w", err)
	}

	// Assign default role (user role)
	defaultRole, err := s.roleRepo.GetByName(ctx, "user")
	if err != nil {
		// Log warning but don't fail registration if default role doesn't exist
		// In production, you might want to handle this differently
	} else {
		if err := s.userRepo.AssignRole(ctx, user.ID(), defaultRole.ID()); err != nil {
			// Log warning but don't fail registration
		}
	}

	// Publish domain event
	event := account.NewUserRegisteredEvent(user.ID(), user.Email(), user.Name())
	if err := s.eventPublisher.Publish(ctx, event); err != nil {
		// Log error but don't fail the operation
		// In production, you might want to use a reliable event publishing mechanism
	}

	return user, nil
}

// GetUserByID retrieves a user by their ID
func (s *UserService) GetUserByID(ctx context.Context, userID account.UserID) (*account.User, error) {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("user_service.GetUserByID: %w", err)
	}

	return user, nil
}

// GetUserByEmail retrieves a user by their email
func (s *UserService) GetUserByEmail(ctx context.Context, email string) (*account.User, error) {
	emailVO, err := account.NewEmail(email)
	if err != nil {
		return nil, fmt.Errorf("user_service.GetUserByEmail: %w", err)
	}

	user, err := s.userRepo.GetByEmail(ctx, emailVO)
	if err != nil {
		return nil, fmt.Errorf("user_service.GetUserByEmail: %w", err)
	}

	return user, nil
}

// UpdateUser updates user information
func (s *UserService) UpdateUser(ctx context.Context, userID account.UserID, name string, email string) (*account.User, error) {
	// Get existing user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("user_service.UpdateUser: %w", err)
	}

	// Validate new email if provided
	if email != "" && email != user.Email().String() {
		emailVO, err := account.NewEmail(email)
		if err != nil {
			return nil, fmt.Errorf("user_service.UpdateUser: %w", err)
		}

		// Check if new email is already taken
		exists, err := s.userRepo.ExistsByEmail(ctx, emailVO)
		if err != nil {
			return nil, fmt.Errorf("user_service.UpdateUser: %w", err)
		}
		if exists {
			return nil, account.ErrUserAlreadyExists
		}

		user.UpdateEmail(emailVO)
	}

	// Update name if provided
	if name != "" {
		user.UpdateName(name)
	}

	// Save updated user
	if err := s.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("user_service.UpdateUser: %w", err)
	}

	return user, nil
}

// ChangePassword changes a user's password
func (s *UserService) ChangePassword(ctx context.Context, userID account.UserID, currentPassword, newPassword string) error {
	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("user_service.ChangePassword: %w", err)
	}

	// Verify current password
	if err := s.passwordHasher.Verify(currentPassword, user.PasswordHash().String()); err != nil {
		return account.ErrInvalidCredentials
	}

	// Hash new password
	hashedPassword, err := s.passwordHasher.Hash(newPassword)
	if err != nil {
		return fmt.Errorf("user_service.ChangePassword: %w", err)
	}

	// Update password
	passwordHash := account.NewPasswordHash(hashedPassword)
	user.UpdatePassword(passwordHash)

	// Save user
	if err := s.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("user_service.ChangePassword: %w", err)
	}

	return nil
}

// DeactivateUser deactivates a user account
func (s *UserService) DeactivateUser(ctx context.Context, userID account.UserID) error {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("user_service.DeactivateUser: %w", err)
	}

	user.Deactivate()

	if err := s.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("user_service.DeactivateUser: %w", err)
	}

	return nil
}

// ActivateUser activates a user account
func (s *UserService) ActivateUser(ctx context.Context, userID account.UserID) error {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("user_service.ActivateUser: %w", err)
	}

	user.Activate()

	if err := s.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("user_service.ActivateUser: %w", err)
	}

	return nil
}

// AssignRole assigns a role to a user
func (s *UserService) AssignRole(ctx context.Context, userID account.UserID, roleID account.RoleID) error {
	// Verify user exists
	_, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("user_service.AssignRole: %w", err)
	}

	// Verify role exists
	role, err := s.roleRepo.GetByID(ctx, roleID)
	if err != nil {
		return fmt.Errorf("user_service.AssignRole: %w", err)
	}

	// Assign role
	if err := s.userRepo.AssignRole(ctx, userID, roleID); err != nil {
		return fmt.Errorf("user_service.AssignRole: %w", err)
	}

	// Publish domain event
	event := account.NewRoleAssignedEvent(userID, roleID, role.Name())
	if err := s.eventPublisher.Publish(ctx, event); err != nil {
		// Log error but don't fail the operation
	}

	return nil
}

// RevokeRole revokes a role from a user
func (s *UserService) RevokeRole(ctx context.Context, userID account.UserID, roleID account.RoleID) error {
	// Verify user exists
	_, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("user_service.RevokeRole: %w", err)
	}

	// Verify role exists and get role info
	role, err := s.roleRepo.GetByID(ctx, roleID)
	if err != nil {
		return fmt.Errorf("user_service.RevokeRole: %w", err)
	}

	// Revoke role
	if err := s.userRepo.RevokeRole(ctx, userID, roleID); err != nil {
		return fmt.Errorf("user_service.RevokeRole: %w", err)
	}

	// Publish domain event
	event := account.NewRoleRevokedEvent(userID, roleID, role.Name())
	if err := s.eventPublisher.Publish(ctx, event); err != nil {
		// Log error but don't fail the operation
	}

	return nil
}

// GetUserRoles retrieves all roles for a user
func (s *UserService) GetUserRoles(ctx context.Context, userID account.UserID) ([]account.Role, error) {
	roles, err := s.userRepo.GetUserRoles(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("user_service.GetUserRoles: %w", err)
	}

	return roles, nil
}

// ListUsers retrieves a paginated list of users
func (s *UserService) ListUsers(ctx context.Context, offset, limit int) ([]*account.User, int, error) {
	users, err := s.userRepo.List(ctx, offset, limit)
	if err != nil {
		return nil, 0, fmt.Errorf("user_service.ListUsers: %w", err)
	}

	total, err := s.userRepo.Count(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("user_service.ListUsers: %w", err)
	}

	return users, total, nil
}

-- Test seed data for users table
-- This file contains test data for the users table used in integration tests
-- Updated to match the new Clean Architecture schema
-- Passwords are Argon2 hashed version of 'password123'

-- Clear existing test users (if any)
DELETE FROM users WHERE id::text LIKE '770e8400-%';

INSERT INTO users (id, name, email, password_hash, last_login_at, is_active, created_at, updated_at) VALUES
    ('770e8400-e29b-41d4-a716-446655440001', 'Super Admin User', '<EMAIL>', '$argon2id$v=19$m=65536,t=3,p=2$c29tZXNhbHQ$qFpFdKDD6Z1CODcaxxmMMdFuRBK/uPiTC1vFzwIUHjU', 1640995200000, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('770e8400-e29b-41d4-a716-446655440002', 'Admin User', '<EMAIL>', '$argon2id$v=19$m=65536,t=3,p=2$c29tZXNhbHQ$qFpFdKDD6Z1CODcaxxmMMdFuRBK/uPiTC1vFzwIUHjU', 1640995200000, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('770e8400-e29b-41d4-a716-446655440003', 'User Manager', '<EMAIL>', '$argon2id$v=19$m=65536,t=3,p=2$c29tZXNhbHQ$qFpFdKDD6Z1CODcaxxmMMdFuRBK/uPiTC1vFzwIUHjU', 1640995200000, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('770e8400-e29b-41d4-a716-446655440004', 'Payment Manager', '<EMAIL>', '$argon2id$v=19$m=65536,t=3,p=2$c29tZXNhbHQ$qFpFdKDD6Z1CODcaxxmMMdFuRBK/uPiTC1vFzwIUHjU', 1640995200000, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('770e8400-e29b-41d4-a716-446655440005', 'Viewer User', '<EMAIL>', '$argon2id$v=19$m=65536,t=3,p=2$c29tZXNhbHQ$qFpFdKDD6Z1CODcaxxmMMdFuRBK/uPiTC1vFzwIUHjU', 1640995200000, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('770e8400-e29b-41d4-a716-446655440006', 'Regular User', '<EMAIL>', '$argon2id$v=19$m=65536,t=3,p=2$c29tZXNhbHQ$qFpFdKDD6Z1CODcaxxmMMdFuRBK/uPiTC1vFzwIUHjU', 1640995200000, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('770e8400-e29b-41d4-a716-446655440007', 'Inactive User', '<EMAIL>', '$argon2id$v=19$m=65536,t=3,p=2$c29tZXNhbHQ$qFpFdKDD6Z1CODcaxxmMMdFuRBK/uPiTC1vFzwIUHjU', NULL, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('770e8400-e29b-41d4-a716-446655440008', 'Test User 1', '<EMAIL>', '$argon2id$v=19$m=65536,t=3,p=2$c29tZXNhbHQ$qFpFdKDD6Z1CODcaxxmMMdFuRBK/uPiTC1vFzwIUHjU', NULL, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('770e8400-e29b-41d4-a716-446655440009', 'Test User 2', '<EMAIL>', '$argon2id$v=19$m=65536,t=3,p=2$c29tZXNhbHQ$qFpFdKDD6Z1CODcaxxmMMdFuRBK/uPiTC1vFzwIUHjU', NULL, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert user-role assignments for testing
-- Clear existing test user-role assignments
DELETE FROM user_roles WHERE user_id::text LIKE '770e8400-%';

INSERT INTO user_roles (user_id, role_id, created_at, updated_at) VALUES
    ('770e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), -- Super Admin User -> Super Administrator
    ('770e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440002', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), -- Admin User -> Administrator
    ('770e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440003', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), -- User Manager -> User Manager
    ('770e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440004', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), -- Payment Manager -> Payment Manager
    ('770e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440005', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), -- Viewer User -> Viewer
    ('770e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-446655440006', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), -- Regular User -> Regular User
    ('770e8400-e29b-41d4-a716-446655440007', '550e8400-e29b-41d4-a716-446655440007', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), -- Inactive User -> Inactive Role
    ('770e8400-e29b-41d4-a716-446655440008', '550e8400-e29b-41d4-a716-446655440006', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), -- Test User 1 -> Regular User
    ('770e8400-e29b-41d4-a716-446655440009', '550e8400-e29b-41d4-a716-446655440006', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP); -- Test User 2 -> Regular User

-- Insert some users with multiple roles for testing
INSERT INTO user_roles (user_id, role_id, created_at, updated_at) VALUES
    ('770e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440005', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), -- User Manager also has Viewer role
    ('770e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440005', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP); -- Payment Manager also has Viewer role

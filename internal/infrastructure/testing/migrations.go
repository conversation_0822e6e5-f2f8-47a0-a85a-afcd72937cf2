package testing

import (
	"context"
	"fmt"
	"os"
	"path/filepath"

	"github.com/golang-migrate/migrate/v4"
	_ "github.com/golang-migrate/migrate/v4/database/postgres"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/config"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/database"
)

// MigrationManager handles database migrations for testing
type MigrationManager struct {
	db     *database.DB
	config *config.DatabaseConfig
}

// NewMigrationManager creates a new migration manager
func NewMigrationManager(db *database.DB, config *config.DatabaseConfig) *MigrationManager {
	return &MigrationManager{
		db:     db,
		config: config,
	}
}

// findMigrationPath finds the migration directory by looking for go.mod
func (m *MigrationManager) findMigrationPath() (string, error) {
	// Start from current directory and walk up to find go.mod
	currentDir, err := os.Getwd()
	if err != nil {
		return "", fmt.Errorf("failed to get current directory: %w", err)
	}

	// Walk up the directory tree to find go.mod
	for {
		goModPath := filepath.Join(currentDir, "go.mod")
		if _, err := os.Stat(goModPath); err == nil {
			// Found go.mod, migrations should be in db/migrations
			migrationPath := filepath.Join(currentDir, "db", "migrations")
			if _, err := os.Stat(migrationPath); err == nil {
				return migrationPath, nil
			}
			return "", fmt.Errorf("migrations directory not found at %s", migrationPath)
		}

		// Move up one directory
		parentDir := filepath.Dir(currentDir)
		if parentDir == currentDir {
			// Reached root directory
			break
		}
		currentDir = parentDir
	}

	return "", fmt.Errorf("could not find project root (go.mod not found)")
}

// RunMigrations runs all up migrations
func (m *MigrationManager) RunMigrations(ctx context.Context) error {
	// Get absolute path to migrations - find the project root first
	migrationPath, err := m.findMigrationPath()
	if err != nil {
		return fmt.Errorf("MigrationManager.RunMigrations: failed to get migration path: %w", err)
	}

	// Create migrate instance
	migrator, err := migrate.New(
		fmt.Sprintf("file://%s", migrationPath),
		m.config.GetDatabaseURL(),
	)
	if err != nil {
		return fmt.Errorf("MigrationManager.RunMigrations: failed to create migrator: %w", err)
	}
	defer migrator.Close()

	// Run migrations
	if err := migrator.Up(); err != nil && err != migrate.ErrNoChange {
		return fmt.Errorf("MigrationManager.RunMigrations: failed to run migrations: %w", err)
	}

	return nil
}

// DropDatabase drops all tables and data
func (m *MigrationManager) DropDatabase(ctx context.Context) error {
	// Get absolute path to migrations - find the project root first
	migrationPath, err := m.findMigrationPath()
	if err != nil {
		return fmt.Errorf("MigrationManager.DropDatabase: failed to get migration path: %w", err)
	}

	// Create migrate instance
	migrator, err := migrate.New(
		fmt.Sprintf("file://%s", migrationPath),
		m.config.GetDatabaseURL(),
	)
	if err != nil {
		return fmt.Errorf("MigrationManager.DropDatabase: failed to create migrator: %w", err)
	}
	defer migrator.Close()

	// Drop all migrations
	if err := migrator.Drop(); err != nil {
		return fmt.Errorf("MigrationManager.DropDatabase: failed to drop database: %w", err)
	}

	return nil
}

// ResetDatabase drops and recreates the database schema
func (m *MigrationManager) ResetDatabase(ctx context.Context) error {
	// Drop database
	if err := m.DropDatabase(ctx); err != nil {
		return fmt.Errorf("MigrationManager.ResetDatabase: failed to drop database: %w", err)
	}

	// Run migrations
	if err := m.RunMigrations(ctx); err != nil {
		return fmt.Errorf("MigrationManager.ResetDatabase: failed to run migrations: %w", err)
	}

	return nil
}

// GetMigrationVersion returns the current migration version
func (m *MigrationManager) GetMigrationVersion(ctx context.Context) (uint, bool, error) {
	// Get absolute path to migrations
	migrationPath, err := filepath.Abs("db/migrations")
	if err != nil {
		return 0, false, fmt.Errorf("MigrationManager.GetMigrationVersion: failed to get migration path: %w", err)
	}

	// Create migrate instance
	migrator, err := migrate.New(
		fmt.Sprintf("file://%s", migrationPath),
		m.config.GetDatabaseDSN(),
	)
	if err != nil {
		return 0, false, fmt.Errorf("MigrationManager.GetMigrationVersion: failed to create migrator: %w", err)
	}
	defer migrator.Close()

	// Get version
	version, dirty, err := migrator.Version()
	if err != nil {
		return 0, false, fmt.Errorf("MigrationManager.GetMigrationVersion: failed to get version: %w", err)
	}

	return version, dirty, nil
}

// TestDatabaseSetup provides a complete database setup for tests
type TestDatabaseSetup struct {
	DB               *database.DB
	MigrationManager *MigrationManager
	Config           *config.DatabaseConfig
}

// NewTestDatabaseSetup creates a new test database setup
func NewTestDatabaseSetup(ctx context.Context, config *config.DatabaseConfig) (*TestDatabaseSetup, error) {
	// Create database connection
	db, err := database.New(ctx, config)
	if err != nil {
		return nil, fmt.Errorf("NewTestDatabaseSetup: failed to create database: %w", err)
	}

	// Create migration manager
	migrationManager := NewMigrationManager(db, config)

	// Run migrations
	if err := migrationManager.RunMigrations(ctx); err != nil {
		db.Close()
		return nil, fmt.Errorf("NewTestDatabaseSetup: failed to run migrations: %w", err)
	}

	return &TestDatabaseSetup{
		DB:               db,
		MigrationManager: migrationManager,
		Config:           config,
	}, nil
}

// Cleanup cleans up the test database setup
func (tds *TestDatabaseSetup) Cleanup(ctx context.Context) error {
	if tds.DB != nil {
		tds.DB.Close()
	}
	return nil
}

// Reset resets the database to a clean state
func (tds *TestDatabaseSetup) Reset(ctx context.Context) error {
	return tds.MigrationManager.ResetDatabase(ctx)
}

// TruncateAllTables truncates all tables (faster than full reset for tests)
func (tds *TestDatabaseSetup) TruncateAllTables(ctx context.Context) error {
	// Get all table names
	query := `
		SELECT tablename 
		FROM pg_tables 
		WHERE schemaname = 'public' 
		AND tablename != 'schema_migrations'
	`

	rows, err := tds.DB.Pool.Query(ctx, query)
	if err != nil {
		return fmt.Errorf("TruncateAllTables: failed to get table names: %w", err)
	}
	defer rows.Close()

	var tables []string
	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			return fmt.Errorf("TruncateAllTables: failed to scan table name: %w", err)
		}
		tables = append(tables, tableName)
	}

	// Truncate all tables
	if len(tables) > 0 {
		for _, table := range tables {
			_, err := tds.DB.Pool.Exec(ctx, fmt.Sprintf("TRUNCATE TABLE %s RESTART IDENTITY CASCADE", table))
			if err != nil {
				return fmt.Errorf("TruncateAllTables: failed to truncate table %s: %w", table, err)
			}
		}
	}

	return nil
}

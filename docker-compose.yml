version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      SENTRY_DSN: ${SENTRY_DSN}
      SERVER_PORT: 8080
      SERVER_MODE: development
      LOGGER_LEVEL: info
      DATABASE_URL: ***********************************************/payment?sslmode=disable
      DATABASE_MAX_CONNS: 10
      DATABASE_MIN_CONNS: 5
      DATABASE_MAX_CONN_LIFETIME: 30m
      DATABASE_MAX_CONN_IDLE_TIME: 5m
      REDIS_ADDRESS: redis:6379
      REDIS_PASSWORD: ""
      REDIS_DB: 0
    depends_on:
      - db
      - redis
    volumes:
      - .:/app # Mount the current directory into the container for live reloading (optional, for development)

  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: payment
      POSTGRES_USER: payment
      POSTGRES_PASSWORD: payment_dev_password
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  db_data:
  redis_data:
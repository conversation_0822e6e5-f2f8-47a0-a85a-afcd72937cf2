package repository

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"

	"github.com/wongpinter/payment-gateway/internal/domain/account"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/database/converter"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/database/sqlc"
)

// PermissionRepository implements the account.PermissionRepository interface
type PermissionRepository struct {
	db      *pgxpool.Pool
	queries *sqlc.Queries
}

// NewPermissionRepository creates a new PermissionRepository
func NewPermissionRepository(db *pgxpool.Pool) *PermissionRepository {
	return &PermissionRepository{
		db:      db,
		queries: sqlc.New(db),
	}
}

// Create saves a new permission to the repository
func (r *PermissionRepository) Create(ctx context.Context, permission *account.Permission) error {
	model := converter.PermissionToModel(permission)
	
	_, err := r.queries.CreatePermission(ctx, sqlc.CreatePermissionParams{
		ID:          model.ID,
		Name:        model.Name,
		Description: model.Description,
		Resource:    model.Resource,
		Action:      model.Action,
		IsActive:    model.IsActive,
		CreatedAt:   model.CreatedAt,
		UpdatedAt:   model.UpdatedAt,
	})
	
	if err != nil {
		return fmt.Errorf("permission_repository.Create: %w", err)
	}
	
	return nil
}

// GetByID retrieves a permission by its ID
func (r *PermissionRepository) GetByID(ctx context.Context, id account.PermissionID) (*account.Permission, error) {
	model, err := r.queries.GetPermissionByID(ctx, id.UUID())
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, account.ErrPermissionNotFound
		}
		return nil, fmt.Errorf("permission_repository.GetByID: %w", err)
	}
	
	permission, err := converter.ModelToPermission(model)
	if err != nil {
		return nil, fmt.Errorf("permission_repository.GetByID: %w", err)
	}
	
	return permission, nil
}

// GetByName retrieves a permission by its name
func (r *PermissionRepository) GetByName(ctx context.Context, name string) (*account.Permission, error) {
	model, err := r.queries.GetPermissionByName(ctx, name)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, account.ErrPermissionNotFound
		}
		return nil, fmt.Errorf("permission_repository.GetByName: %w", err)
	}
	
	permission, err := converter.ModelToPermission(model)
	if err != nil {
		return nil, fmt.Errorf("permission_repository.GetByName: %w", err)
	}
	
	return permission, nil
}

// Update updates an existing permission in the repository
func (r *PermissionRepository) Update(ctx context.Context, permission *account.Permission) error {
	model := converter.PermissionToModel(permission)
	
	_, err := r.queries.UpdatePermission(ctx, sqlc.UpdatePermissionParams{
		ID:          model.ID,
		Name:        model.Name,
		Description: model.Description,
		Resource:    model.Resource,
		Action:      model.Action,
		IsActive:    model.IsActive,
		UpdatedAt:   converter.TimeToPgTimestamptz(time.Now()),
	})
	
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return account.ErrPermissionNotFound
		}
		return fmt.Errorf("permission_repository.Update: %w", err)
	}
	
	return nil
}

// Delete removes a permission from the repository (soft delete)
func (r *PermissionRepository) Delete(ctx context.Context, id account.PermissionID) error {
	err := r.queries.DeletePermission(ctx, id.UUID())
	if err != nil {
		return fmt.Errorf("permission_repository.Delete: %w", err)
	}
	
	return nil
}

// List retrieves a list of permissions with pagination
func (r *PermissionRepository) List(ctx context.Context, offset, limit int) ([]*account.Permission, error) {
	models, err := r.queries.ListPermissions(ctx, sqlc.ListPermissionsParams{
		Limit:  int32(limit),
		Offset: int32(offset),
	})
	
	if err != nil {
		return nil, fmt.Errorf("permission_repository.List: %w", err)
	}
	
	permissions := make([]*account.Permission, 0, len(models))
	for _, model := range models {
		permission, err := converter.ModelToPermission(model)
		if err != nil {
			return nil, fmt.Errorf("permission_repository.List: %w", err)
		}
		permissions = append(permissions, permission)
	}
	
	return permissions, nil
}

// Count returns the total number of permissions
func (r *PermissionRepository) Count(ctx context.Context) (int, error) {
	count, err := r.queries.CountPermissions(ctx)
	if err != nil {
		return 0, fmt.Errorf("permission_repository.Count: %w", err)
	}
	
	return int(count), nil
}

// ExistsByName checks if a permission with the given name exists
func (r *PermissionRepository) ExistsByName(ctx context.Context, name string) (bool, error) {
	exists, err := r.queries.ExistsPermissionByName(ctx, name)
	if err != nil {
		return false, fmt.Errorf("permission_repository.ExistsByName: %w", err)
	}
	
	return exists, nil
}

// GetByResource retrieves all permissions for a specific resource
func (r *PermissionRepository) GetByResource(ctx context.Context, resource string) ([]*account.Permission, error) {
	models, err := r.queries.GetPermissionsByResource(ctx, resource)
	if err != nil {
		return nil, fmt.Errorf("permission_repository.GetByResource: %w", err)
	}
	
	permissions := make([]*account.Permission, 0, len(models))
	for _, model := range models {
		permission, err := converter.ModelToPermission(model)
		if err != nil {
			return nil, fmt.Errorf("permission_repository.GetByResource: %w", err)
		}
		permissions = append(permissions, permission)
	}
	
	return permissions, nil
}

// GetByResourceAndAction retrieves a permission by resource and action
func (r *PermissionRepository) GetByResourceAndAction(ctx context.Context, resource, action string) (*account.Permission, error) {
	model, err := r.queries.GetPermissionByResourceAndAction(ctx, sqlc.GetPermissionByResourceAndActionParams{
		Resource: resource,
		Action:   action,
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, account.ErrPermissionNotFound
		}
		return nil, fmt.Errorf("permission_repository.GetByResourceAndAction: %w", err)
	}
	
	permission, err := converter.ModelToPermission(model)
	if err != nil {
		return nil, fmt.Errorf("permission_repository.GetByResourceAndAction: %w", err)
	}
	
	return permission, nil
}

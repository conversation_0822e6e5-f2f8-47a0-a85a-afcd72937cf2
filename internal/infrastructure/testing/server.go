package testing

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"path/filepath"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/config"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/database"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/i18n"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/logger"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/middleware"
)

// TestServer represents a test server instance
type TestServer struct {
	Server           *httptest.Server
	Router           *gin.Engine
	Config           *config.Config
	DB               *database.DB
	SeedSetup        *TestSeedSetup
	MigrationManager *MigrationManager
	I18n             *i18n.I18n
	ctx              context.Context
}

// TestServerConfig holds configuration for test server setup
type TestServerConfig struct {
	UseContainers bool
	SeedDir       string
	LangDir       string
	LoadSeeds     bool
	LogLevel      string
}

// NewTestServer creates a new test server instance
func NewTestServer(ctx context.Context, cfg *TestServerConfig) (*TestServer, error) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	ts := &TestServer{
		ctx: ctx,
	}

	// Setup configuration
	if err := ts.setupConfig(cfg); err != nil {
		return nil, fmt.Errorf("NewTestServer: failed to setup config: %w", err)
	}

	// Setup database
	if err := ts.setupDatabase(ctx, cfg); err != nil {
		return nil, fmt.Errorf("NewTestServer: failed to setup database: %w", err)
	}

	// Setup i18n
	if err := ts.setupI18n(cfg); err != nil {
		return nil, fmt.Errorf("NewTestServer: failed to setup i18n: %w", err)
	}

	// Setup router and middleware
	if err := ts.setupRouter(); err != nil {
		return nil, fmt.Errorf("NewTestServer: failed to setup router: %w", err)
	}

	// Setup seeds
	if err := ts.setupSeeds(ctx, cfg); err != nil {
		return nil, fmt.Errorf("NewTestServer: failed to setup seeds: %w", err)
	}

	// Create HTTP test server
	ts.Server = httptest.NewServer(ts.Router)

	return ts, nil
}

// setupConfig creates test configuration
func (ts *TestServer) setupConfig(cfg *TestServerConfig) error {
	// Create test configuration
	ts.Config = &config.Config{
		Server: config.ServerConfig{
			Host:         "localhost",
			Port:         8080,
			ReadTimeout:  30 * time.Second,
			WriteTimeout: 30 * time.Second,
			IdleTimeout:  60 * time.Second,
			CORS: config.CORSConfig{
				AllowedOrigins:   []string{"*"},
				AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
				AllowedHeaders:   []string{"*"},
				ExposedHeaders:   []string{"X-Correlation-ID"},
				AllowCredentials: true,
				MaxAge:           86400,
			},
		},
		Logger: config.LoggerConfig{
			Level:  cfg.LogLevel,
			Format: "console",
		},
		I18n: config.I18nConfig{
			DefaultLanguage:      "en",
			FallbackLanguage:     "en",
			SupportedLanguages:   []string{"en", "id"},
			LanguageDirectory:    cfg.LangDir,
		},
		JWT: config.JWTConfig{
			Secret:           "test-secret-key-for-testing-only",
			AccessTokenTTL:   15 * time.Minute,
			RefreshTokenTTL:  24 * time.Hour,
		},
	}

	return nil
}

// setupDatabase sets up test database
func (ts *TestServer) setupDatabase(ctx context.Context, cfg *TestServerConfig) error {
	var dbConfig *config.DatabaseConfig

	if cfg.UseContainers {
		// Use containerized database
		dbConfig = GetTestDatabaseConfig()
		if dbConfig == nil {
			return fmt.Errorf("test containers not initialized")
		}
	} else {
		// Use in-memory or local test database
		dbConfig = &config.DatabaseConfig{
			Host:            "localhost",
			Port:            5432,
			Name:            "payment_gateway_test",
			User:            "test_user",
			Password:        "test_password",
			SSLMode:         "disable",
			MaxOpenConns:    5,
			MaxIdleConns:    2,
			ConnMaxLifetime: 5 * time.Minute,
			ConnMaxIdleTime: 5 * time.Minute,
			MigrationPath:   "file://db/migrations",
		}
	}

	// Create database connection
	db, err := database.New(ctx, dbConfig)
	if err != nil {
		return fmt.Errorf("failed to create database connection: %w", err)
	}

	ts.DB = db
	ts.Config.Database = *dbConfig

	// Setup migration manager
	ts.MigrationManager = NewMigrationManager(db, dbConfig)

	// Run migrations
	if err := ts.MigrationManager.RunMigrations(ctx); err != nil {
		return fmt.Errorf("failed to run migrations: %w", err)
	}

	return nil
}

// setupI18n sets up internationalization
func (ts *TestServer) setupI18n(cfg *TestServerConfig) error {
	langDir := cfg.LangDir
	if langDir == "" {
		langDir = "configs/lang"
	}

	// Get absolute path
	absLangDir, err := filepath.Abs(langDir)
	if err != nil {
		return fmt.Errorf("failed to get absolute language directory path: %w", err)
	}

	// Create i18n instance
	i18nInstance, err := i18n.New(&ts.Config.I18n, absLangDir)
	if err != nil {
		return fmt.Errorf("failed to create i18n instance: %w", err)
	}

	ts.I18n = i18nInstance

	// Initialize global i18n
	if err := i18n.Init(&ts.Config.I18n, absLangDir); err != nil {
		return fmt.Errorf("failed to initialize global i18n: %w", err)
	}

	return nil
}

// setupRouter sets up Gin router with middleware
func (ts *TestServer) setupRouter() error {
	// Create router
	ts.Router = gin.New()

	// Setup middleware chain
	middleware.SetupMiddlewareChain(ts.Router, ts.Config)

	// Add health check endpoint
	ts.Router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "ok",
			"timestamp": time.Now().Unix(),
		})
	})

	return nil
}

// setupSeeds sets up test seed data
func (ts *TestServer) setupSeeds(ctx context.Context, cfg *TestServerConfig) error {
	seedDir := cfg.SeedDir
	if seedDir == "" {
		seedDir = "testdata/seeds"
	}

	// Get absolute path
	absSeedDir, err := filepath.Abs(seedDir)
	if err != nil {
		return fmt.Errorf("failed to get absolute seed directory path: %w", err)
	}

	ts.SeedSetup = NewTestSeedSetup(ts.DB, absSeedDir)

	// Load seeds if requested
	if cfg.LoadSeeds {
		if err := ts.SeedSetup.LoadBasicSeeds(ctx); err != nil {
			return fmt.Errorf("failed to load basic seeds: %w", err)
		}
	}

	return nil
}

// Close closes the test server and cleans up resources
func (ts *TestServer) Close() error {
	var errs []error

	// Close HTTP server
	if ts.Server != nil {
		ts.Server.Close()
	}

	// Close database
	if ts.DB != nil {
		ts.DB.Close()
	}

	if len(errs) > 0 {
		return fmt.Errorf("cleanup errors: %v", errs)
	}

	return nil
}

// Reset resets the test server to a clean state
func (ts *TestServer) Reset(ctx context.Context) error {
	// Reset database
	if ts.MigrationManager != nil {
		if err := ts.MigrationManager.ResetDatabase(ctx); err != nil {
			return fmt.Errorf("failed to reset database: %w", err)
		}
	}

	// Reload basic seeds
	if ts.SeedSetup != nil {
		if err := ts.SeedSetup.LoadBasicSeeds(ctx); err != nil {
			return fmt.Errorf("failed to reload seeds: %w", err)
		}
	}

	return nil
}

// ClearData clears all test data but keeps schema
func (ts *TestServer) ClearData(ctx context.Context) error {
	if ts.SeedSetup != nil {
		return ts.SeedSetup.SeedManager.ClearAllData(ctx)
	}
	return nil
}

// LoadSeeds loads specific seed data
func (ts *TestServer) LoadSeeds(ctx context.Context, seedNames ...string) error {
	if ts.SeedSetup != nil {
		return ts.SeedSetup.SeedManager.LoadSpecificSeeds(ctx, seedNames...)
	}
	return nil
}

// GetURL returns the test server URL
func (ts *TestServer) GetURL() string {
	if ts.Server != nil {
		return ts.Server.URL
	}
	return ""
}

// GetClient returns an HTTP client configured for the test server
func (ts *TestServer) GetClient() *http.Client {
	return ts.Server.Client()
}

// DefaultTestServerConfig returns default configuration for test server
func DefaultTestServerConfig() *TestServerConfig {
	return &TestServerConfig{
		UseContainers: true,
		SeedDir:       "testdata/seeds",
		LangDir:       "configs/lang",
		LoadSeeds:     true,
		LogLevel:      "info",
	}
}

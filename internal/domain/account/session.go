package account

import (
	"time"

	"github.com/google/uuid"
)

// SessionID represents a unique identifier for a session
type SessionID struct {
	value uuid.UUID
}

// NewSessionID creates a new SessionID
func NewSessionID() SessionID {
	return SessionID{value: uuid.New()}
}

// SessionIDFromString creates a SessionID from a string
func SessionIDFromString(s string) (SessionID, error) {
	id, err := uuid.Parse(s)
	if err != nil {
		return SessionID{}, err
	}
	return SessionID{value: id}, nil
}

// UUID returns the underlying UUID
func (id SessionID) UUID() uuid.UUID {
	return id.value
}

// String returns the string representation of the SessionID
func (id SessionID) String() string {
	return id.value.String()
}

// Session represents a user session for JWT refresh tokens
type Session struct {
	id           SessionID
	userID       UserID
	refreshToken string
	expiresAt    time.Time
	createdAt    time.Time
	updatedAt    time.Time
}

// NewSession creates a new Session
func NewSession(userID UserID, refreshToken string, ttl time.Duration) *Session {
	now := time.Now()
	return &Session{
		id:           NewSessionID(),
		userID:       userID,
		refreshToken: refreshToken,
		expiresAt:    now.Add(ttl),
		createdAt:    now,
		updatedAt:    now,
	}
}

// NewSessionWithID creates a Session with all fields specified (for repository use)
func NewSessionWithID(id SessionID, userID UserID, refreshToken string, expiresAt, createdAt, updatedAt time.Time) *Session {
	return &Session{
		id:           id,
		userID:       userID,
		refreshToken: refreshToken,
		expiresAt:    expiresAt,
		createdAt:    createdAt,
		updatedAt:    updatedAt,
	}
}

// ID returns the session's ID
func (s *Session) ID() SessionID {
	return s.id
}

// UserID returns the user ID associated with this session
func (s *Session) UserID() UserID {
	return s.userID
}

// RefreshToken returns the refresh token
func (s *Session) RefreshToken() string {
	return s.refreshToken
}

// ExpiresAt returns when the session expires
func (s *Session) ExpiresAt() time.Time {
	return s.expiresAt
}

// CreatedAt returns when the session was created
func (s *Session) CreatedAt() time.Time {
	return s.createdAt
}

// UpdatedAt returns when the session was last updated
func (s *Session) UpdatedAt() time.Time {
	return s.updatedAt
}

// IsExpired checks if the session has expired
func (s *Session) IsExpired() bool {
	return time.Now().After(s.expiresAt)
}

// UpdateRefreshToken updates the refresh token and extends the expiration
func (s *Session) UpdateRefreshToken(newRefreshToken string, ttl time.Duration) {
	s.refreshToken = newRefreshToken
	s.expiresAt = time.Now().Add(ttl)
	s.updatedAt = time.Now()
}

// Extend extends the session expiration time
func (s *Session) Extend(ttl time.Duration) {
	s.expiresAt = time.Now().Add(ttl)
	s.updatedAt = time.Now()
}

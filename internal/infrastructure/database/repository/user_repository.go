package repository

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"

	"github.com/wongpinter/payment-gateway/internal/domain/account"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/database/converter"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/database/sqlc"
)

// UserRepository implements the account.UserRepository interface
type UserRepository struct {
	db      *pgxpool.Pool
	queries *sqlc.Queries
}

// NewUserRepository creates a new UserRepository
func NewUserRepository(db *pgxpool.Pool) *UserRepository {
	return &UserRepository{
		db:      db,
		queries: sqlc.New(db),
	}
}

// Create saves a new user to the repository
func (r *UserRepository) Create(ctx context.Context, user *account.User) error {
	model := converter.UserToModel(user)
	
	_, err := r.queries.CreateUser(ctx, sqlc.CreateUserParams{
		ID:           model.ID,
		Name:         model.Name,
		Email:        model.Email,
		PasswordHash: model.PasswordHash,
		IsActive:     model.IsActive,
		CreatedAt:    model.CreatedAt,
		UpdatedAt:    model.UpdatedAt,
	})
	
	if err != nil {
		return fmt.Errorf("user_repository.Create: %w", err)
	}
	
	return nil
}

// GetByID retrieves a user by their ID
func (r *UserRepository) GetByID(ctx context.Context, id account.UserID) (*account.User, error) {
	model, err := r.queries.GetUserByID(ctx, id.UUID())
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, account.ErrUserNotFound
		}
		return nil, fmt.Errorf("user_repository.GetByID: %w", err)
	}
	
	user, err := converter.ModelToUser(model)
	if err != nil {
		return nil, fmt.Errorf("user_repository.GetByID: %w", err)
	}
	
	return user, nil
}

// GetByEmail retrieves a user by their email address
func (r *UserRepository) GetByEmail(ctx context.Context, email account.Email) (*account.User, error) {
	model, err := r.queries.GetUserByEmail(ctx, email.String())
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, account.ErrUserNotFound
		}
		return nil, fmt.Errorf("user_repository.GetByEmail: %w", err)
	}
	
	user, err := converter.ModelToUser(model)
	if err != nil {
		return nil, fmt.Errorf("user_repository.GetByEmail: %w", err)
	}
	
	return user, nil
}

// Update updates an existing user in the repository
func (r *UserRepository) Update(ctx context.Context, user *account.User) error {
	model := converter.UserToModel(user)
	
	_, err := r.queries.UpdateUser(ctx, sqlc.UpdateUserParams{
		ID:           model.ID,
		Name:         model.Name,
		Email:        model.Email,
		PasswordHash: model.PasswordHash,
		IsActive:     model.IsActive,
		UpdatedAt:    converter.TimeToPgTimestamptz(time.Now()),
	})
	
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return account.ErrUserNotFound
		}
		return fmt.Errorf("user_repository.Update: %w", err)
	}
	
	return nil
}

// Delete removes a user from the repository (soft delete)
func (r *UserRepository) Delete(ctx context.Context, id account.UserID) error {
	err := r.queries.DeleteUser(ctx, id.UUID())
	if err != nil {
		return fmt.Errorf("user_repository.Delete: %w", err)
	}
	
	return nil
}

// List retrieves a list of users with pagination
func (r *UserRepository) List(ctx context.Context, offset, limit int) ([]*account.User, error) {
	models, err := r.queries.ListUsers(ctx, sqlc.ListUsersParams{
		Limit:  int32(limit),
		Offset: int32(offset),
	})
	
	if err != nil {
		return nil, fmt.Errorf("user_repository.List: %w", err)
	}
	
	users := make([]*account.User, 0, len(models))
	for _, model := range models {
		user, err := converter.ModelToUser(model)
		if err != nil {
			return nil, fmt.Errorf("user_repository.List: %w", err)
		}
		users = append(users, user)
	}
	
	return users, nil
}

// Count returns the total number of users
func (r *UserRepository) Count(ctx context.Context) (int, error) {
	count, err := r.queries.CountUsers(ctx)
	if err != nil {
		return 0, fmt.Errorf("user_repository.Count: %w", err)
	}
	
	return int(count), nil
}

// ExistsByEmail checks if a user with the given email exists
func (r *UserRepository) ExistsByEmail(ctx context.Context, email account.Email) (bool, error) {
	exists, err := r.queries.ExistsByEmail(ctx, email.String())
	if err != nil {
		return false, fmt.Errorf("user_repository.ExistsByEmail: %w", err)
	}
	
	return exists, nil
}

// GetUserRoles retrieves all roles for a specific user
func (r *UserRepository) GetUserRoles(ctx context.Context, userID account.UserID) ([]account.Role, error) {
	models, err := r.queries.GetUserRoles(ctx, userID.UUID())
	if err != nil {
		return nil, fmt.Errorf("user_repository.GetUserRoles: %w", err)
	}
	
	roles := make([]account.Role, 0, len(models))
	for _, model := range models {
		role, err := converter.ModelToRole(model)
		if err != nil {
			return nil, fmt.Errorf("user_repository.GetUserRoles: %w", err)
		}
		roles = append(roles, *role)
	}
	
	return roles, nil
}

// AssignRole assigns a role to a user
func (r *UserRepository) AssignRole(ctx context.Context, userID account.UserID, roleID account.RoleID) error {
	err := r.queries.AssignUserRole(ctx, sqlc.AssignUserRoleParams{
		UserID: userID.UUID(),
		RoleID: roleID.UUID(),
	})
	
	if err != nil {
		return fmt.Errorf("user_repository.AssignRole: %w", err)
	}
	
	return nil
}

// RevokeRole revokes a role from a user
func (r *UserRepository) RevokeRole(ctx context.Context, userID account.UserID, roleID account.RoleID) error {
	err := r.queries.RevokeUserRole(ctx, sqlc.RevokeUserRoleParams{
		UserID: userID.UUID(),
		RoleID: roleID.UUID(),
	})
	
	if err != nil {
		return fmt.Errorf("user_repository.RevokeRole: %w", err)
	}
	
	return nil
}

// GetUsersWithRole retrieves all users that have a specific role
func (r *UserRepository) GetUsersWithRole(ctx context.Context, roleID account.RoleID) ([]*account.User, error) {
	models, err := r.queries.GetUsersWithRole(ctx, roleID.UUID())
	if err != nil {
		return nil, fmt.Errorf("user_repository.GetUsersWithRole: %w", err)
	}
	
	users := make([]*account.User, 0, len(models))
	for _, model := range models {
		user, err := converter.ModelToUser(model)
		if err != nil {
			return nil, fmt.Errorf("user_repository.GetUsersWithRole: %w", err)
		}
		users = append(users, user)
	}
	
	return users, nil
}

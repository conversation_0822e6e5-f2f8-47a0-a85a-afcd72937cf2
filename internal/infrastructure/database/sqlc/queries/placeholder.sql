-- Placeholder queries for initial sqlc generation
-- These will be replaced with actual queries as we implement bounded contexts

-- name: GetUserByID :one
SELECT id, name, email, role_id, last_login_at, status, created_at, updated_at
FROM users 
WHERE id = $1 AND deleted_at IS NULL;

-- name: GetUserByEmail :one
SELECT id, name, email, password_hash, role_id, last_login_at, status, created_at, updated_at
FROM users 
WHERE email = $1 AND deleted_at IS NULL;

-- name: CreateUser :one
INSERT INTO users (name, email, password_hash, role_id)
VALUES ($1, $2, $3, $4)
RETURNING id, name, email, role_id, status, created_at, updated_at;

-- name: UpdateUserLastLogin :exec
UPDATE users 
SET last_login_at = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND deleted_at IS NULL;

-- name: GetRoleByID :one
SELECT id, code, name, description, status, created_at, updated_at
FROM roles 
WHERE id = $1 AND deleted_at IS NULL;

-- name: GetRoleByCode :one
SELECT id, code, name, description, status, created_at, updated_at
FROM roles 
WHERE code = $1 AND deleted_at IS NULL;

-- name: ListActiveRoles :many
SELECT id, code, name, description, status, created_at, updated_at
FROM roles 
WHERE status = true AND deleted_at IS NULL
ORDER BY name;

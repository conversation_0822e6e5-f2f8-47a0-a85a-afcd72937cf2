// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: users.sql

package sqlc

import (
	"context"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
)

const assignUserRole = `-- name: AssignUserRole :exec
INSERT INTO user_roles (id, user_id, role_id, created_at, updated_at)
VALUES (gen_random_uuid(), $1, $2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (user_id, role_id) DO NOTHING
`

type AssignUserRoleParams struct {
	UserID uuid.UUID `db:"user_id" json:"user_id"`
	RoleID uuid.UUID `db:"role_id" json:"role_id"`
}

func (q *Queries) AssignUserRole(ctx context.Context, arg AssignUserRoleParams) error {
	_, err := q.db.Exec(ctx, assignUserRole, arg.UserID, arg.RoleID)
	return err
}

const countUsers = `-- name: CountUsers :one
SELECT COUNT(*) FROM users 
WHERE deleted_at IS NULL
`

func (q *Queries) CountUsers(ctx context.Context) (int64, error) {
	row := q.db.QueryRow(ctx, countUsers)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createUser = `-- name: CreateUser :one
INSERT INTO users (
    id, name, email, password_hash, is_active, created_at, updated_at
) VALUES (
    $1, $2, $3, $4, $5, $6, $7
) RETURNING id, name, email, password_hash, last_login_at, created_at, updated_at, deleted_at, is_active
`

type CreateUserParams struct {
	ID           uuid.UUID          `db:"id" json:"id"`
	Name         string             `db:"name" json:"name"`
	Email        string             `db:"email" json:"email"`
	PasswordHash string             `db:"password_hash" json:"password_hash"`
	IsActive     bool               `db:"is_active" json:"is_active"`
	CreatedAt    pgtype.Timestamptz `db:"created_at" json:"created_at"`
	UpdatedAt    pgtype.Timestamptz `db:"updated_at" json:"updated_at"`
}

func (q *Queries) CreateUser(ctx context.Context, arg CreateUserParams) (User, error) {
	row := q.db.QueryRow(ctx, createUser,
		arg.ID,
		arg.Name,
		arg.Email,
		arg.PasswordHash,
		arg.IsActive,
		arg.CreatedAt,
		arg.UpdatedAt,
	)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.PasswordHash,
		&i.LastLoginAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.IsActive,
	)
	return i, err
}

const deleteUser = `-- name: DeleteUser :exec
UPDATE users 
SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND deleted_at IS NULL
`

func (q *Queries) DeleteUser(ctx context.Context, id uuid.UUID) error {
	_, err := q.db.Exec(ctx, deleteUser, id)
	return err
}

const existsByEmail = `-- name: ExistsByEmail :one
SELECT EXISTS(
    SELECT 1 FROM users 
    WHERE email = $1 AND deleted_at IS NULL
)
`

func (q *Queries) ExistsByEmail(ctx context.Context, email string) (bool, error) {
	row := q.db.QueryRow(ctx, existsByEmail, email)
	var exists bool
	err := row.Scan(&exists)
	return exists, err
}

const getUserByEmail = `-- name: GetUserByEmail :one
SELECT id, name, email, password_hash, last_login_at, created_at, updated_at, deleted_at, is_active FROM users 
WHERE email = $1 AND deleted_at IS NULL
`

func (q *Queries) GetUserByEmail(ctx context.Context, email string) (User, error) {
	row := q.db.QueryRow(ctx, getUserByEmail, email)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.PasswordHash,
		&i.LastLoginAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.IsActive,
	)
	return i, err
}

const getUserByID = `-- name: GetUserByID :one
SELECT id, name, email, password_hash, last_login_at, created_at, updated_at, deleted_at, is_active FROM users 
WHERE id = $1 AND deleted_at IS NULL
`

func (q *Queries) GetUserByID(ctx context.Context, id uuid.UUID) (User, error) {
	row := q.db.QueryRow(ctx, getUserByID, id)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.PasswordHash,
		&i.LastLoginAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.IsActive,
	)
	return i, err
}

const getUserRoles = `-- name: GetUserRoles :many
SELECT r.id, r.name, r.description, r.created_at, r.updated_at, r.deleted_at, r.is_active FROM roles r
INNER JOIN user_roles ur ON r.id = ur.role_id
WHERE ur.user_id = $1 
    AND ur.deleted_at IS NULL 
    AND r.deleted_at IS NULL
    AND r.is_active = TRUE
ORDER BY r.name
`

func (q *Queries) GetUserRoles(ctx context.Context, userID uuid.UUID) ([]Role, error) {
	rows, err := q.db.Query(ctx, getUserRoles, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Role{}
	for rows.Next() {
		var i Role
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.IsActive,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserWithRoles = `-- name: GetUserWithRoles :one
SELECT 
    u.id,
    u.name,
    u.email,
    u.password_hash,
    u.is_active,
    u.last_login_at,
    u.created_at,
    u.updated_at,
    COALESCE(
        JSON_AGG(
            JSON_BUILD_OBJECT(
                'id', r.id,
                'name', r.name,
                'description', r.description,
                'is_active', r.is_active
            )
        ) FILTER (WHERE r.id IS NOT NULL), 
        '[]'::json
    ) as roles
FROM users u
LEFT JOIN user_roles ur ON u.id = ur.user_id AND ur.deleted_at IS NULL
LEFT JOIN roles r ON ur.role_id = r.id AND r.deleted_at IS NULL AND r.is_active = TRUE
WHERE u.id = $1 AND u.deleted_at IS NULL
GROUP BY u.id, u.name, u.email, u.password_hash, u.is_active, u.last_login_at, u.created_at, u.updated_at
`

type GetUserWithRolesRow struct {
	ID           uuid.UUID          `db:"id" json:"id"`
	Name         string             `db:"name" json:"name"`
	Email        string             `db:"email" json:"email"`
	PasswordHash string             `db:"password_hash" json:"password_hash"`
	IsActive     bool               `db:"is_active" json:"is_active"`
	LastLoginAt  pgtype.Int8        `db:"last_login_at" json:"last_login_at"`
	CreatedAt    pgtype.Timestamptz `db:"created_at" json:"created_at"`
	UpdatedAt    pgtype.Timestamptz `db:"updated_at" json:"updated_at"`
	Roles        interface{}        `db:"roles" json:"roles"`
}

func (q *Queries) GetUserWithRoles(ctx context.Context, id uuid.UUID) (GetUserWithRolesRow, error) {
	row := q.db.QueryRow(ctx, getUserWithRoles, id)
	var i GetUserWithRolesRow
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.PasswordHash,
		&i.IsActive,
		&i.LastLoginAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Roles,
	)
	return i, err
}

const getUsersWithRole = `-- name: GetUsersWithRole :many
SELECT u.id, u.name, u.email, u.password_hash, u.last_login_at, u.created_at, u.updated_at, u.deleted_at, u.is_active FROM users u
INNER JOIN user_roles ur ON u.id = ur.user_id
WHERE ur.role_id = $1 
    AND ur.deleted_at IS NULL 
    AND u.deleted_at IS NULL
    AND u.is_active = TRUE
ORDER BY u.name
`

func (q *Queries) GetUsersWithRole(ctx context.Context, roleID uuid.UUID) ([]User, error) {
	rows, err := q.db.Query(ctx, getUsersWithRole, roleID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []User{}
	for rows.Next() {
		var i User
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Email,
			&i.PasswordHash,
			&i.LastLoginAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.IsActive,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listUsers = `-- name: ListUsers :many
SELECT id, name, email, password_hash, last_login_at, created_at, updated_at, deleted_at, is_active FROM users 
WHERE deleted_at IS NULL
ORDER BY created_at DESC
LIMIT $1 OFFSET $2
`

type ListUsersParams struct {
	Limit  int32 `db:"limit" json:"limit"`
	Offset int32 `db:"offset" json:"offset"`
}

func (q *Queries) ListUsers(ctx context.Context, arg ListUsersParams) ([]User, error) {
	rows, err := q.db.Query(ctx, listUsers, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []User{}
	for rows.Next() {
		var i User
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Email,
			&i.PasswordHash,
			&i.LastLoginAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.IsActive,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const revokeUserRole = `-- name: RevokeUserRole :exec
UPDATE user_roles 
SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
WHERE user_id = $1 AND role_id = $2 AND deleted_at IS NULL
`

type RevokeUserRoleParams struct {
	UserID uuid.UUID `db:"user_id" json:"user_id"`
	RoleID uuid.UUID `db:"role_id" json:"role_id"`
}

func (q *Queries) RevokeUserRole(ctx context.Context, arg RevokeUserRoleParams) error {
	_, err := q.db.Exec(ctx, revokeUserRole, arg.UserID, arg.RoleID)
	return err
}

const updateLastLogin = `-- name: UpdateLastLogin :exec
UPDATE users
SET last_login_at = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND deleted_at IS NULL
`

type UpdateLastLoginParams struct {
	ID          uuid.UUID   `db:"id" json:"id"`
	LastLoginAt pgtype.Int8 `db:"last_login_at" json:"last_login_at"`
}

func (q *Queries) UpdateLastLogin(ctx context.Context, arg UpdateLastLoginParams) error {
	_, err := q.db.Exec(ctx, updateLastLogin, arg.ID, arg.LastLoginAt)
	return err
}

const updateUser = `-- name: UpdateUser :one
UPDATE users 
SET 
    name = $2,
    email = $3,
    password_hash = $4,
    is_active = $5,
    updated_at = $6
WHERE id = $1 AND deleted_at IS NULL
RETURNING id, name, email, password_hash, last_login_at, created_at, updated_at, deleted_at, is_active
`

type UpdateUserParams struct {
	ID           uuid.UUID          `db:"id" json:"id"`
	Name         string             `db:"name" json:"name"`
	Email        string             `db:"email" json:"email"`
	PasswordHash string             `db:"password_hash" json:"password_hash"`
	IsActive     bool               `db:"is_active" json:"is_active"`
	UpdatedAt    pgtype.Timestamptz `db:"updated_at" json:"updated_at"`
}

func (q *Queries) UpdateUser(ctx context.Context, arg UpdateUserParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUser,
		arg.ID,
		arg.Name,
		arg.Email,
		arg.PasswordHash,
		arg.IsActive,
		arg.UpdatedAt,
	)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.PasswordHash,
		&i.LastLoginAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.IsActive,
	)
	return i, err
}

package redis

import (
	"fmt"

	"github.com/redis/go-redis/v9"

	"github.com/wongpinter/payment-gateway/internal/infrastructure/config"
)

// New creates a new Redis client
func New(cfg *config.RedisConfig) (*redis.Client, error) {
	// Create Redis client options
	opts := &redis.Options{
		Addr:            fmt.Sprintf("%s:%d", cfg.Host, cfg.Port),
		Password:        cfg.Password,
		DB:              cfg.DB,
		PoolSize:        cfg.PoolSize,
		MinIdleConns:    cfg.MinIdleConns,
		DialTimeout:     cfg.DialTimeout,
		ReadTimeout:     cfg.ReadTimeout,
		WriteTimeout:    cfg.WriteTimeout,
		PoolTimeout:     cfg.PoolTimeout,
		ConnMaxIdleTime: cfg.IdleTimeout,
	}

	// Create Redis client
	client := redis.NewClient(opts)

	return client, nil
}

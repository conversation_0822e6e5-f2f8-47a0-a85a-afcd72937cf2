// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: roles.sql

package sqlc

import (
	"context"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
)

const assignRolePermission = `-- name: AssignRolePermission :exec
INSERT INTO role_permissions (id, role_id, permission_id, created_at, updated_at)
VALUES (gen_random_uuid(), $1, $2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (role_id, permission_id) DO NOTHING
`

type AssignRolePermissionParams struct {
	RoleID       uuid.UUID `db:"role_id" json:"role_id"`
	PermissionID uuid.UUID `db:"permission_id" json:"permission_id"`
}

func (q *Queries) AssignRolePermission(ctx context.Context, arg AssignRolePermissionParams) error {
	_, err := q.db.Exec(ctx, assignRolePermission, arg.RoleID, arg.PermissionID)
	return err
}

const countRoles = `-- name: CountRoles :one
SELECT COUNT(*) FROM roles 
WHERE deleted_at IS NULL
`

func (q *Queries) CountRoles(ctx context.Context) (int64, error) {
	row := q.db.QueryRow(ctx, countRoles)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createRole = `-- name: CreateRole :one
INSERT INTO roles (
    id, name, description, is_active, created_at, updated_at
) VALUES (
    $1, $2, $3, $4, $5, $6
) RETURNING id, name, description, created_at, updated_at, deleted_at, is_active
`

type CreateRoleParams struct {
	ID          uuid.UUID          `db:"id" json:"id"`
	Name        string             `db:"name" json:"name"`
	Description pgtype.Text        `db:"description" json:"description"`
	IsActive    bool               `db:"is_active" json:"is_active"`
	CreatedAt   pgtype.Timestamptz `db:"created_at" json:"created_at"`
	UpdatedAt   pgtype.Timestamptz `db:"updated_at" json:"updated_at"`
}

func (q *Queries) CreateRole(ctx context.Context, arg CreateRoleParams) (Role, error) {
	row := q.db.QueryRow(ctx, createRole,
		arg.ID,
		arg.Name,
		arg.Description,
		arg.IsActive,
		arg.CreatedAt,
		arg.UpdatedAt,
	)
	var i Role
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.IsActive,
	)
	return i, err
}

const deleteRole = `-- name: DeleteRole :exec
UPDATE roles 
SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND deleted_at IS NULL
`

func (q *Queries) DeleteRole(ctx context.Context, id uuid.UUID) error {
	_, err := q.db.Exec(ctx, deleteRole, id)
	return err
}

const existsRoleByName = `-- name: ExistsRoleByName :one
SELECT EXISTS(
    SELECT 1 FROM roles 
    WHERE name = $1 AND deleted_at IS NULL
)
`

func (q *Queries) ExistsRoleByName(ctx context.Context, name string) (bool, error) {
	row := q.db.QueryRow(ctx, existsRoleByName, name)
	var exists bool
	err := row.Scan(&exists)
	return exists, err
}

const getRoleByID = `-- name: GetRoleByID :one
SELECT id, name, description, created_at, updated_at, deleted_at, is_active FROM roles 
WHERE id = $1 AND deleted_at IS NULL
`

func (q *Queries) GetRoleByID(ctx context.Context, id uuid.UUID) (Role, error) {
	row := q.db.QueryRow(ctx, getRoleByID, id)
	var i Role
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.IsActive,
	)
	return i, err
}

const getRoleByName = `-- name: GetRoleByName :one
SELECT id, name, description, created_at, updated_at, deleted_at, is_active FROM roles 
WHERE name = $1 AND deleted_at IS NULL
`

func (q *Queries) GetRoleByName(ctx context.Context, name string) (Role, error) {
	row := q.db.QueryRow(ctx, getRoleByName, name)
	var i Role
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.IsActive,
	)
	return i, err
}

const getRolePermissions = `-- name: GetRolePermissions :many
SELECT p.id, p.name, p.description, p.resource, p.action, p.is_active, p.created_at, p.updated_at, p.deleted_at FROM permissions p
INNER JOIN role_permissions rp ON p.id = rp.permission_id
WHERE rp.role_id = $1 
    AND rp.deleted_at IS NULL 
    AND p.deleted_at IS NULL
    AND p.is_active = TRUE
ORDER BY p.resource, p.action
`

func (q *Queries) GetRolePermissions(ctx context.Context, roleID uuid.UUID) ([]Permission, error) {
	rows, err := q.db.Query(ctx, getRolePermissions, roleID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Permission{}
	for rows.Next() {
		var i Permission
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.Resource,
			&i.Action,
			&i.IsActive,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getRoleWithPermissions = `-- name: GetRoleWithPermissions :one
SELECT 
    r.id,
    r.name,
    r.description,
    r.is_active,
    r.created_at,
    r.updated_at,
    COALESCE(
        JSON_AGG(
            JSON_BUILD_OBJECT(
                'id', p.id,
                'name', p.name,
                'description', p.description,
                'resource', p.resource,
                'action', p.action,
                'is_active', p.is_active
            )
        ) FILTER (WHERE p.id IS NOT NULL), 
        '[]'::json
    ) as permissions
FROM roles r
LEFT JOIN role_permissions rp ON r.id = rp.role_id AND rp.deleted_at IS NULL
LEFT JOIN permissions p ON rp.permission_id = p.id AND p.deleted_at IS NULL AND p.is_active = TRUE
WHERE r.id = $1 AND r.deleted_at IS NULL
GROUP BY r.id, r.name, r.description, r.is_active, r.created_at, r.updated_at
`

type GetRoleWithPermissionsRow struct {
	ID          uuid.UUID          `db:"id" json:"id"`
	Name        string             `db:"name" json:"name"`
	Description pgtype.Text        `db:"description" json:"description"`
	IsActive    bool               `db:"is_active" json:"is_active"`
	CreatedAt   pgtype.Timestamptz `db:"created_at" json:"created_at"`
	UpdatedAt   pgtype.Timestamptz `db:"updated_at" json:"updated_at"`
	Permissions interface{}        `db:"permissions" json:"permissions"`
}

func (q *Queries) GetRoleWithPermissions(ctx context.Context, id uuid.UUID) (GetRoleWithPermissionsRow, error) {
	row := q.db.QueryRow(ctx, getRoleWithPermissions, id)
	var i GetRoleWithPermissionsRow
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Permissions,
	)
	return i, err
}

const getRolesWithPermission = `-- name: GetRolesWithPermission :many
SELECT r.id, r.name, r.description, r.created_at, r.updated_at, r.deleted_at, r.is_active FROM roles r
INNER JOIN role_permissions rp ON r.id = rp.role_id
WHERE rp.permission_id = $1 
    AND rp.deleted_at IS NULL 
    AND r.deleted_at IS NULL
    AND r.is_active = TRUE
ORDER BY r.name
`

func (q *Queries) GetRolesWithPermission(ctx context.Context, permissionID uuid.UUID) ([]Role, error) {
	rows, err := q.db.Query(ctx, getRolesWithPermission, permissionID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Role{}
	for rows.Next() {
		var i Role
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.IsActive,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listRoles = `-- name: ListRoles :many
SELECT id, name, description, created_at, updated_at, deleted_at, is_active FROM roles 
WHERE deleted_at IS NULL
ORDER BY name
LIMIT $1 OFFSET $2
`

type ListRolesParams struct {
	Limit  int32 `db:"limit" json:"limit"`
	Offset int32 `db:"offset" json:"offset"`
}

func (q *Queries) ListRoles(ctx context.Context, arg ListRolesParams) ([]Role, error) {
	rows, err := q.db.Query(ctx, listRoles, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Role{}
	for rows.Next() {
		var i Role
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.IsActive,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const revokeRolePermission = `-- name: RevokeRolePermission :exec
UPDATE role_permissions 
SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
WHERE role_id = $1 AND permission_id = $2 AND deleted_at IS NULL
`

type RevokeRolePermissionParams struct {
	RoleID       uuid.UUID `db:"role_id" json:"role_id"`
	PermissionID uuid.UUID `db:"permission_id" json:"permission_id"`
}

func (q *Queries) RevokeRolePermission(ctx context.Context, arg RevokeRolePermissionParams) error {
	_, err := q.db.Exec(ctx, revokeRolePermission, arg.RoleID, arg.PermissionID)
	return err
}

const updateRole = `-- name: UpdateRole :one
UPDATE roles 
SET 
    name = $2,
    description = $3,
    is_active = $4,
    updated_at = $5
WHERE id = $1 AND deleted_at IS NULL
RETURNING id, name, description, created_at, updated_at, deleted_at, is_active
`

type UpdateRoleParams struct {
	ID          uuid.UUID          `db:"id" json:"id"`
	Name        string             `db:"name" json:"name"`
	Description pgtype.Text        `db:"description" json:"description"`
	IsActive    bool               `db:"is_active" json:"is_active"`
	UpdatedAt   pgtype.Timestamptz `db:"updated_at" json:"updated_at"`
}

func (q *Queries) UpdateRole(ctx context.Context, arg UpdateRoleParams) (Role, error) {
	row := q.db.QueryRow(ctx, updateRole,
		arg.ID,
		arg.Name,
		arg.Description,
		arg.IsActive,
		arg.UpdatedAt,
	)
	var i Role
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.IsActive,
	)
	return i, err
}

# Development Rules and Best Practices

This document outlines the development rules and best practices established for the Payment Gateway project to ensure consistency, maintainability, and reduce common issues encountered during development.

## Table of Contents

01. [Clean Architecture Principles](#clean-architecture-principles)
02. [Domain Layer Rules](#domain-layer-rules)
03. [Application Layer Rules](#application-layer-rules)
04. [Infrastructure Layer Rules](#infrastructure-layer-rules)
05. [Testing Standards](#testing-standards)
06. [Database and Migration Rules](#database-and-migration-rules)
07. [Configuration Management](#configuration-management)
08. [Error Handling](#error-handling)
09. [Logging Standards](#logging-standards)
10. [Code Organization](#code-organization)

## Clean Architecture Principles

### Dependency Direction Rules

* **STRICT ENFORCEMENT**: Dependencies must flow inward only: Infrastructure → Application → Domain
* Domain layer MUST NOT import from Application or Infrastructure layers
* Application layer MUST NOT import from Infrastructure layer
* Infrastructure layer CAN import from both Application and Domain layers
* Use dependency injection to invert dependencies when needed

### Interface Segregation

* Define interfaces in the layer that uses them (Application layer defines repository interfaces)
* Keep interfaces focused and minimal (single responsibility)
* Use interface segregation to avoid forcing implementations to depend on methods they don't use

## Domain Layer Rules

### Entity Creation Patterns

* **ALWAYS** use factory methods for entity creation:
  + `NewUser(email, passwordHash, name)` for new entities with auto-generated IDs
  + `NewUserWithID(id, email, passwordHash, name, isActive, createdAt, updatedAt)` for repository reconstruction
* **NEVER** expose entity fields directly - use getter methods
* **ALWAYS** validate input in factory methods and update methods

### Value Object Standards

* **ALWAYS** validate value objects at creation time
* Use factory methods like `NewEmail(email string) (Email, error)` with validation
* Make value objects immutable
* Implement proper String() methods for debugging

### Domain Events

* **ALWAYS** implement the `DomainEvent` interface for all domain events
* Use consistent event naming: `UserRegisteredEvent`,  `RoleAssignedEvent`
* Include all necessary context in event data
* Generate unique event IDs and timestamps

## Application Layer Rules

### Service Implementation

* **ALWAYS** validate input parameters at service boundaries
* Use domain entities and value objects, not primitive types
* Handle domain errors and convert to application-specific errors
* Keep services focused on orchestration, not business logic

### Repository Interface Design

* Define repository interfaces in the application layer
* Use domain entities as parameters and return types
* Keep repository methods focused and atomic
* Use context. Context for all repository methods

## Infrastructure Layer Rules

### Database Repository Implementation

* **ALWAYS** use proper error handling and context propagation
* Use database transactions for multi-step operations
* Convert between database models and domain entities using converter functions
* Handle database-specific errors and convert to domain errors

### Configuration Management

* **NEVER** manually edit package configuration files (package.json, go.mod, etc.)
* **ALWAYS** use package managers for dependency management
* Provide both connection strings and full URLs for different use cases:
  + `GetDatabaseDSN()` for application connections
  + `GetDatabaseURL()` for migration tools that require schemes

### Path Resolution

* **ALWAYS** implement dynamic path resolution for migrations and test data
* Use `go.mod` file location to find project root
* Never hardcode relative paths that depend on working directory

## Testing Standards

### Unit Testing Rules

* **ALWAYS** test domain entities using getter methods, not direct field access
* Use proper mock interfaces that match actual interface signatures
* Test both success and error cases
* Use table-driven tests for multiple scenarios

### Integration Testing Rules

* **ALWAYS** use testcontainers for database integration tests
* Implement proper container lifecycle management with singleton pattern
* Use seed data with predictable IDs for easy identification
* Load seed data in proper dependency order (permissions → roles → users)
* Use correct seed data names that match actual test data

### Test Infrastructure

* Implement centralized test setup with proper cleanup
* Use environment-specific configuration for containerized vs local testing
* Implement proper test isolation with database cleanup between tests
* Use standardized test user accounts with different role assignments

### Test Data Management

* **ALWAYS** verify seed data names match test expectations
* Use descriptive role names like "Regular User", "Administrator" instead of generic "user", "admin"
* Implement proper test data cleanup and container management
* Use structured test data with predictable patterns

## Database and Migration Rules

### Migration Management

* Use golang-migrate library with proper DSN format including scheme
* Implement dynamic migration path resolution
* Always provide both up and down migrations
* Use descriptive migration names with timestamps

### Schema Design

* Follow consistent naming conventions for tables and columns
* Use UUIDs for primary keys in domain entities
* Implement proper foreign key constraints
* Use appropriate indexes for query performance

## Configuration Management

### Environment Configuration

* Use structured configuration with validation
* Support environment variable overrides with consistent naming patterns
* Implement configuration validation at startup
* Provide sensible defaults for development environments

### Database Configuration

* Provide multiple DSN formats for different use cases
* Implement proper connection pooling settings
* Use environment-specific database names for testing

## Error Handling

### Error Propagation

* Use context-aware error handling throughout the application
* Wrap errors with meaningful context using fmt. Errorf
* Convert infrastructure errors to domain errors at boundaries
* Implement proper error logging with correlation IDs

### HTTP Error Responses

* Use standardized error response format
* Include correlation IDs for request tracing
* Implement proper status code mapping
* Log errors with appropriate detail levels

## Logging Standards

### Structured Logging

* Use structured logging with consistent field names
* Include correlation IDs and trace IDs in all log entries
* Use appropriate log levels (debug, info, warn, error)
* Log important business events and errors

### Context Propagation

* Always pass context. Context through the application layers
* Use context for correlation ID and trace ID propagation
* Implement proper context-aware logging

## Code Organization

### Package Structure

* Follow Clean Architecture layer separation
* Use clear package naming that reflects purpose
* Keep related functionality together
* Avoid circular dependencies

### Import Management

* Group imports logically (standard library, third-party, internal)
* Use meaningful aliases for conflicting package names
* Keep imports minimal and focused

### File Organization

* Keep files focused on single responsibilities
* Use descriptive file names that reflect content
* Organize test files alongside implementation files
* Use consistent naming patterns across the codebase

## Common Pitfalls to Avoid

01. **Direct Field Access**: Never access entity fields directly in tests or external code
02. **Wrong Factory Methods**: Use correct entity creation patterns (NewUser vs NewUserWithID)
03. **Hardcoded Paths**: Always implement dynamic path resolution for migrations and test data
04. **Missing Validation**: Always validate inputs at domain and application boundaries
05. **Incorrect Seed Data**: Verify seed data names match test expectations
06. **Manual Package Management**: Always use package managers instead of manual file editing
07. **Missing Error Context**: Always wrap errors with meaningful context
08. **Inconsistent Testing**: Follow established testing patterns for consistency

## Enforcement

These rules should be enforced through:
* Code reviews
* Automated testing (unit, integration, e2e)
* Linting and static analysis tools
* Documentation and training
* Regular architecture reviews

## Specific Implementation Patterns

### Domain Entity Patterns

```go
// ✅ CORRECT: Use factory methods with validation
user := account.NewUser(email, passwordHash, name)

// ✅ CORRECT: Use getter methods for field access
userID := user.ID()
userEmail := user.Email()

// ❌ WRONG: Direct field access
userID := user.id // This will cause compilation errors

// ✅ CORRECT: Repository reconstruction
user := account.NewUserWithID(id, email, passwordHash, name, isActive, createdAt, updatedAt)
```

### Repository Interface Patterns

```go
// ✅ CORRECT: Application layer defines interfaces
type UserRepository interface {
    Create(ctx context.Context, user *account.User) error
    GetByID(ctx context.Context, id account.UserID) (*account.User, error)
    GetByEmail(ctx context.Context, email account.Email) (*account.User, error)
}

// ✅ CORRECT: Infrastructure layer implements interfaces
type PostgreSQLUserRepository struct {
    db *database.DB
}

func (r *PostgreSQLUserRepository) Create(ctx context.Context, user *account.User) error {
    // Implementation using domain entities
}
```

### Testing Patterns

```go
// ✅ CORRECT: Integration test with proper setup
func TestUserRepository_Integration(t *testing.T) {
    if testing.Short() {
        t.Skip("Skipping integration tests in short mode")
    }

    ctx := context.Background()
    dbSetup := testingpkg.NewTestDatabaseSetup()
    defer dbSetup.Cleanup(ctx)

    // Use correct seed data names
    role, err := roleRepo.GetByName(ctx, "Regular User") // Not "user"
    require.NoError(t, err)
}

// ✅ CORRECT: Use getter methods in tests
assert.Equal(t, expectedEmail.String(), user.Email().String())
assert.Equal(t, expectedName, user.Name())

// ❌ WRONG: Direct field access in tests
assert.Equal(t, expectedEmail, user.email) // Compilation error
```

### Configuration Patterns

```go
// ✅ CORRECT: Provide multiple DSN formats
func (c *DatabaseConfig) GetDatabaseDSN() string {
    return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
        c.Host, c.Port, c.User, c.Password, c.Name, c.SSLMode)
}

func (c *DatabaseConfig) GetDatabaseURL() string {
    return fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=%s",
        c.User, c.Password, c.Host, c.Port, c.Name, c.SSLMode)
}
```

### Path Resolution Patterns

```go
// ✅ CORRECT: Dynamic path resolution
func findProjectRoot() (string, error) {
    currentDir, err := os.Getwd()
    if err != nil {
        return "", err
    }

    for {
        goModPath := filepath.Join(currentDir, "go.mod")
        if _, err := os.Stat(goModPath); err == nil {
            return currentDir, nil
        }

        parentDir := filepath.Dir(currentDir)
        if parentDir == currentDir {
            break
        }
        currentDir = parentDir
    }

    return "", fmt.Errorf("could not find project root")
}
```

## Troubleshooting Common Issues

### Compilation Errors

01. **"undefined field" errors**: Use getter methods instead of direct field access
02. **"cannot use" type errors**: Check entity creation patterns and value object usage
03. **Import cycle errors**: Review dependency direction and layer separation

### Test Failures

01. **"role not found" errors**: Verify seed data names match test expectations
02. **"no such file or directory" errors**: Implement dynamic path resolution
03. **Database connection errors**: Check DSN format and container setup

### Integration Test Issues

01. **Container startup failures**: Verify Docker is running and accessible
02. **Migration failures**: Check migration path resolution and DSN format
03. **Seed data loading failures**: Verify file paths and dependency order

## Performance Considerations

### Database Operations

* Use connection pooling with appropriate limits
* Implement proper indexing for frequently queried fields
* Use transactions for multi-step operations
* Consider read replicas for read-heavy operations

### Testing Performance

* Use testcontainers singleton pattern to avoid container recreation
* Implement proper test isolation without full database recreation
* Use parallel testing where appropriate
* Cache test containers across test runs

## Security Considerations

### Authentication and Authorization

* Always validate user permissions at service boundaries
* Use secure password hashing (bcrypt with appropriate cost)
* Implement proper session management
* Use correlation IDs for audit trails

### Data Protection

* Never log sensitive information (passwords, tokens)
* Use proper input validation and sanitization
* Implement rate limiting for API endpoints
* Use HTTPS in production environments

## Updates

This document should be updated whenever new patterns are established or existing rules are modified. All team members should be notified of changes and the reasoning behind them.

## Version History

* **v1.0** (2025-06-28): Initial version based on Clean Architecture refactoring experience
  + Established core principles and patterns
  + Documented common pitfalls and solutions
  + Added specific implementation examples

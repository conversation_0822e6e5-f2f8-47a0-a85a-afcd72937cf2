-- name: CreateRole :one
INSERT INTO roles (
    id, name, description, is_active, created_at, updated_at
) VALUES (
    $1, $2, $3, $4, $5, $6
) RETURNING *;

-- name: GetRoleByID :one
SELECT * FROM roles 
WHERE id = $1 AND deleted_at IS NULL;

-- name: GetRoleByName :one
SELECT * FROM roles 
WHERE name = $1 AND deleted_at IS NULL;

-- name: UpdateRole :one
UPDATE roles 
SET 
    name = $2,
    description = $3,
    is_active = $4,
    updated_at = $5
WHERE id = $1 AND deleted_at IS NULL
RETURNING *;

-- name: DeleteRole :exec
UPDATE roles 
SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND deleted_at IS NULL;

-- name: ListRoles :many
SELECT * FROM roles 
WHERE deleted_at IS NULL
ORDER BY name
LIMIT $1 OFFSET $2;

-- name: CountRoles :one
SELECT COUNT(*) FROM roles 
WHERE deleted_at IS NULL;

-- name: ExistsRoleByName :one
SELECT EXISTS(
    SELECT 1 FROM roles 
    WHERE name = $1 AND deleted_at IS NULL
);

-- name: GetRolePermissions :many
SELECT p.* FROM permissions p
INNER JOIN role_permissions rp ON p.id = rp.permission_id
WHERE rp.role_id = $1 
    AND rp.deleted_at IS NULL 
    AND p.deleted_at IS NULL
    AND p.is_active = TRUE
ORDER BY p.resource, p.action;

-- name: AssignRolePermission :exec
INSERT INTO role_permissions (id, role_id, permission_id, created_at, updated_at)
VALUES (gen_random_uuid(), $1, $2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- name: RevokeRolePermission :exec
UPDATE role_permissions 
SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
WHERE role_id = $1 AND permission_id = $2 AND deleted_at IS NULL;

-- name: GetRolesWithPermission :many
SELECT r.* FROM roles r
INNER JOIN role_permissions rp ON r.id = rp.role_id
WHERE rp.permission_id = $1 
    AND rp.deleted_at IS NULL 
    AND r.deleted_at IS NULL
    AND r.is_active = TRUE
ORDER BY r.name;

-- name: GetRoleWithPermissions :one
SELECT 
    r.id,
    r.name,
    r.description,
    r.is_active,
    r.created_at,
    r.updated_at,
    COALESCE(
        JSON_AGG(
            JSON_BUILD_OBJECT(
                'id', p.id,
                'name', p.name,
                'description', p.description,
                'resource', p.resource,
                'action', p.action,
                'is_active', p.is_active
            )
        ) FILTER (WHERE p.id IS NOT NULL), 
        '[]'::json
    ) as permissions
FROM roles r
LEFT JOIN role_permissions rp ON r.id = rp.role_id AND rp.deleted_at IS NULL
LEFT JOIN permissions p ON rp.permission_id = p.id AND p.deleted_at IS NULL AND p.is_active = TRUE
WHERE r.id = $1 AND r.deleted_at IS NULL
GROUP BY r.id, r.name, r.description, r.is_active, r.created_at, r.updated_at;

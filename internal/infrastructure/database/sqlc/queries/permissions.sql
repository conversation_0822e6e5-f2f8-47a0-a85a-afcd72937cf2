-- name: CreatePermission :one
INSERT INTO permissions (
    id, name, description, resource, action, is_active, created_at, updated_at
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8
) RETURNING *;

-- name: GetPermissionByID :one
SELECT * FROM permissions 
WHERE id = $1 AND deleted_at IS NULL;

-- name: GetPermissionByName :one
SELECT * FROM permissions 
WHERE name = $1 AND deleted_at IS NULL;

-- name: UpdatePermission :one
UPDATE permissions 
SET 
    name = $2,
    description = $3,
    resource = $4,
    action = $5,
    is_active = $6,
    updated_at = $7
WHERE id = $1 AND deleted_at IS NULL
RETURNING *;

-- name: DeletePermission :exec
UPDATE permissions 
SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND deleted_at IS NULL;

-- name: ListPermissions :many
SELECT * FROM permissions 
WHERE deleted_at IS NULL
ORDER BY resource, action
LIMIT $1 OFFSET $2;

-- name: CountPermissions :one
SELECT COUNT(*) FROM permissions 
WHERE deleted_at IS NULL;

-- name: ExistsPermissionByName :one
SELECT EXISTS(
    SELECT 1 FROM permissions 
    WHERE name = $1 AND deleted_at IS NULL
);

-- name: GetPermissionsByResource :many
SELECT * FROM permissions 
WHERE resource = $1 AND deleted_at IS NULL
ORDER BY action;

-- name: GetPermissionByResourceAndAction :one
SELECT * FROM permissions 
WHERE resource = $1 AND action = $2 AND deleted_at IS NULL;

# Phase 8: Final Integration & Deployment Preparation

## 1. Overview and Goals

This document outlines the eighth and final phase of refactoring the `payment-service` . This phase focuses on integrating all previously developed bounded contexts, ensuring system-wide functionality, performance, and security, and preparing the application for deployment.

**Goal for Phase 8:** To achieve a fully integrated, stable, and deployable `payment-service` that adheres to Clean Architecture and Domain-Driven Design principles, ready for production environments.

**Objectives for Phase 8:**
1.  Integrate all bounded contexts (`account`, `organization`, `configuration`, `billing`, `notification`, `reconciliation`) into a cohesive system.
2.  Implement cross-cutting concerns (e.g., API Gateway, centralized error handling, logging, tracing).
3.  Conduct comprehensive system-wide testing (integration, end-to-end, performance, security).
4.  Finalize deployment configurations and scripts.
5.  Update all relevant documentation.
6.  Ensure all implementations adhere to the defined coding guidelines.

**Scope for Phase 8:**
*   Review and refine inter-context communication (Domain Events, API calls).
*   Implement API Gateway functionalities (e.g., routing, authentication/authorization enforcement, rate limiting).
*   Centralize and standardize error handling across all contexts.
*   Implement distributed tracing and enhanced logging.
*   Performance testing and optimization.
*   Security audits and vulnerability assessments.
*   Containerization (Docker) and orchestration (Kubernetes readiness).
*   CI/CD pipeline integration and automation.
*   Final review of all documentation (code, architecture, API).
*   Creating comprehensive system-wide E2E tests.

**Out of Scope for Phase 8:**
*   Actual production deployment (this phase prepares for it).
*   Advanced monitoring and alerting setup (beyond basic metrics collection).
*   Detailed infrastructure provisioning (e.g., cloud-specific IaC).

## 2. Architectural Principles (Recap from Previous Phases)

This refactoring effort formally adopts and enforces **Clean Architecture** and **Domain-Driven Design (DDD)** principles to create clear boundaries between different layers of the application and promote scalability through Bounded Contexts.

### 2.1. The Dependency Rule (Clean Architecture)

Dependencies must only point inwards: `Infrastructure` -> `Application` -> `Domain` .
*   **Domain Layer**: Contains business entities and rules. It has zero dependencies on any outer layer.
*   **Application Layer**: Contains use cases that orchestrate the business logic. It defines interfaces that the Infrastructure layer implements.
*   **Infrastructure Layer**: Contains all external-facing components (database, payment providers, etc.). It depends on the Application layer.

```mermaid
graph TD
    subgraph "Domain Layer"
        direction LR
        D_Entities[Entities]
        D_Interfaces[Repository Interfaces]
    end

    subgraph "Application Layer"
        direction LR
        A_UseCases[Use Cases] --> D_Interfaces
        A_Interfaces[Provider Interfaces]
    end

    subgraph "Infrastructure Layer"
        direction LR
        I_Handlers[HTTP Handlers] --> A_UseCases
        I_Adapters[Provider Adapters] -- implements --> A_Interfaces
        I_Repos[Repository Implementations] -- implements --> D_Interfaces
    end

    I_Handlers --> A_UseCases
    A_UseCases --> D_Entities
    I_Repos --> D_Entities
    I_Adapters -- implements --> A_Interfaces
    A_UseCases -- depends on --> A_Interfaces
```

### 2.2. Bounded Contexts (Domain-Driven Design)

This phase focuses on the integration of all bounded contexts:

*   **`account`**: Manages identity and access control for internal system users.
*   **`organization`**: Manages the external parties involved in transactions.
*   **`billing`**: The core transactional context that handles the entire payment lifecycle.
*   **`configuration`**: Manages the "how" of payments, including providers and channels.
*   **`notification`**: Responsible for all outgoing communications.
*   **`reconciliation`**: Responsible for matching internal transaction records with external financial data.

### 2.3. Presentation Layer: Ports & Adapters

The presentation layer will adopt a Ports and Adapters approach, living in `/internal/ports` and responsible for translating external requests into calls to the application layer.

*   **`/ports/rest/`**: Contains all HTTP handlers, separated by their "persona" or purpose.
    -   **`openapi/`**: For external client APIs.
    -   **`dashboard/`**: For the internal admin panel API.
    -   **`callback/`**: For incoming webhooks from third parties.
*   **Shared Use Cases**: All handlers will call the same use cases in the `/internal/app` layer, ensuring business logic is centralized and consistently applied.

### 2.4. Target Directory Structure

The new directory structure reflects the DDD approach and Clean Architecture principles. This phase will ensure all directories are correctly populated and integrated.

```
/payment-service/
├── cmd/
│   └── api/
│       ├── server/                # Server setup, DI, and routing
│       │   ├── server.go
│       │   ├── dependencies.go
│       │   └── routes.go
│       └── main.go                # Lean application entry point
├── internal/
│   ├── app/                       # Application layer (use cases, services)
│   │   ├── account/
│   │   ├── organization/
│   │   ├── billing/
│   │   ├── configuration/
│   │   ├── notification/
│   │   ├── reconciliation/
│   │   └── ...
│   ├── domain/                    # Domain layer (entities, value objects, core business logic)
│   │   ├── account/
│   │   ├── organization/
│   │   ├── billing/
│   │   ├── configuration/
│   │   ├── notification/
│   │   ├── reconciliation/
│   │   └── ...
│   ├── ports/                     # Presentation Layer (Handlers)
│   │   └── rest/
│   │       ├── openapi/
│   │       ├── dashboard/
│   │       ├── callback/
│   │       └── ...
│   └── infrastructure/            # Infrastructure layer (external concerns)
│       ├── database/              # PostgreSQL repository implementations
│       ├── provider/              # Adapters for the gateway packages
│       ├── cache/                 # Redis implementation
│       ├── eventbus/              # Domain Event Bus implementation
│       └── locking/               # Distributed locking (e.g., Redis-based)
├── pkg/
│   ├── gateways/                  # External, reusable gateway packages
│   │   ├── xendit/
│   │   │   ├── client.go
│   │   │   ├── go.mod
│   │   └── nicepay/
│   │       ├── client.go
│   │       └── go.mod
│   └── ... (other shared libraries)
├── api/                           # API definitions (e.g., OpenAPI/Swagger specs)
├── db/
│   └── migrations/                # Database migration .sql files
├── docs/                          # Project documentation
└── scripts/                       # Scripts for build, deployment, etc.
```

## 3. Key Architectural Decisions (Recap from Previous Phases)

### 3.1. Manual Dependency Injection

To maintain full control over the application's startup process and avoid external dependencies, **manual dependency injection** will be used. All dependencies will be constructed and "wired" together in the `/cmd/api/server` package.

### 3.2. Externalized Gateway Packages

All third-party payment provider integrations (e.g., Xendit, NicePay) will be built as **standalone, reusable Go packages** located in `/pkg/gateways` .

### 3.3. Internationalization (i18n)

The application will support multiple languages, starting with English (en) and Indonesian (id). This will impact user-facing strings in API responses, validation messages, and notifications.

### 3.4. Domain Event Mechanism

The in-process domain event mechanism established in Phase 2 will be utilized for inter-context communication. This phase will ensure all necessary event publishers and subscribers across contexts are correctly wired and functional.

### 3.5. Centralized Fee Calculation

A dedicated fee calculation service will reside within the `billing` bounded context.

### 3.6. Event Sourcing for Transaction Status

Transaction status management will leverage an Event Sourcing pattern, as detailed in the comprehensive [ `docs/event_sourcing_strategy.md` ](docs/event_sourcing_strategy.md) document.

### 3.7. Reliable Third-Party Notifications (Outbox Pattern)

The Outbox pattern, implemented in Phase 5 within the `billing` context, will ensure that events triggering notifications are reliably stored. The `notification` context will then be responsible for processing these `outbox` entries and sending the actual notifications.

## 4. Standardized Libraries (Recap from Phase 1)

The following libraries will be standardized across the project:

*   **Database Driver:** `pgx/v5`
*   **SQL Code Generator:** `sqlc`
*   **HTTP Framework:** `Gin`
*   **Logger:** `Zerolog`
*   **Configuration:** `Koanf`
*   **HTTP Client:** `Resty v2`
*   **Message Broker:** `go-rabbitmq`
*   **Integration Testing:** `testcontainers-go`
*   **Database Migrations:** `golang-migrate/migrate`
*   **Error Reporting:** `sentry-go`
*   **Internationalization:** `go-i18n`

## 5. Coding Conventions & Guidelines (Recap from Phase 1)

Adherence to these guidelines is mandatory for all contributors to ensure a high-quality, consistent, and maintainable codebase.

### 5.1. General Principles & Idiomatic Go

*   **Rule 1.1 (Simplicity - KISS):** Prefer simple, clear solutions over complex ones.
*   **Rule 1.2 (Effective Go):** Follow the principles outlined in the official [Effective Go](https://go.dev/doc/effective_go) document.
*   **Rule 1.3 (Package Naming):** All package names MUST be short-lowercase.
*   **Rule 1.4 (Variable Naming):** All variable names MUST be `camelCase`.
*   **Rule 1.5 (Interfaces):** Define interfaces to decouple components.
*   **Rule 1.6 (Formatting):** All generated Go code MUST be formatted with `gofmt`.

### 5.2. Project Structure & Clean Architecture

*   **Rule 2.1 (Strict Layering):** The dependency direction `API` -> `Application` -> `Domain` MUST be strictly enforced.
*   **Rule 2.2 (Domain Purity):** The `internal/domain` package MUST NOT import any other project packages.
*   **Rule 2.3 (Thin Handlers):** API handlers (`internal/ports/rest`) MUST only parse requests, call a single application service method, and generate a standardized response.

### 5.3. Error Management Protocol

*   **Rule 3.1 (Error Wrapping):** Errors MUST be wrapped with context using `fmt.Errorf("package.function: %w", err)`.
*   **Rule 3.2 (Custom Domain Errors):** Use custom error variables for predictable business rule failures.
*   **Rule 3.3 (Centralized HTTP Error Mapping):** A dedicated Gin error-handling middleware is the only place where errors are translated into HTTP status codes.
*   **Rule 3.4 (Sentry for Unexpected Errors):** Use `webapi.ErrorWithSentry(c, err)` for all 5xx internal server errors.

### 5.4. Standardized API Responses (webapi package)

*   **Rule 4.1 (Mandatory Helpers):** All HTTP responses MUST be generated exclusively using helper functions from the `internal/ports/rest/webapi` package.
*   **Rule 4.2 (Success Structure):** The mandatory JSON structure for a successful response is: `{"status": "success", "data": { /* Actual response data */ }, "correlationId": "UUID_OF_REQUEST"}`.
*   **Rule 4.3 (Error Structure):** The mandatory JSON structure for an error response is: `{"status": "error", "error": {"code": "UNIQUE_ERROR_CODE", "message": "Human-readable error description."}, "correlationId": "UUID_OF_REQUEST"}`.
*   **Rule 4.4 (Helper Signatures and Usage):** Adhere to the defined signatures for `webapi.Success`,   `webapi.SuccessPaginated`,   `webapi.Error`, and `webapi.ErrorWithSentry`.

### 5.5. Logging Best Practices (zerolog)

*   **Rule 5.1 (Structured Logging):** All logging MUST be done using the global `zerolog` instance.
*   **Rule 5.2 (Contextual Logging):** Every log statement MUST include the `trace_id` from the context.
*   **Rule 5.3 (Error Logging):** When logging an error, the error object MUST be included using `.Err(err)`.

### 5.6. Concurrency and Graceful Shutdown

*   **Rule 6.1 (Panic Safety):** Any new goroutine MUST use a `defer...recover()` block, logging any recovered panic to Sentry.
*   **Rule 6.2 (Graceful Termination):** Any long-running goroutine MUST accept a `context.Context` and listen on `ctx.Done()`.
*   **Rule 6.3 (Server Shutdown):** `main.go` must implement graceful shutdown for the HTTP server.

### 5.7. Gin Middleware

*   **Rule 7.1 (Tracing Middleware):** MUST be the first middleware, generating a `trace_id`.
*   **Rule 7.2 (Logger Middleware):** MUST log request and response details.
*   **Rule 7.3 (Error Middleware):** MUST be the last middleware to centralize error handling.

### 5.8. Database Access (sqlc with pgx/v5)

*   **Rule 8.1 (SQL Files):** All SQL queries MUST be defined in `.sql` files.
*   **Rule 8.2 (sqlc.yaml Configuration):** The `sqlc.yaml` file MUST be configured to use `sql_package: "pgx/v5"` and appropriate type overrides.
*   **Rule 8.3 (Type Conversion):** All conversions between domain types and `pgtype` MUST be handled in a dedicated `internal/infrastructure/database/converters` package.
*   **Rule 8.4 (Transactions):** Repository methods that perform writes MUST accept `pgx.Tx` as a parameter.

### 5.9. Testing and CI/CD Strategy

*   **Rule 9.1 (Containerized Testing):** All integration and end-to-end tests that require external services (like a database or Redis) MUST use `testcontainers-go`.
*   **Rule 9.2 (Seeding Data for Tests):** For every new `.up.sql` migration file, a corresponding test data seed file MUST be created.
*   **Rule 9.3 (Test Coverage):** API handlers, application services, and repositories MUST have dedicated test files.
*   **Rule 9.4 (CI/CD Gate):** A pull request MUST NOT be allowed to merge into the main branch unless all tests (`go test ./...`) pass.

### 5.10. Internationalization (i18n)

*   **Rule 10.1 (Localization Support):** The application MUST support English (en) and Indonesian (id) locales.

## 6. Database Schemas for Phase 8

This phase will not introduce new bounded context-specific schemas but will focus on ensuring all existing schemas are correctly integrated and optimized for system-wide operations. It will also include any necessary audit or cross-cutting concern related tables.

### 6.1. Audit and Logging Schemas

#### `trx_request_logs` Table:

```sql
CREATE TABLE trx_request_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    resource_type VARCHAR(255), -- e.g., 'cash_in', 'cash_out'
    resource_id UUID, -- e.g., invoice number, batch number (changed to UUID)
    correlation_id VARCHAR(255),
    request_headers TEXT,
    request_method VARCHAR(10),
    request_path TEXT,
    request_query TEXT,
    request_body TEXT,
    response_status VARCHAR(10),
    response_body TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

*   **`resource_id`**: Changed to `UUID` to align with other tables.

## 7. Detailed Tasks for Phase 8

### 7.1. System Integration & Cross-Cutting Concerns

*   **Task 7.1.1: Review and Refine Inter-Context Communication**
    -   Verify all domain event publishers and subscribers are correctly wired and functional across all contexts.
    -   Ensure proper error handling and logging for inter-context communication.
*   **Task 7.1.2: Implement API Gateway Functionalities**
    -   Review and consolidate all API routes in `cmd/api/server/routes.go`.
    -   Implement centralized authentication and authorization enforcement for all API endpoints.
    -   Consider implementing rate limiting middleware.
*   **Task 7.1.3: Enhance Logging and Tracing**
    -   Ensure `correlation_id` is propagated consistently across all layers and contexts.
    -   Implement structured logging for all critical operations and errors.
    -   Integrate with a distributed tracing system (e.g., OpenTelemetry, if not already covered by Sentry's tracing capabilities).
*   **Task 7.1.4: Centralize Error Handling**
    -   Review and consolidate error mapping in `internal/ports/rest/middleware/error.go` to cover all possible domain and application errors from all contexts.
    -   Ensure consistent error responses across the entire API.

### 7.2. System-Wide Testing

*   **Task 7.2.1: Implement Comprehensive Integration Tests**
    -   Write integration tests that span multiple bounded contexts to verify end-to-end flows (e.g., user registration -> company creation -> payment configuration -> cash-in transaction -> notification).
*   **Task 7.2.2: Implement System-Wide End-to-End Tests**
    -   Develop a suite of E2E tests covering critical business processes from the client's perspective.
    -   Utilize `SetupTestServer()` for a consistent testing environment.
*   **Task 7.2.3: Conduct Performance Testing**
    -   Perform load and stress testing on key API endpoints and critical business flows.
    -   Identify and address performance bottlenecks.
*   **Task 7.2.4: Conduct Security Audits**
    -   Perform vulnerability scanning and penetration testing.
    -   Review authentication, authorization, and data handling for security flaws.

### 7.3. Deployment Preparation

*   **Task 7.3.1: Finalize Dockerization**
    -   Ensure `Dockerfile` is optimized for production (multi-stage builds, small base images).
    -   Verify `docker-compose.yml` is suitable for local development and testing.
*   **Task 7.3.2: Prepare for Orchestration**
    -   If targeting Kubernetes, create initial Kubernetes deployment manifests (Deployment, Service, Ingress).
*   **Task 7.3.3: Update CI/CD Pipeline**
    -   Automate build, test, and container image creation processes.
    -   Integrate security scanning tools into the pipeline.
*   **Task 7.3.4: Final Documentation Review**
    -   Review all phase documents (`docs/phases/`).
    -   Ensure API documentation (Postman collections) is complete and accurate.
    -   Update `README.md` with deployment instructions and system overview.

## 8. Phase 8 Deliverables

*   Fully integrated `payment-service` with all bounded contexts communicating seamlessly.
*   Robust cross-cutting concerns implemented (API Gateway, centralized error handling, distributed tracing, enhanced logging).
*   Comprehensive system-wide test suite (integration, E2E, performance, security).
*   Production-ready Docker images and initial orchestration manifests.
*   Automated CI/CD pipeline for build and test.
*   Complete and up-to-date documentation for the entire system.
*   All code adhering to the defined coding guidelines.

## 9. Phase 8 Diagram:

```mermaid
graph TD

    subgraph "Phase 8: Final Integration & Deployment Preparation"
        A[7.1. System Integration & Cross-Cutting Concerns] --> B(7.2. System-Wide Testing)
        B --> C(7.3. Deployment Preparation)
        A --> D
        B --> D
        C --> D
    end

    subgraph "7.1. System Integration & Cross-Cutting Concerns"
        A1[7.1.1: Review and Refine Inter-Context Communication]
        A2[7.1.2: Implement API Gateway Functionalities]
        A3[7.1.3: Enhance Logging and Tracing]
        A4[7.1.4: Centralize Error Handling]
    end

    subgraph "7.2. System-Wide Testing"
        B1[7.2.1: Implement Comprehensive Integration Tests]
        B2[7.2.2: Implement System-Wide End-to-End Tests]
        B3[7.2.3: Conduct Performance Testing]
        B4[7.2.4: Conduct Security Audits]
    end

    subgraph "7.3. Deployment Preparation"
        C1[7.3.1: Finalize Dockerization]
        C2[7.3.2: Prepare for Orchestration]
        C3[7.3.3: Update CI/CD Pipeline]
        C4[7.3.4: Final Documentation Review]
    end

    style A1 fill:#f9f,stroke:#333,stroke-width:2px
    style A2 fill:#f9f,stroke:#333,stroke-width:2px
    style A3 fill:#f9f,stroke:#333,stroke-width:2px
    style A4 fill:#f9f,stroke:#333,stroke-width:2px

    style B1 fill:#bbf,stroke:#333,stroke-width:2px
    style B2 fill:#bbf,stroke:#333,stroke-width:2px
    style B3 fill:#bbf,stroke:#333,stroke-width:2px
    style B4 fill:#bbf,stroke:#333,stroke-width:2px

    style C1 fill:#ccf,stroke:#333,stroke-width:2px
    style C2 fill:#ccf,stroke:#333,stroke-width:2px
    style C3 fill:#ccf,stroke:#333,stroke-width:2px
    style C4 fill:#ccf,stroke:#333,stroke-width:2px

    style D1 fill:#ddf,stroke:#333,stroke-width:2px
    style D2 fill:#ddf,stroke:#333,stroke-width:2px
    style D3 fill:#ddf,stroke:#333,stroke-width:2px
    style D4 fill:#ddf,stroke:#333,stroke-width:2px

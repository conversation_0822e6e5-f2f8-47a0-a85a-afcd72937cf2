# Phase 6: Notification Bounded Context Implementation

## 1. Overview and Goals

This document outlines the sixth phase of refactoring the `payment-service`, focusing on the implementation of the `notification` bounded context. This phase builds directly on the foundational infrastructure established in Phase 1 and the domain event mechanism from Phase 2.

**Goal for Phase 6:** To implement a secure, robust, and Clean Architecture-compliant `notification` bounded context, responsible for reliably delivering outgoing communications (webhooks and emails) to external systems.

**Objectives for Phase 6:**
1.  Implement core functionalities for publishing and processing outgoing notifications.
2.  Establish reliable delivery mechanisms for webhooks and emails, including retry logic.
3.  Centralize notification configurations.
4.  Ensure all implementations adhere to the defined coding guidelines and leverage the infrastructure from Phase 1 and domain events from Phase 2.
5.  Develop comprehensive unit, integration, and end-to-end tests for the `notification` context.

**Scope for Phase 6:**
*   Refactoring `modules/configuration/email_log` and `email_template` functionality into `internal/app/notification` and `internal/domain/notification`.
*   Updating database schemas for `outgoing_notifications`, `notification_delivery_logs`, and `notification_configs` to use UUIDs.
*   Implementing `NotificationService` for publishing notifications.
*   Implementing `WebhookDeliveryService` for handling webhook delivery, including retry logic.
*   Implementing `EmailDeliveryService` for handling email sending.
*   Integrating with the Outbox pattern (from Phase 5) for reliable notification publishing.
*   Subscribing to relevant domain events from other contexts (e.g., `CashInPaidEvent`, `CashOutDisbursedEvent`, `UserRegisteredEvent`).
*   Developing API endpoints for managing notification configurations and viewing delivery logs.
*   Creating initial unit, integration, and E2E tests for the `notification` context.

**Out of Scope for Phase 6:**
*   Complex templating engines for emails beyond basic string replacement.
*   Real-time push notifications (e.g., WebSockets).
*   SMS notifications.
*   Any other bounded contexts not directly related to outgoing communications.

## 2. Architectural Principles (Recap from Phase 1, 2, 3, 4 & 5)

This refactoring effort formally adopts and enforces **Clean Architecture** and **Domain-Driven Design (DDD)** principles to create clear boundaries between different layers of the application and promote scalability through Bounded Contexts.

### 2.1. The Dependency Rule (Clean Architecture)

Dependencies must only point inwards: `Infrastructure` -> `Application` -> `Domain`.
*   **Domain Layer**: Contains business entities and rules. It has zero dependencies on any outer layer.
*   **Application Layer**: Contains use cases that orchestrate the business logic. It defines interfaces that the Infrastructure layer implements.
*   **Infrastructure Layer**: Contains all external-facing components (database, payment providers, etc.). It depends on the Application layer.

```mermaid
graph TD
    subgraph "Domain Layer"
        direction LR
        D_Entities[Entities]
        D_Interfaces[Repository Interfaces]
    end

    subgraph "Application Layer"
        direction LR
        A_UseCases[Use Cases] --> D_Interfaces
        A_Interfaces[Provider Interfaces]
    end

    subgraph "Infrastructure Layer"
        direction LR
        I_Handlers[HTTP Handlers] --> A_UseCases
        I_Adapters[Provider Adapters] -- implements --> A_Interfaces
        I_Repos[Repository Implementations] -- implements --> D_Interfaces
    end

    I_Handlers --> A_UseCases
    A_UseCases --> D_Entities
    I_Repos --> D_Entities
    I_Adapters -- implements --> A_Interfaces
    A_UseCases -- depends on --> A_Interfaces
```

### 2.2. Bounded Contexts (Domain-Driven Design)

The `notification` bounded context is the focus of this phase.

*   **`account`**: Manages identity and access control for internal system users (admins, support, etc.).
*   **`organization`**: Manages the external parties involved in transactions (Companies, Partners, Products).
*   **`billing`**: The core transactional context that handles the entire payment lifecycle (Transactions, TransactionItems, TransactionHistory).
*   **`configuration`**: Manages the "how" of payments, including providers and channels (PaymentProvider, PaymentChannel, ProviderChannelMapping).
*   **`notification`**: Responsible for all outgoing communications (e.g., webhooks, emails).
*   **`reconciliation`**: Responsible for matching internal transaction records with external financial data.

### 2.3. Presentation Layer: Ports & Adapters

The presentation layer will adopt a Ports and Adapters approach, living in `/internal/ports` and responsible for translating external requests into calls to the application layer.

*   **`/ports/rest/`**: Contains all HTTP handlers, separated by their "persona" or purpose.
    *   **`openapi/`**: For external client APIs.
    *   **`dashboard/`**: For the internal admin panel API.
    *   **`callback/`**: For incoming webhooks from third parties.
*   **Shared Use Cases**: All handlers will call the same use cases in the `/internal/app` layer, ensuring business logic is centralized and consistently applied.

### 2.4. Target Directory Structure

The new directory structure reflects the DDD approach and Clean Architecture principles. This phase will populate the `internal/app/notification` and `internal/domain/notification` directories.

```
/payment-service/
├── cmd/
│   └── api/
│       ├── server/                # Server setup, DI, and routing
│       │   ├── server.go
│       │   ├── dependencies.go
│       │   └── routes.go
│       └── main.go                # Lean application entry point
├── internal/
│   ├── app/                       # Application layer (use cases, services)
│   │   ├── account/
│   │   ├── organization/
│   │   ├── billing/
│   │   ├── configuration/
│   │   ├── notification/          # <-- Populated in this phase
│   │   └── ...
│   ├── domain/                    # Domain layer (entities, value objects, core business logic)
│   │   ├── account/
│   │   ├── organization/
│   │   ├── billing/
│   │   ├── configuration/
│   │   ├── notification/          # <-- Populated in this phase
│   │   └── ...
│   ├── ports/                     # Presentation Layer (Handlers)
│   │   └── rest/
│   │       ├── openapi/
│   │       ├── dashboard/
│   │       ├── callback/
│   │       └── ...
│   └── infrastructure/            # Infrastructure layer (external concerns)
│       ├── database/              # PostgreSQL repository implementations
│       ├── provider/              # Adapters for the gateway packages
│       ├── cache/                 # Redis implementation
│       ├── eventbus/              # Domain Event Bus implementation
│       └── locking/               # Distributed locking (e.g., Redis-based)
├── pkg/
│   ├── gateways/                  # External, reusable gateway packages
│   │   ├── xendit/
│   │   │   ├── client.go
│   │   │   ├── go.mod
│   │   └── nicepay/
│   │       ├── client.go
│   │       └── go.mod
│   └── ... (other shared libraries)
├── api/                           # API definitions (e.g., OpenAPI/Swagger specs)
├── db/
│   └── migrations/                # Database migration .sql files
├── docs/                          # Project documentation
└── scripts/                       # Scripts for build, deployment, etc.
```

## 3. Key Architectural Decisions (Recap from Phase 1, 2, 3, 4 & 5)

### 3.1. Manual Dependency Injection

To maintain full control over the application's startup process and avoid external dependencies, **manual dependency injection** will be used. All dependencies will be constructed and "wired" together in the `/cmd/api/server` package.

### 3.2. Externalized Gateway Packages

All third-party payment provider integrations (e.g., Xendit, NicePay) will be built as **standalone, reusable Go packages** located in `/pkg/gateways`.

### 3.3. Internationalization (i18n)

The application will support multiple languages, starting with English (en) and Indonesian (id). This will impact user-facing strings in API responses, validation messages, and notifications.

### 3.4. Domain Event Mechanism

The in-process domain event mechanism established in Phase 2 will be utilized for inter-context communication. The `notification` context will primarily **subscribe** to events published by other contexts (e.g., `CashInPaidEvent`, `CashOutDisbursedEvent`, `UserRegisteredEvent`) to trigger notifications.

### 3.5. Centralized Fee Calculation

A dedicated fee calculation service will reside within the `billing` bounded context.

### 3.6. Event Sourcing for Transaction Status

Transaction status management will leverage an Event Sourcing pattern, as detailed in the comprehensive [`docs/event_sourcing_strategy.md`](docs/event_sourcing_strategy.md) document.

### 3.7. Reliable Third-Party Notifications (Outbox Pattern)

The Outbox pattern, implemented in Phase 5 within the `billing` context, will ensure that events triggering notifications are reliably stored. The `notification` context will then be responsible for processing these `outbox` entries and sending the actual notifications.

## 4. Standardized Libraries (Recap from Phase 1)

The following libraries will be standardized across the project:

*   **Database Driver:** `pgx/v5`
*   **SQL Code Generator:** `sqlc`
*   **HTTP Framework:** `Gin`
*   **Logger:** `Zerolog`
*   **Configuration:** `Koanf`
*   **HTTP Client:** `Resty v2`
*   **Message Broker:** `go-rabbitmq`
*   **Integration Testing:** `testcontainers-go`
*   **Database Migrations:** `golang-migrate/migrate`
*   **Error Reporting:** `sentry-go`
*   **Internationalization:** `go-i18n`

## 5. Coding Conventions & Guidelines (Recap from Phase 1)

Adherence to these guidelines is mandatory for all contributors to ensure a high-quality, consistent, and maintainable codebase.

### 5.1. General Principles & Idiomatic Go
*   **Rule 1.1 (Simplicity - KISS):** Prefer simple, clear solutions over complex ones.
*   **Rule 1.2 (Effective Go):** Follow the principles outlined in the official [Effective Go](https://go.dev/doc/effective_go) document.
*   **Rule 1.3 (Package Naming):** All package names MUST be short-lowercase.
*   **Rule 1.4 (Variable Naming):** All variable names MUST be `camelCase`.
*   **Rule 1.5 (Interfaces):** Define interfaces to decouple components.
*   **Rule 1.6 (Formatting):** All generated Go code MUST be formatted with `gofmt`.

### 5.2. Project Structure & Clean Architecture
*   **Rule 2.1 (Strict Layering):** The dependency direction `API` -> `Application` -> `Domain` MUST be strictly enforced.
*   **Rule 2.2 (Domain Purity):** The `internal/domain` package MUST NOT import any other project packages.
*   **Rule 2.3 (Thin Handlers):** API handlers (`internal/ports/rest`) MUST only parse requests, call a single application service method, and generate a standardized response.

### 5.3. Error Management Protocol
*   **Rule 3.1 (Error Wrapping):** Errors MUST be wrapped with context using `fmt.Errorf("package.function: %w", err)`.
*   **Rule 3.2 (Custom Domain Errors):** Use custom error variables for predictable business rule failures.
*   **Rule 3.3 (Centralized HTTP Error Mapping):** A dedicated Gin error-handling middleware is the only place where errors are translated into HTTP status codes.
*   **Rule 3.4 (Sentry for Unexpected Errors):** Use `webapi.ErrorWithSentry(c, err)` for all 5xx internal server errors.

### 5.4. Standardized API Responses (webapi package)
*   **Rule 4.1 (Mandatory Helpers):** All HTTP responses MUST be generated exclusively using helper functions from the `internal/ports/rest/webapi` package.
*   **Rule 4.2 (Success Structure):** The mandatory JSON structure for a successful response is: `{"status": "success", "data": { /* Actual response data */ }, "correlationId": "UUID_OF_REQUEST"}`.
*   **Rule 4.3 (Error Structure):** The mandatory JSON structure for an error response is: `{"status": "error", "error": {"code": "UNIQUE_ERROR_CODE", "message": "Human-readable error description."}, "correlationId": "UUID_OF_REQUEST"}`.
*   **Rule 4.4 (Helper Signatures and Usage):** Adhere to the defined signatures for `webapi.Success`, `webapi.SuccessPaginated`, `webapi.Error`, and `webapi.ErrorWithSentry`.

### 5.5. Logging Best Practices (zerolog)
*   **Rule 5.1 (Structured Logging):** All logging MUST be done using the global `zerolog` instance.
*   **Rule 5.2 (Contextual Logging):** Every log statement MUST include the `trace_id` from the context.
*   **Rule 5.3 (Error Logging):** When logging an error, the error object MUST be included using `.Err(err)`.

### 5.6. Concurrency and Graceful Shutdown
*   **Rule 6.1 (Panic Safety):** Any new goroutine MUST use a `defer...recover()` block, logging any recovered panic to Sentry.
*   **Rule 6.2 (Graceful Termination):** Any long-running goroutine MUST accept a `context.Context` and listen on `ctx.Done()`.
*   **Rule 6.3 (Server Shutdown):** `main.go` must implement graceful shutdown for the HTTP server.

### 5.7. Gin Middleware
*   **Rule 7.1 (Tracing Middleware):** MUST be the first middleware, generating a `trace_id`.
*   **Rule 7.2 (Logger Middleware):** MUST log request and response details.
*   **Rule 7.3 (Error Middleware):** MUST be the last middleware to centralize error handling.

### 5.8. Database Access (sqlc with pgx/v5)
*   **Rule 8.1 (SQL Files):** All SQL queries MUST be defined in `.sql` files.
*   **Rule 8.2 (sqlc.yaml Configuration):** The `sqlc.yaml` file MUST be configured to use `sql_package: "pgx/v5"` and appropriate type overrides.
*   **Rule 8.3 (Type Conversion):** All conversions between domain types and `pgtype` MUST be handled in a dedicated `internal/infrastructure/database/converters` package.
*   **Rule 8.4 (Transactions):** Repository methods that perform writes MUST accept `pgx.Tx` as a parameter.

### 5.9. Testing and CI/CD Strategy
*   **Rule 9.1 (Containerized Testing):** All integration and end-to-end tests that require external services (like a database or Redis) MUST use `testcontainers-go`.
*   **Rule 9.2 (Seeding Data for Tests):** For every new `.up.sql` migration file, a corresponding test data seed file MUST be created.
*   **Rule 9.3 (Test Coverage):** API handlers, application services, and repositories MUST have dedicated test files.
*   **Rule 9.4 (CI/CD Gate):** A pull request MUST NOT be allowed to merge into the main branch unless all tests (`go test ./...`) pass.

### 5.10. Internationalization (i18n)
*   **Rule 10.1 (Localization Support):** The application MUST support English (en) and Indonesian (id) locales.

## 6. Database Schemas for Phase 6

This phase will introduce and refine the `notification` related schemas.

### 6.1. Notification Schemas (`notification` Bounded Context)

#### `outgoing_notifications` Table:
```sql
CREATE TABLE outgoing_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    aggregate_type VARCHAR(255) NOT NULL, -- e.g., 'CashInTransaction', 'CashOutTransaction'
    aggregate_id UUID NOT NULL,
    event_type VARCHAR(255) NOT NULL, -- e.g., 'CashInPaid', 'CashOutDisbursed', 'UserRegistered'
    recipient_type VARCHAR(50) NOT NULL, -- e.g., 'WEBHOOK', 'EMAIL'
    recipient_address TEXT NOT NULL, -- e.g., webhook URL or email address
    payload JSONB NOT NULL, -- The notification payload (e.g., event data, email content)
    status VARCHAR(50) NOT NULL DEFAULT 'PENDING', -- PENDING, SENT, FAILED, RETRYING
    last_attempt_at TIMESTAMP WITH TIME ZONE,
    next_attempt_at TIMESTAMP WITH TIME ZONE,
    attempt_count INT DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### `notification_delivery_logs` Table:
```sql
CREATE TABLE notification_delivery_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    notification_id UUID NOT NULL REFERENCES outgoing_notifications(id),
    attempt_number INT NOT NULL,
    response_code INT,
    response_body TEXT,
    error_message TEXT,
    delivered_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### `notification_configs` Table:
```sql
CREATE TABLE notification_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL, -- References companies(id) from organization context
    event_type VARCHAR(255) NOT NULL, -- e.g., 'CashInPaid', 'UserRegistered'
    delivery_method VARCHAR(50) NOT NULL, -- 'WEBHOOK', 'EMAIL'
    config_details JSONB, -- e.g., webhook URL, email template ID, subject, body template
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(company_id, event_type, delivery_method)
);
```

## 7. Detailed Tasks for Phase 6

### 7.1. Notification Domain & Repository Implementation
*   **Task 7.1.1: Define Notification Domain Entities**
    *   Create `internal/domain/notification/notification.go`.
    *   Define structs for `OutgoingNotification`, `NotificationDeliveryLog`, `NotificationConfig`.
*   **Task 7.1.2: Update Database Migrations**
    *   Create a new migration `db/migrations/000006_notification_tables.up.sql` to define `outgoing_notifications`, `notification_delivery_logs`, and `notification_configs` tables. Ensure UUIDs are used for primary keys.
    *   Create corresponding `000006_notification_tables.down.sql`.
*   **Task 7.1.3: Implement Notification Repositories**
    *   Create `internal/infrastructure/database/notification/notification_repository.go`.
    *   Implement repository interfaces for all `notification` entities using `sqlc`-generated code.
    *   Ensure all `pgtype` conversions are handled by `internal/infrastructure/database/converters`.

### 7.2. Notification Application Use Cases
*   **Task 7.2.1: Implement Notification Service**
    *   Create `internal/app/notification/notification_service.go`.
    *   Implement `PublishNotification(ctx context.Context, eventType string, aggregateID UUID, payload JSONB)`:
        *   Looks up `notification_configs` for the given `eventType` and `company_id` (derived from aggregate).
        *   Creates `outgoing_notifications` records for each configured recipient/method.
        *   **Does NOT send directly; relies on background processor.**
    *   Implement `ProcessOutgoingNotification(ctx context.Context, notificationID UUID)`:
        *   Fetches `outgoing_notification` record.
        *   Based on `recipient_type` (WEBHOOK/EMAIL), calls appropriate delivery service.
        *   Updates `outgoing_notification.status`, `attempt_count`, `next_attempt_at`, `error_message`.
        *   Records `notification_delivery_log`.
        *   If failed and retries remain, schedules a retry (e.g., by updating `next_attempt_at`).
*   **Task 7.2.2: Implement Webhook Delivery Service**
    *   Create `internal/app/notification/webhook_delivery_service.go`.
    *   Implement `DeliverWebhook(ctx context.Context, url string, payload JSONB, headers map[string]string)`: Handles the actual HTTP request, signature generation, and response parsing.
    *   Integrate retry logic (e.g., exponential backoff) for failed deliveries.
*   **Task 7.2.3: Implement Email Delivery Service**
    *   Create `internal/app/notification/email_delivery_service.go`.
    *   Implement `SendEmail(ctx context.Context, recipient, subject, body string, attachments []byte)`: Encapsulates email sending logic (e.g., using a third-party email provider SDK).
*   **Task 7.2.4: Implement Event Consumers**
    *   Create `internal/app/notification/event_consumers.go`.
    *   Implement `EventHandler` implementations that subscribe to relevant domain events from other contexts (e.g., `CashInPaidEvent`, `CashOutDisbursedEvent`, `UserRegisteredEvent`).
    *   When an event is received, call `NotificationService.PublishNotification`.

### 7.3. Notification Presentation Layer (API)
*   **Task 7.3.1: Implement Notification Config Handlers**
    *   Create `internal/ports/rest/dashboard/notification_config_handler.go`.
    *   Implement handlers for `POST /notification-configs`, `GET /notification-configs/{id}`, `PUT /notification-configs/{id}`, `DELETE /notification-configs/{id}`.
*   **Task 7.3.2: Implement Notification Log Handlers**
    *   Create `internal/ports/rest/dashboard/notification_log_handler.go`.
    *   Implement handlers for `GET /notification-delivery-logs`, `GET /notification-delivery-logs/{id}`.
*   **Task 7.3.3: Update Main Application Wiring**
    *   Modify `cmd/api/server/dependencies.go` to wire up the new `Notification` repositories and services.
    *   Modify `cmd/api/server/routes.go` to define the new `notification` endpoints, ensuring proper authentication and authorization middleware is applied.
    *   Register `Notification` event consumers with the `InMemoryEventBus`.
    *   Start a background worker (Go routine) that periodically calls `NotificationService.ProcessOutgoingNotification` for pending notifications (this replaces the `OutboxProcessor` from Phase 5 for notifications).

### 7.4. Testing and Validation
*   **Task 7.4.1: Update Seed Data**
    *   Create `testdata/seeds/000006_seed_notification_data.sql` with sample data for all `notification` tables.
*   **Task 7.4.2: Implement Unit Tests**
    *   Write unit tests for `internal/app/notification` services (mocking repositories, external delivery clients, and `EventPublisher`).
*   **Task 7.4.3: Implement Integration Tests**
    *   Write integration tests for `internal/infrastructure/database/notification` repositories using `testcontainers-go` for PostgreSQL.
    *   Write integration tests for `internal/app/notification` services using `testcontainers-go` for PostgreSQL and mocked external services.
*   **Task 7.4.4: Implement End-to-End Tests**
    *   Write E2E tests for `notification` API endpoints using `SetupTestServer()` from Phase 1.
    *   Test CRUD operations for notification configurations.
    *   Test the full flow from an event being published (e.g., `UserRegisteredEvent`) to an `outgoing_notification` being created and then processed (webhook sent/email sent).
    *   Verify retry logic for failed deliveries.
*   **Task 7.4.5: Update Postman Collection**
    *   Create `docs/postman/collections/notification.postman_collection.json` with requests for all new `notification` endpoints.

## 8. Phase 6 Deliverables

*   Fully implemented `notification` bounded context with reliable outgoing communication capabilities (webhooks and emails).
*   Updated database schemas for `outgoing_notifications`, `notification_delivery_logs`, and `notification_configs`.
*   Working `NotificationService` for publishing and processing notifications.
*   Integration with the Outbox pattern for reliable notification publishing.
*   Subscription to relevant domain events from other contexts.
*   Comprehensive unit, integration, and E2E tests for the `notification` context.
*   Updated Postman collection for `notification` endpoints.
*   All code adhering to the defined coding guidelines.

## 9. Phase 6 Diagram:

```mermaid
graph TD
    subgraph "Phase 6: Notification Bounded Context Implementation"
        A[7.1. Notification Domain & Repository] --> B(7.2. Notification Application Use Cases)
        B --> C(7.3. Notification Presentation Layer)
        C --> D(7.4. Testing and Validation)
        A --> D
        B --> D
        C --> D
    end

    subgraph "7.1. Notification Domain & Repository"
        A1[7.1.1: Define Notification Domain Entities]
        A2[7.1.2: Update Database Migrations]
        A3[7.1.3: Implement Notification Repositories]
    end

    subgraph "7.2. Notification Application Use Cases"
        B1[7.2.1: Implement Notification Service]
        B2[7.2.2: Implement Webhook Delivery Service]
        B3[7.2.3: Implement Email Delivery Service]
        B4[7.2.4: Implement Event Consumers]
    end

    subgraph "7.3. Notification Presentation Layer (API)"
        C1[7.3.1: Implement Notification Config Handlers]
        C2[7.3.2: Implement Notification Log Handlers]
        C3[7.3.3: Update Main Application Wiring]
    end

    subgraph "7.4. Testing and Validation"
        D1[7.4.1: Update Seed Data]
        D2[7.4.2: Implement Unit Tests]
        D3[7.4.3: Implement Integration Tests]
        D4[7.4.4: Implement End-to-End Tests]
        D5[7.4.5: Update Postman Collection]
    end

    style A1 fill:#f9f,stroke:#333,stroke-width:2px
    style A2 fill:#f9f,stroke:#333,stroke-width:2px
    style A3 fill:#f9f,stroke:#333,stroke-width:2px

    style B1 fill:#bbf,stroke:#333,stroke-width:2px
    style B2 fill:#bbf,stroke:#333,stroke-width:2px
    style B3 fill:#bbf,stroke:#333,stroke-width:2px
    style B4 fill:#bbf,stroke:#333,stroke-width:2px

    style C1 fill:#ccf,stroke:#333,stroke-width:2px
    style C2 fill:#ccf,stroke:#333,stroke-width:2px
    style C3 fill:#ccf,stroke:#333,stroke-width:2px

    style D1 fill:#ddf,stroke:#333,stroke-width:2px
    style D2 fill:#ddf,stroke:#333,stroke-width:2px
    style D3 fill:#ddf,stroke:#333,stroke-width:2px
    style D4 fill:#ddf,stroke:#333,stroke-width:2px
    style D5 fill:#ddf,stroke:#333,stroke-width:2px

## 10. Phase Completion Checklist

This checklist serves as a verification tool to ensure all tasks, requirements, rules, and guidelines for Phase 6 have been successfully completed and adhered to. Before marking this phase as complete, all items below must be checked off.

### 10.1. General Completion & Quality
*   [ ] All tasks outlined in Section 7 ("Detailed Tasks for Phase 6") have been implemented.
*   [ ] The project builds successfully without errors (`go build ./...`).
*   [ ] All tests pass (`go test ./...`).
*   [ ] Code has been formatted with `gofmt` (`gofmt -s -w .`).
*   [ ] No new warnings or errors are introduced by linters (e.g., `golangci-lint run`).
*   [ ] All dependencies are properly managed (`go mod tidy`).

### 10.2. Architectural Adherence
*   [ ] The `internal/app/notification` and `internal/domain/notification` directories are populated as per the target structure.
*   [ ] Dependencies within the `notification` context strictly follow the Clean Architecture rule.
*   [ ] Domain Event Mechanism is correctly utilized for subscribing to events from other contexts.
*   [ ] Integration with the Outbox pattern (from Phase 5) for reliable notification publishing is functional.

### 10.3. Core Functionality Verification
*   [ ] Core functionalities for publishing and processing outgoing notifications are functional.
*   [ ] Reliable delivery mechanisms for webhooks and emails, including retry logic, are implemented.
*   [ ] Centralized notification configurations are functional.
*   [ ] `NotificationService` for publishing and processing notifications is working.
*   [ ] `WebhookDeliveryService` and `EmailDeliveryService` are implemented and functional.
*   [ ] Event consumers for relevant domain events are implemented and correctly trigger notifications.
*   [ ] API endpoints for managing notification configurations and viewing delivery logs are functional.

### 10.4. Testing & Validation
*   [ ] All unit tests for `notification` services are passing.
*   [ ] All integration tests for `notification` repositories and services are passing.
*   [ ] All E2E tests for `notification` API endpoints are passing, covering CRUD operations and full notification flow.
*   [ ] E2E tests verify retry logic for failed deliveries.
*   [ ] Postman collection for `notification` endpoints is updated and functional.
*   [ ] Seed data for `notification` tables is correctly applied in tests.

### 10.5. Coding Guidelines Compliance (Self-Assessment)
*   [ ] **Rule 1 (General):** `gofmt`, `camelCase`, short-lowercase package names, interface-based design are followed.
*   [ ] **Rule 2 (Architecture):** `API` -> `Application` -> `Domain` dependency rule and thin handlers are enforced.
*   [ ] **Rule 3 (Error Handling):** Error wrapping, custom errors, centralized mapping, and Sentry integration are used.
*   [ ] **Rule 4 (API Responses):** `webapi` helpers are used for all responses, and error messages are localized.
*   [ ] **Rule 5 (Logging):** Structured, contextual logging with `zerolog` is implemented.
*   [ ] **Rule 6 (Concurrency):** Panic safety and graceful termination are considered.
*   [ ] **Rule 7 (Middleware):** The exact middleware order is followed.
*   [ ] **Rule 8 (Database):** `.sql` files for `sqlc`, `pgx/v5` configuration, centralized type converters, and transactional repositories are used.
*   [ ] **Rule 9 (Testing & CI/CD):** `testcontainers-go`, DDL/DML separation, and test coverage are adhered to.
*   [ ] **Rule 10 (Internationalization):** Localization support is correctly implemented.
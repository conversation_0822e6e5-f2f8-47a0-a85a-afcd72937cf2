# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work
go.work.sum

# Build output
/bin/
/dist/
/build/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Environment files
.env
.env.local
.env.development
.env.test
.env.production
*.env

# Configuration files with secrets
config.local.yaml
config.production.yaml
config.development.yaml

# Log files
*.log
logs/

# Database files
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/
.tmp/

# Coverage reports
coverage.out
coverage.html
coverage.xml

# Air live reload tool
.air.toml
tmp/

# Docker
.dockerignore

# Generated files
*_gen.go
*_generated.go

# sqlc generated files (we'll keep these in version control but ignore local overrides)
# internal/infrastructure/database/sqlc/*.go

# Migration files backup
*.backup

# Test data
testdata/tmp/
testdata/temp/

# Certificates and keys
*.pem
*.key
*.crt
*.p12
*.pfx

# Local development tools
.tool-versions

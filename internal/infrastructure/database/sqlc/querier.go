// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"context"

	"github.com/google/uuid"
)

type Querier interface {
	AssignRolePermission(ctx context.Context, arg AssignRolePermissionParams) error
	AssignUserRole(ctx context.Context, arg AssignUserRoleParams) error
	CountPermissions(ctx context.Context) (int64, error)
	CountRoles(ctx context.Context) (int64, error)
	CountUsers(ctx context.Context) (int64, error)
	CreatePermission(ctx context.Context, arg CreatePermissionParams) (Permission, error)
	CreateRole(ctx context.Context, arg CreateRoleParams) (Role, error)
	CreateUser(ctx context.Context, arg CreateUserParams) (User, error)
	DeletePermission(ctx context.Context, id uuid.UUID) error
	DeleteRole(ctx context.Context, id uuid.UUID) error
	DeleteUser(ctx context.Context, id uuid.UUID) error
	ExistsByEmail(ctx context.Context, email string) (bool, error)
	ExistsPermissionByName(ctx context.Context, name string) (bool, error)
	ExistsRoleByName(ctx context.Context, name string) (bool, error)
	GetPermissionByID(ctx context.Context, id uuid.UUID) (Permission, error)
	GetPermissionByName(ctx context.Context, name string) (Permission, error)
	GetPermissionByResourceAndAction(ctx context.Context, arg GetPermissionByResourceAndActionParams) (Permission, error)
	GetPermissionsByResource(ctx context.Context, resource string) ([]Permission, error)
	GetRoleByID(ctx context.Context, id uuid.UUID) (Role, error)
	GetRoleByName(ctx context.Context, name string) (Role, error)
	GetRolePermissions(ctx context.Context, roleID uuid.UUID) ([]Permission, error)
	GetRoleWithPermissions(ctx context.Context, id uuid.UUID) (GetRoleWithPermissionsRow, error)
	GetRolesWithPermission(ctx context.Context, permissionID uuid.UUID) ([]Role, error)
	GetUserByEmail(ctx context.Context, email string) (User, error)
	GetUserByID(ctx context.Context, id uuid.UUID) (User, error)
	GetUserRoles(ctx context.Context, userID uuid.UUID) ([]Role, error)
	GetUserWithRoles(ctx context.Context, id uuid.UUID) (GetUserWithRolesRow, error)
	GetUsersWithRole(ctx context.Context, roleID uuid.UUID) ([]User, error)
	ListPermissions(ctx context.Context, arg ListPermissionsParams) ([]Permission, error)
	ListRoles(ctx context.Context, arg ListRolesParams) ([]Role, error)
	ListUsers(ctx context.Context, arg ListUsersParams) ([]User, error)
	RevokeRolePermission(ctx context.Context, arg RevokeRolePermissionParams) error
	RevokeUserRole(ctx context.Context, arg RevokeUserRoleParams) error
	UpdateLastLogin(ctx context.Context, arg UpdateLastLoginParams) error
	UpdatePermission(ctx context.Context, arg UpdatePermissionParams) (Permission, error)
	UpdateRole(ctx context.Context, arg UpdateRoleParams) (Role, error)
	UpdateUser(ctx context.Context, arg UpdateUserParams) (User, error)
}

var _ Querier = (*Queries)(nil)

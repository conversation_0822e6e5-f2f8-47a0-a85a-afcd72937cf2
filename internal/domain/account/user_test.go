package account

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestUserID(t *testing.T) {
	t.Run("NewUserID creates valid UUID", func(t *testing.T) {
		userID := NewUserID()
		assert.NotEqual(t, uuid.Nil, userID.UUID())
		assert.NotEmpty(t, userID.String())
	})

	t.Run("UserIDFromString with valid UUID", func(t *testing.T) {
		validUUID := "550e8400-e29b-41d4-a716-************"
		userID, err := UserIDFromString(validUUID)

		require.NoError(t, err)
		assert.Equal(t, validUUID, userID.String())
	})

	t.Run("UserIDFromString with invalid UUID", func(t *testing.T) {
		invalidUUID := "invalid-uuid"
		_, err := UserIDFromString(invalidUUID)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid user ID format")
	})
}

func TestEmail(t *testing.T) {
	t.Run("NewEmail with valid email", func(t *testing.T) {
		validEmail := "<EMAIL>"
		email, err := NewEmail(validEmail)

		require.NoError(t, err)
		assert.Equal(t, validEmail, email.String())
	})

	t.Run("NewEmail with empty email", func(t *testing.T) {
		_, err := NewEmail("")

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "email cannot be empty")
	})

	t.Run("NewEmail with invalid email format", func(t *testing.T) {
		testCases := []string{
			"invalid",
			"@invalid",
			"test@",
			"test.com",
			"te",
		}

		for _, email := range testCases {
			t.Run("email: "+email, func(t *testing.T) {
				_, err := NewEmail(email)
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "invalid email format")
			})
		}
	})
}

func TestPasswordHash(t *testing.T) {
	t.Run("NewPasswordHash creates valid hash", func(t *testing.T) {
		hashValue := "$argon2id$v=19$m=65536,t=3,p=2$c29tZXNhbHQ$qFpFdKDD6Z1CODcaxxmMMdFuRBK/uPiTC1vFzwIUHjU"
		passwordHash := NewPasswordHash(hashValue)

		assert.Equal(t, hashValue, passwordHash.String())
	})

	t.Run("NewPasswordHash with empty hash", func(t *testing.T) {
		passwordHash := NewPasswordHash("")
		assert.Equal(t, "", passwordHash.String())
	})
}

func TestUser(t *testing.T) {
	t.Run("NewUser creates valid user", func(t *testing.T) {
		email, _ := NewEmail("<EMAIL>")
		passwordHash := NewPasswordHash("hashed_password")
		name := "Test User"

		user := NewUser(email, passwordHash, name)

		assert.NotEqual(t, UserID{}, user.ID())
		assert.Equal(t, email, user.Email())
		assert.Equal(t, passwordHash, user.PasswordHash())
		assert.Equal(t, name, user.Name())
		assert.True(t, user.IsActive())
		assert.NotZero(t, user.CreatedAt())
		assert.NotZero(t, user.UpdatedAt())
		assert.Empty(t, user.Roles())
	})

	t.Run("NewUserWithID creates user with specified fields", func(t *testing.T) {
		userID := NewUserID()
		email, _ := NewEmail("<EMAIL>")
		passwordHash := NewPasswordHash("hashed_password")
		name := "Test User"
		isActive := false
		createdAt := time.Now().Add(-24 * time.Hour)
		updatedAt := time.Now().Add(-1 * time.Hour)

		user := NewUserWithID(userID, email, passwordHash, name, isActive, createdAt, updatedAt)

		assert.Equal(t, userID, user.ID())
		assert.Equal(t, email, user.Email())
		assert.Equal(t, passwordHash, user.PasswordHash())
		assert.Equal(t, name, user.Name())
		assert.Equal(t, isActive, user.IsActive())
		assert.Equal(t, createdAt, user.CreatedAt())
		assert.Equal(t, updatedAt, user.UpdatedAt())
	})

	t.Run("UpdatePassword updates password and timestamp", func(t *testing.T) {
		email, _ := NewEmail("<EMAIL>")
		passwordHash := NewPasswordHash("old_password")
		user := NewUser(email, passwordHash, "Test User")

		oldUpdatedAt := user.UpdatedAt()
		time.Sleep(1 * time.Millisecond) // Ensure timestamp difference

		newPasswordHash := NewPasswordHash("new_password")
		user.UpdatePassword(newPasswordHash)

		assert.Equal(t, newPasswordHash, user.PasswordHash())
		assert.True(t, user.UpdatedAt().After(oldUpdatedAt))
	})

	t.Run("UpdateEmail updates email and timestamp", func(t *testing.T) {
		email, _ := NewEmail("<EMAIL>")
		passwordHash := NewPasswordHash("password")
		user := NewUser(email, passwordHash, "Test User")

		oldUpdatedAt := user.UpdatedAt()
		time.Sleep(1 * time.Millisecond)

		newEmail, _ := NewEmail("<EMAIL>")
		user.UpdateEmail(newEmail)

		assert.Equal(t, newEmail, user.Email())
		assert.True(t, user.UpdatedAt().After(oldUpdatedAt))
	})

	t.Run("UpdateName updates name and timestamp", func(t *testing.T) {
		email, _ := NewEmail("<EMAIL>")
		passwordHash := NewPasswordHash("password")
		user := NewUser(email, passwordHash, "Old Name")

		oldUpdatedAt := user.UpdatedAt()
		time.Sleep(1 * time.Millisecond)

		newName := "New Name"
		user.UpdateName(newName)

		assert.Equal(t, newName, user.Name())
		assert.True(t, user.UpdatedAt().After(oldUpdatedAt))
	})

	t.Run("Deactivate sets user inactive and updates timestamp", func(t *testing.T) {
		email, _ := NewEmail("<EMAIL>")
		passwordHash := NewPasswordHash("password")
		user := NewUser(email, passwordHash, "Test User")

		assert.True(t, user.IsActive())
		oldUpdatedAt := user.UpdatedAt()
		time.Sleep(1 * time.Millisecond)

		user.Deactivate()

		assert.False(t, user.IsActive())
		assert.True(t, user.UpdatedAt().After(oldUpdatedAt))
	})

	t.Run("Activate sets user active and updates timestamp", func(t *testing.T) {
		email, _ := NewEmail("<EMAIL>")
		passwordHash := NewPasswordHash("password")
		user := NewUserWithID(NewUserID(), email, passwordHash, "Test User", false, time.Now(), time.Now())

		assert.False(t, user.IsActive())
		oldUpdatedAt := user.UpdatedAt()
		time.Sleep(1 * time.Millisecond)

		user.Activate()

		assert.True(t, user.IsActive())
		assert.True(t, user.UpdatedAt().After(oldUpdatedAt))
	})

	t.Run("AddRole adds role to user", func(t *testing.T) {
		email, _ := NewEmail("<EMAIL>")
		passwordHash := NewPasswordHash("password")
		user := NewUser(email, passwordHash, "Test User")

		role := NewRole("admin", "Administrator role")
		user.AddRole(*role)

		assert.Len(t, user.Roles(), 1)
		assert.Equal(t, role.ID(), user.Roles()[0].ID())
	})

	t.Run("AddRole does not add duplicate role", func(t *testing.T) {
		email, _ := NewEmail("<EMAIL>")
		passwordHash := NewPasswordHash("password")
		user := NewUser(email, passwordHash, "Test User")

		role := NewRole("admin", "Administrator role")
		user.AddRole(*role)
		user.AddRole(*role) // Add same role again

		assert.Len(t, user.Roles(), 1)
	})

	t.Run("RemoveRole removes role from user", func(t *testing.T) {
		email, _ := NewEmail("<EMAIL>")
		passwordHash := NewPasswordHash("password")
		user := NewUser(email, passwordHash, "Test User")

		role1 := NewRole("admin", "Administrator role")
		role2 := NewRole("user", "User role")
		user.AddRole(*role1)
		user.AddRole(*role2)

		assert.Len(t, user.Roles(), 2)

		user.RemoveRole(role1.ID())

		assert.Len(t, user.Roles(), 1)
		assert.Equal(t, role2.ID(), user.Roles()[0].ID())
	})

	t.Run("HasRole returns true for existing role", func(t *testing.T) {
		email, _ := NewEmail("<EMAIL>")
		passwordHash := NewPasswordHash("password")
		user := NewUser(email, passwordHash, "Test User")

		role := NewRole("admin", "Administrator role")
		user.AddRole(*role)

		assert.True(t, user.HasRole(role.ID()))
	})

	t.Run("HasRole returns false for non-existing role", func(t *testing.T) {
		email, _ := NewEmail("<EMAIL>")
		passwordHash := NewPasswordHash("password")
		user := NewUser(email, passwordHash, "Test User")

		role := NewRole("admin", "Administrator role")

		assert.False(t, user.HasRole(role.ID()))
	})
}

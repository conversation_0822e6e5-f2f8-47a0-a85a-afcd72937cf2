package handler_test

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/wongpinter/payment-gateway/internal/application/account/service"
	"github.com/wongpinter/payment-gateway/internal/domain/account"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/config"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/database"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/database/repository"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/events"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/middleware"
	redispkg "github.com/wongpinter/payment-gateway/internal/infrastructure/redis"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/security"
	testingpkg "github.com/wongpinter/payment-gateway/internal/infrastructure/testing"
	"github.com/wongpinter/payment-gateway/internal/interfaces/http/handler"
	authmiddleware "github.com/wongpinter/payment-gateway/internal/interfaces/http/middleware"
	"github.com/wongpinter/payment-gateway/internal/interfaces/http/routes"
)

// E2ETestSuite represents the E2E test suite
type E2ETestSuite struct {
	server         *httptest.Server
	router         *gin.Engine
	db             *database.DB
	redisClient    *redis.Client
	config         *config.Config
	seedSetup      *testingpkg.TestSeedSetup
	migrationMgr   *testingpkg.MigrationManager
	authHandler    *handler.AuthHandler
	authMiddleware *authmiddleware.AuthMiddleware
	ctx            context.Context
}

// setupE2ETestSuite creates a complete E2E test environment
func setupE2ETestSuite(t *testing.T) *E2ETestSuite {
	if testing.Short() {
		t.Skip("Skipping E2E tests in short mode")
	}

	ctx := context.Background()

	// Initialize test containers
	err := testingpkg.InitTestContainers(ctx)
	require.NoError(t, err, "Failed to initialize test containers")

	// Get database and Redis configurations
	dbConfig := testingpkg.GetTestDatabaseConfig()
	redisConfig := testingpkg.GetTestRedisConfig()
	require.NotNil(t, dbConfig, "Database config should not be nil")
	require.NotNil(t, redisConfig, "Redis config should not be nil")

	// Create database connection
	db, err := database.New(ctx, dbConfig)
	require.NoError(t, err, "Failed to create database connection")

	// Create Redis client
	redisClient, err := redispkg.New(redisConfig)
	require.NoError(t, err, "Failed to create Redis client")

	// Create test configuration
	cfg := &config.Config{
		Server: config.ServerConfig{
			Host:         "localhost",
			Port:         8080,
			ReadTimeout:  30 * time.Second,
			WriteTimeout: 30 * time.Second,
			IdleTimeout:  60 * time.Second,
		},
		Logger: config.LoggerConfig{
			Level:  "info",
			Format: "console",
		},
		Database: *dbConfig,
		Redis:    *redisConfig,
		JWT: config.JWTConfig{
			Secret:               "test-secret-key-for-e2e-tests-only",
			AccessTokenDuration:  15 * time.Minute,
			RefreshTokenDuration: 24 * time.Hour,
		},
	}

	// Run migrations
	migrationMgr := testingpkg.NewMigrationManager(db, dbConfig)
	err = migrationMgr.RunMigrations(ctx)
	require.NoError(t, err, "Failed to run migrations")

	// Load seed data - get absolute path to testdata/seeds
	seedPath := "../../../../testdata/seeds"
	seedSetup := testingpkg.NewTestSeedSetup(db, seedPath)
	err = seedSetup.LoadBasicSeeds(ctx)
	require.NoError(t, err, "Failed to load seed data")

	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create router
	router := gin.New()

	// Setup middleware chain
	middleware.SetupMiddlewareChain(router, cfg)

	// Initialize dependencies
	suite := &E2ETestSuite{
		router:       router,
		db:           db,
		redisClient:  redisClient,
		config:       cfg,
		seedSetup:    seedSetup,
		migrationMgr: migrationMgr,
		ctx:          ctx,
	}

	// Setup application dependencies
	err = suite.setupDependencies()
	require.NoError(t, err, "Failed to setup dependencies")

	// Setup routes
	suite.setupRoutes()

	// Create HTTP test server
	suite.server = httptest.NewServer(suite.router)

	// Cleanup function
	t.Cleanup(func() {
		suite.cleanup()
	})

	return suite
}

// setupDependencies initializes all application dependencies with proper logger
func (s *E2ETestSuite) setupDependencies() error {
	// Create a logger for the event publisher
	logger := zerolog.New(os.Stdout).With().Timestamp().Logger()

	// Initialize infrastructure dependencies
	passwordHasher := security.NewArgon2PasswordHasher()
	eventPublisher := events.NewInMemoryEventPublisher(&logger)

	// Initialize JWT components
	jwtBlacklist := security.NewJWTBlacklist(s.redisClient)
	jwtConfig := security.JWTManagerConfig{
		Secret:          s.config.JWT.Secret,
		AccessTokenTTL:  s.config.JWT.AccessTokenDuration,
		RefreshTokenTTL: s.config.JWT.RefreshTokenDuration,
	}
	jwtManager := security.NewJWTManager(jwtConfig, jwtBlacklist)

	// Initialize repositories
	userRepo := repository.NewUserRepository(s.db.Pool)
	roleRepo := repository.NewRoleRepository(s.db.Pool)
	permissionRepo := repository.NewPermissionRepository(s.db.Pool)
	sessionRepo := repository.NewSessionRepository(s.redisClient)

	// Initialize application services
	userService := service.NewUserService(
		userRepo,
		roleRepo,
		passwordHasher,
		eventPublisher,
	)

	// Create JWT adapter
	jwtAdapter := &JWTAdapter{jwtManager: jwtManager}

	authService := service.NewAuthService(
		userRepo,
		roleRepo,
		permissionRepo,
		sessionRepo,
		passwordHasher,
		eventPublisher,
		jwtAdapter,
	)

	// Initialize HTTP layer
	s.authHandler = handler.NewAuthHandler(userService, authService)
	s.authMiddleware = authmiddleware.NewAuthMiddleware(authService)

	return nil
}

// JWTAdapter adapts the security.JWTManager to implement service.JWTManager interface
type JWTAdapter struct {
	jwtManager *security.JWTManager
}

func (a *JWTAdapter) GenerateAccessToken(ctx context.Context, user *account.User, roles []account.Role) (string, time.Time, error) {
	return a.jwtManager.GenerateAccessToken(ctx, user, roles)
}

func (a *JWTAdapter) GenerateRefreshToken(ctx context.Context, user *account.User) (string, time.Time, error) {
	return a.jwtManager.GenerateRefreshToken(ctx, user)
}

func (a *JWTAdapter) ValidateAccessToken(ctx context.Context, tokenString string) (*service.AccessTokenClaims, error) {
	claims, err := a.jwtManager.ValidateAccessToken(ctx, tokenString)
	if err != nil {
		return nil, err
	}
	return &service.AccessTokenClaims{
		UserID: claims.UserID,
		Email:  claims.Email,
		Name:   claims.Name,
		Roles:  claims.Roles,
	}, nil
}

func (a *JWTAdapter) ValidateRefreshToken(ctx context.Context, tokenString string) (*service.RefreshTokenClaims, error) {
	claims, err := a.jwtManager.ValidateRefreshToken(ctx, tokenString)
	if err != nil {
		return nil, err
	}
	return &service.RefreshTokenClaims{
		UserID: claims.UserID,
	}, nil
}

func (a *JWTAdapter) BlacklistToken(ctx context.Context, tokenString string) error {
	return a.jwtManager.BlacklistToken(ctx, tokenString)
}

func (a *JWTAdapter) BlacklistUserTokens(ctx context.Context, userID string, expiresAt time.Time) error {
	return a.jwtManager.BlacklistUserTokens(ctx, userID, expiresAt)
}

func (a *JWTAdapter) ExtractTokenID(tokenString string) (string, error) {
	return a.jwtManager.ExtractTokenID(tokenString)
}

func (a *JWTAdapter) GetTokenTTLs() (accessTTL, refreshTTL time.Duration) {
	return a.jwtManager.GetTokenTTLs()
}

// setupRoutes sets up all application routes
func (s *E2ETestSuite) setupRoutes() {
	// Health check endpoint
	s.router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "ok",
			"timestamp": time.Now().Unix(),
		})
	})

	// API v1 routes
	v1 := s.router.Group("/api/v1")

	// Authentication routes
	routes.AuthRoutes(v1, s.authHandler, s.authMiddleware)
}

// cleanup cleans up test resources
func (s *E2ETestSuite) cleanup() {
	if s.server != nil {
		s.server.Close()
	}
	if s.seedSetup != nil {
		// Clear seed data
		s.seedSetup.SeedManager.ClearAllData(s.ctx)
	}
	if s.migrationMgr != nil {
		// Drop database schema
		s.migrationMgr.DropDatabase(s.ctx)
	}
	if s.db != nil {
		s.db.Close()
	}
	if s.redisClient != nil {
		s.redisClient.Close()
	}
}

// Helper functions for making HTTP requests
func (s *E2ETestSuite) makeRequest(method, path string, body interface{}, headers map[string]string) (*http.Response, error) {
	var reqBody *bytes.Buffer
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, err
		}
		reqBody = bytes.NewBuffer(jsonBody)
	} else {
		reqBody = bytes.NewBuffer(nil)
	}

	req, err := http.NewRequest(method, s.server.URL+path, reqBody)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	client := &http.Client{}
	return client.Do(req)
}

func (s *E2ETestSuite) parseResponse(resp *http.Response, target interface{}) error {
	defer resp.Body.Close()
	return json.NewDecoder(resp.Body).Decode(target)
}

// Test data structures
type APIResponse struct {
	Success       bool        `json:"success"`
	Message       string      `json:"message"`
	Data          interface{} `json:"data"`
	Error         *ErrorInfo  `json:"error,omitempty"`
	CorrelationID string      `json:"correlation_id"`
}

type ErrorInfo struct {
	Code     string      `json:"code"`
	Message  string      `json:"message"`
	Details  interface{} `json:"details,omitempty"`
	SentryID string      `json:"sentry_id,omitempty"`
}

type AuthResponse struct {
	AccessToken  string   `json:"access_token"`
	RefreshToken string   `json:"refresh_token"`
	ExpiresAt    string   `json:"expires_at"`
	User         UserInfo `json:"user"`
}

type UserInfo struct {
	ID       string   `json:"id"`
	Email    string   `json:"email"`
	Name     string   `json:"name"`
	IsActive bool     `json:"is_active"`
	Roles    []string `json:"roles"`
}

type RegisterResponse struct {
	ID        string    `json:"id"`
	Email     string    `json:"email"`
	Name      string    `json:"name"`
	IsActive  bool      `json:"is_active"`
	CreatedAt time.Time `json:"created_at"`
}

// TestAuthE2E_CompleteUserJourney tests the complete user authentication journey
func TestAuthE2E_CompleteUserJourney(t *testing.T) {
	suite := setupE2ETestSuite(t)

	// Test data
	testUser := map[string]interface{}{
		"email":    "<EMAIL>",
		"password": "SecurePassword123!",
		"name":     "E2E Test User",
	}

	t.Run("1. Health Check", func(t *testing.T) {
		resp, err := suite.makeRequest("GET", "/health", nil, nil)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var healthResp map[string]interface{}
		err = suite.parseResponse(resp, &healthResp)
		require.NoError(t, err)
		assert.Equal(t, "ok", healthResp["status"])
	})

	var accessToken, refreshToken string

	t.Run("2. User Registration", func(t *testing.T) {
		resp, err := suite.makeRequest("POST", "/api/v1/auth/register", testUser, nil)
		require.NoError(t, err)
		assert.Equal(t, http.StatusCreated, resp.StatusCode)

		var apiResp APIResponse
		err = suite.parseResponse(resp, &apiResp)
		require.NoError(t, err)

		assert.True(t, apiResp.Success)
		assert.Equal(t, "User registered successfully", apiResp.Message)
		assert.NotEmpty(t, apiResp.CorrelationID)

		// Verify response data
		dataBytes, err := json.Marshal(apiResp.Data)
		require.NoError(t, err)

		var registerResp RegisterResponse
		err = json.Unmarshal(dataBytes, &registerResp)
		require.NoError(t, err)

		assert.NotEmpty(t, registerResp.ID)
		assert.Equal(t, testUser["email"], registerResp.Email)
		assert.Equal(t, testUser["name"], registerResp.Name)
		assert.True(t, registerResp.IsActive)
		assert.False(t, registerResp.CreatedAt.IsZero())
	})

	t.Run("3. User Registration - Duplicate Email", func(t *testing.T) {
		resp, err := suite.makeRequest("POST", "/api/v1/auth/register", testUser, nil)
		require.NoError(t, err)
		assert.Equal(t, http.StatusConflict, resp.StatusCode)

		var apiResp APIResponse
		err = suite.parseResponse(resp, &apiResp)
		require.NoError(t, err)

		assert.False(t, apiResp.Success)
		assert.Equal(t, "Request failed", apiResp.Message)
		require.NotNil(t, apiResp.Error)
		assert.Equal(t, "User already exists", apiResp.Error.Message)
	})

	t.Run("4. User Login", func(t *testing.T) {
		loginData := map[string]interface{}{
			"email":    testUser["email"],
			"password": testUser["password"],
		}

		resp, err := suite.makeRequest("POST", "/api/v1/auth/login", loginData, nil)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var apiResp APIResponse
		err = suite.parseResponse(resp, &apiResp)
		require.NoError(t, err)

		assert.True(t, apiResp.Success)
		assert.Equal(t, "Login successful", apiResp.Message)

		// Verify response data
		dataBytes, err := json.Marshal(apiResp.Data)
		require.NoError(t, err)

		var authResp AuthResponse
		err = json.Unmarshal(dataBytes, &authResp)
		require.NoError(t, err)

		assert.NotEmpty(t, authResp.AccessToken)
		assert.NotEmpty(t, authResp.RefreshToken)
		assert.NotEmpty(t, authResp.ExpiresAt)

		// Verify user info
		assert.NotEmpty(t, authResp.User.ID)
		assert.Equal(t, testUser["email"], authResp.User.Email)
		assert.Equal(t, testUser["name"], authResp.User.Name)
		assert.True(t, authResp.User.IsActive)

		// Store tokens for subsequent tests
		accessToken = authResp.AccessToken
		refreshToken = authResp.RefreshToken
	})

	t.Run("5. User Login - Invalid Credentials", func(t *testing.T) {
		loginData := map[string]interface{}{
			"email":    testUser["email"],
			"password": "WrongPassword123!",
		}

		resp, err := suite.makeRequest("POST", "/api/v1/auth/login", loginData, nil)
		require.NoError(t, err)
		assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)

		var apiResp APIResponse
		err = suite.parseResponse(resp, &apiResp)
		require.NoError(t, err)

		assert.False(t, apiResp.Success)
		assert.Equal(t, "Request failed", apiResp.Message)
		require.NotNil(t, apiResp.Error)
		assert.Equal(t, "Invalid email or password", apiResp.Error.Message)
	})

	t.Run("6. Get Profile - Authenticated", func(t *testing.T) {
		headers := map[string]string{
			"Authorization": "Bearer " + accessToken,
		}

		resp, err := suite.makeRequest("GET", "/api/v1/auth/profile", nil, headers)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var apiResp APIResponse
		err = suite.parseResponse(resp, &apiResp)
		require.NoError(t, err)

		assert.True(t, apiResp.Success)
		assert.Equal(t, "Profile retrieved successfully", apiResp.Message)

		// Verify user info
		dataBytes, err := json.Marshal(apiResp.Data)
		require.NoError(t, err)

		var userInfo UserInfo
		err = json.Unmarshal(dataBytes, &userInfo)
		require.NoError(t, err)

		assert.NotEmpty(t, userInfo.ID)
		assert.Equal(t, testUser["email"], userInfo.Email)
		assert.Equal(t, testUser["name"], userInfo.Name)
		assert.True(t, userInfo.IsActive)
	})

	t.Run("7. Get Profile - Unauthenticated", func(t *testing.T) {
		resp, err := suite.makeRequest("GET", "/api/v1/auth/profile", nil, nil)
		require.NoError(t, err)
		assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)

		var apiResp APIResponse
		err = suite.parseResponse(resp, &apiResp)
		require.NoError(t, err)

		assert.False(t, apiResp.Success)
		assert.Equal(t, "Request failed", apiResp.Message)
		require.NotNil(t, apiResp.Error)
		assert.Contains(t, apiResp.Error.Message, "Authorization token required")
	})

	var newAccessToken, newRefreshToken string

	t.Run("8. Refresh Token", func(t *testing.T) {
		refreshData := map[string]interface{}{
			"refresh_token": refreshToken,
		}

		resp, err := suite.makeRequest("POST", "/api/v1/auth/refresh", refreshData, nil)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var apiResp APIResponse
		err = suite.parseResponse(resp, &apiResp)
		require.NoError(t, err)

		assert.True(t, apiResp.Success)
		assert.Equal(t, "Token refreshed successfully", apiResp.Message)

		// Verify response data
		dataBytes, err := json.Marshal(apiResp.Data)
		require.NoError(t, err)

		var authResp AuthResponse
		err = json.Unmarshal(dataBytes, &authResp)
		require.NoError(t, err)

		assert.NotEmpty(t, authResp.AccessToken)
		assert.NotEmpty(t, authResp.RefreshToken)
		assert.NotEqual(t, accessToken, authResp.AccessToken) // Should be different

		// Store new tokens
		newAccessToken = authResp.AccessToken
		newRefreshToken = authResp.RefreshToken
	})

	t.Run("9. Refresh Token - Invalid Token", func(t *testing.T) {
		refreshData := map[string]interface{}{
			"refresh_token": "invalid-refresh-token",
		}

		resp, err := suite.makeRequest("POST", "/api/v1/auth/refresh", refreshData, nil)
		require.NoError(t, err)
		assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)

		var apiResp APIResponse
		err = suite.parseResponse(resp, &apiResp)
		require.NoError(t, err)

		assert.False(t, apiResp.Success)
		assert.Equal(t, "Request failed", apiResp.Message)
		require.NotNil(t, apiResp.Error)
		assert.Contains(t, apiResp.Error.Message, "Invalid refresh token")
	})

	t.Run("10. Use New Access Token", func(t *testing.T) {
		headers := map[string]string{
			"Authorization": "Bearer " + newAccessToken,
		}

		resp, err := suite.makeRequest("GET", "/api/v1/auth/profile", nil, headers)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var apiResp APIResponse
		err = suite.parseResponse(resp, &apiResp)
		require.NoError(t, err)

		assert.True(t, apiResp.Success)
		assert.Equal(t, "Profile retrieved successfully", apiResp.Message)
	})

	t.Run("11. Logout", func(t *testing.T) {
		logoutData := map[string]interface{}{
			"refresh_token": newRefreshToken,
		}

		// Include access token in Authorization header for blacklisting
		headers := map[string]string{
			"Authorization": "Bearer " + newAccessToken,
		}

		resp, err := suite.makeRequest("POST", "/api/v1/auth/logout", logoutData, headers)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var apiResp APIResponse
		err = suite.parseResponse(resp, &apiResp)
		require.NoError(t, err)

		assert.True(t, apiResp.Success)
		assert.Equal(t, "Logout successful", apiResp.Message)
	})

	t.Run("12. Use Token After Logout", func(t *testing.T) {
		headers := map[string]string{
			"Authorization": "Bearer " + newAccessToken,
		}

		resp, err := suite.makeRequest("GET", "/api/v1/auth/profile", nil, headers)
		require.NoError(t, err)
		assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)

		var apiResp APIResponse
		err = suite.parseResponse(resp, &apiResp)
		require.NoError(t, err)

		assert.False(t, apiResp.Success)
		assert.Equal(t, "Request failed", apiResp.Message)
		require.NotNil(t, apiResp.Error)
		assert.Contains(t, apiResp.Error.Message, "revoked")
	})
}

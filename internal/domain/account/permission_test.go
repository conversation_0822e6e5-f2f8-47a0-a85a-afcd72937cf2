package account

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestPermissionID(t *testing.T) {
	t.Run("NewPermissionID creates valid UUID", func(t *testing.T) {
		permissionID := NewPermissionID()
		assert.NotEqual(t, uuid.Nil, permissionID.UUID())
		assert.NotEmpty(t, permissionID.String())
	})

	t.Run("PermissionIDFromString with valid UUID", func(t *testing.T) {
		validUUID := "550e8400-e29b-41d4-a716-************"
		permissionID, err := PermissionIDFromString(validUUID)

		require.NoError(t, err)
		assert.Equal(t, validUUID, permissionID.String())
	})

	t.Run("PermissionIDFromString with invalid UUID", func(t *testing.T) {
		invalidUUID := "invalid-uuid"
		_, err := PermissionIDFromString(invalidUUID)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid permission ID format")
	})
}

func TestPermission(t *testing.T) {
	t.Run("NewPermission creates valid permission", func(t *testing.T) {
		name := "user.create"
		description := "Create users"
		resource := "user"
		action := "create"

		permission := NewPermission(name, description, resource, action)

		assert.NotEqual(t, PermissionID{}, permission.ID())
		assert.Equal(t, name, permission.Name())
		assert.Equal(t, description, permission.Description())
		assert.Equal(t, resource, permission.Resource())
		assert.Equal(t, action, permission.Action())
		assert.True(t, permission.IsActive())
		assert.NotZero(t, permission.CreatedAt())
		assert.NotZero(t, permission.UpdatedAt())
	})

	t.Run("NewPermissionWithID creates permission with specified fields", func(t *testing.T) {
		permissionID := NewPermissionID()
		name := "user.create"
		description := "Create users"
		resource := "user"
		action := "create"
		isActive := false
		createdAt := time.Now().Add(-24 * time.Hour)
		updatedAt := time.Now().Add(-1 * time.Hour)

		permission := NewPermissionWithID(permissionID, name, description, resource, action, isActive, createdAt, updatedAt)

		assert.Equal(t, permissionID, permission.ID())
		assert.Equal(t, name, permission.Name())
		assert.Equal(t, description, permission.Description())
		assert.Equal(t, resource, permission.Resource())
		assert.Equal(t, action, permission.Action())
		assert.Equal(t, isActive, permission.IsActive())
		assert.Equal(t, createdAt, permission.CreatedAt())
		assert.Equal(t, updatedAt, permission.UpdatedAt())
	})

	t.Run("UpdateName updates name and timestamp", func(t *testing.T) {
		permission := NewPermission("old.name", "Description", "resource", "action")

		oldUpdatedAt := permission.UpdatedAt()
		time.Sleep(1 * time.Millisecond) // Ensure timestamp difference

		newName := "new.name"
		permission.UpdateName(newName)

		assert.Equal(t, newName, permission.Name())
		assert.True(t, permission.UpdatedAt().After(oldUpdatedAt))
	})

	t.Run("UpdateDescription updates description and timestamp", func(t *testing.T) {
		permission := NewPermission("name", "old_description", "resource", "action")

		oldUpdatedAt := permission.UpdatedAt()
		time.Sleep(1 * time.Millisecond)

		newDescription := "new_description"
		permission.UpdateDescription(newDescription)

		assert.Equal(t, newDescription, permission.Description())
		assert.True(t, permission.UpdatedAt().After(oldUpdatedAt))
	})

	t.Run("UpdateResource updates resource and timestamp", func(t *testing.T) {
		permission := NewPermission("name", "description", "old_resource", "action")

		oldUpdatedAt := permission.UpdatedAt()
		time.Sleep(1 * time.Millisecond)

		newResource := "new_resource"
		permission.UpdateResource(newResource)

		assert.Equal(t, newResource, permission.Resource())
		assert.True(t, permission.UpdatedAt().After(oldUpdatedAt))
	})

	t.Run("UpdateAction updates action and timestamp", func(t *testing.T) {
		permission := NewPermission("name", "description", "resource", "old_action")

		oldUpdatedAt := permission.UpdatedAt()
		time.Sleep(1 * time.Millisecond)

		newAction := "new_action"
		permission.UpdateAction(newAction)

		assert.Equal(t, newAction, permission.Action())
		assert.True(t, permission.UpdatedAt().After(oldUpdatedAt))
	})

	t.Run("Deactivate sets permission inactive and updates timestamp", func(t *testing.T) {
		permission := NewPermission("user.create", "Create users", "user", "create")

		assert.True(t, permission.IsActive())
		oldUpdatedAt := permission.UpdatedAt()
		time.Sleep(1 * time.Millisecond)

		permission.Deactivate()

		assert.False(t, permission.IsActive())
		assert.True(t, permission.UpdatedAt().After(oldUpdatedAt))
	})

	t.Run("Activate sets permission active and updates timestamp", func(t *testing.T) {
		permission := NewPermissionWithID(NewPermissionID(), "user.create", "Create users", "user", "create", false, time.Now(), time.Now())

		assert.False(t, permission.IsActive())
		oldUpdatedAt := permission.UpdatedAt()
		time.Sleep(1 * time.Millisecond)

		permission.Activate()

		assert.True(t, permission.IsActive())
		assert.True(t, permission.UpdatedAt().After(oldUpdatedAt))
	})

	t.Run("FullName returns formatted permission string", func(t *testing.T) {
		permission := NewPermission("user.create", "Create users", "user", "create")

		expected := "user:create"
		assert.Equal(t, expected, permission.FullName())
	})

	t.Run("Resource and Action return correct values", func(t *testing.T) {
		permission := NewPermission("user.create", "Create users", "user", "create")

		assert.Equal(t, "user", permission.Resource())
		assert.Equal(t, "create", permission.Action())
		assert.NotEqual(t, "role", permission.Resource())
		assert.NotEqual(t, "read", permission.Action())
	})

	t.Run("Equals compares permissions correctly", func(t *testing.T) {
		permission1 := NewPermission("user.create", "Create users", "user", "create")
		permission2 := NewPermission("user.read", "Read users", "user", "read")
		permission3 := NewPermissionWithID(permission1.ID(), "user.create", "Create users", "user", "create", true, time.Now(), time.Now())

		assert.True(t, permission1.Equals(*permission3))
		assert.False(t, permission1.Equals(*permission2))
	})
}

func TestPermissionConstants(t *testing.T) {
	t.Run("PermissionUserCreate has correct values", func(t *testing.T) {
		assert.Equal(t, "user.create", PermissionUserCreate.Name())
		assert.Equal(t, "Create new users", PermissionUserCreate.Description())
		assert.Equal(t, "user", PermissionUserCreate.Resource())
		assert.Equal(t, "create", PermissionUserCreate.Action())
		assert.True(t, PermissionUserCreate.IsActive())
	})

	t.Run("PermissionUserRead has correct values", func(t *testing.T) {
		assert.Equal(t, "user.read", PermissionUserRead.Name())
		assert.Equal(t, "Read user information", PermissionUserRead.Description())
		assert.Equal(t, "user", PermissionUserRead.Resource())
		assert.Equal(t, "read", PermissionUserRead.Action())
		assert.True(t, PermissionUserRead.IsActive())
	})

	t.Run("Permission constants have unique IDs", func(t *testing.T) {
		permissions := []*Permission{
			PermissionUserCreate,
			PermissionUserRead,
		}

		ids := make(map[string]bool)
		for _, perm := range permissions {
			id := perm.ID().String()
			assert.False(t, ids[id], "Duplicate permission ID found: %s", id)
			ids[id] = true
		}
	})
}

package config

import (
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestLoad(t *testing.T) {
	tests := []struct {
		name        string
		configFile  string
		envVars     map[string]string
		expectError bool
		validate    func(t *testing.T, cfg *Config)
	}{
		{
			name:       "load default config",
			configFile: "../../../configs/config.yaml",
			envVars: map[string]string{
				"PAYMENT_GATEWAY_JWT_SECRET": "test-secret-key-for-testing",
			},
			validate: func(t *testing.T, cfg *Config) {
				assert.Equal(t, "0.0.0.0", cfg.Server.Host)
				assert.Equal(t, 8080, cfg.Server.Port)
				assert.Equal(t, "info", cfg.Logger.Level)
				assert.Equal(t, "en", cfg.I18n.DefaultLanguage)
			},
		},
		{
			name:       "override with environment variables",
			configFile: "../../../configs/config.yaml",
			envVars: map[string]string{
				"PAYMENT_GATEWAY_SERVER_HOST":  "0.0.0.0",
				"PAYMENT_GATEWAY_SERVER_PORT":  "9090",
				"PAYMENT_GATEWAY_LOGGER_LEVEL": "debug",
				"PAYMENT_GATEWAY_JWT_SECRET":   "test-secret-key-for-testing",
			},
			validate: func(t *testing.T, cfg *Config) {
				assert.Equal(t, "0.0.0.0", cfg.Server.Host)
				assert.Equal(t, 9090, cfg.Server.Port)
				assert.Equal(t, "debug", cfg.Logger.Level)
			},
		},
		{
			name:        "non-existent config file",
			configFile:  "non-existent.yaml",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set environment variables
			for key, value := range tt.envVars {
				os.Setenv(key, value)
				defer os.Unsetenv(key)
			}

			cfg, err := Load(tt.configFile)

			if tt.expectError {
				assert.Error(t, err)
				return
			}

			require.NoError(t, err)
			require.NotNil(t, cfg)

			if tt.validate != nil {
				tt.validate(t, cfg)
			}
		})
	}
}

func TestValidate(t *testing.T) {
	tests := []struct {
		name        string
		config      *Config
		expectError bool
	}{
		{
			name: "valid config",
			config: &Config{
				Server: ServerConfig{
					Host: "localhost",
					Port: 8080,
				},
				Logger: LoggerConfig{
					Level:  "info",
					Format: "json",
				},
				Database: DatabaseConfig{
					Host:     "localhost",
					Port:     5432,
					Name:     "test_db",
					User:     "test_user",
					Password: "test_pass",
				},
				JWT: JWTConfig{
					Secret:               "test-secret-key-that-is-not-default",
					AccessTokenDuration:  15 * time.Minute,
					RefreshTokenDuration: 24 * time.Hour,
				},
			},
			expectError: false,
		},
		{
			name: "invalid server port",
			config: &Config{
				Server: ServerConfig{
					Host: "localhost",
					Port: -1,
				},
			},
			expectError: true,
		},
		{
			name: "invalid logger level",
			config: &Config{
				Logger: LoggerConfig{
					Level: "invalid",
				},
			},
			expectError: true,
		},
		{
			name: "missing database name",
			config: &Config{
				Database: DatabaseConfig{
					Host: "localhost",
					Port: 5432,
					User: "test_user",
				},
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validate(tt.config)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestDatabaseConfig_GetDatabaseDSN(t *testing.T) {
	cfg := &DatabaseConfig{
		Host:     "localhost",
		Port:     5432,
		Name:     "test_db",
		User:     "test_user",
		Password: "test_pass",
		SSLMode:  "disable",
	}

	dsn := cfg.GetDatabaseDSN()
	expected := "host=localhost port=5432 user=test_user password=test_pass dbname=test_db sslmode=disable"
	assert.Equal(t, expected, dsn)
}

func TestRedisConfig_GetRedisAddr(t *testing.T) {
	cfg := &RedisConfig{
		Host: "localhost",
		Port: 6379,
	}

	addr := cfg.GetRedisAddr()
	expected := "localhost:6379"
	assert.Equal(t, expected, addr)
}

func TestServerConfig_GetServerAddr(t *testing.T) {
	cfg := &ServerConfig{
		Host: "0.0.0.0",
		Port: 8080,
	}

	addr := cfg.GetServerAddr()
	expected := "0.0.0.0:8080"
	assert.Equal(t, expected, addr)
}

func TestValidateServerConfig(t *testing.T) {
	tests := []struct {
		name        string
		config      *ServerConfig
		expectError bool
	}{
		{
			name: "valid config",
			config: &ServerConfig{
				Host:         "localhost",
				Port:         8080,
				ReadTimeout:  30 * time.Second,
				WriteTimeout: 30 * time.Second,
				IdleTimeout:  60 * time.Second,
			},
			expectError: false,
		},
		{
			name: "invalid port - negative",
			config: &ServerConfig{
				Host: "localhost",
				Port: -1,
			},
			expectError: true,
		},
		{
			name: "invalid port - too high",
			config: &ServerConfig{
				Host: "localhost",
				Port: 70000,
			},
			expectError: true,
		},
		{
			name: "empty host",
			config: &ServerConfig{
				Host: "",
				Port: 8080,
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateServerConfig(tt.config)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateLoggerConfig(t *testing.T) {
	tests := []struct {
		name        string
		config      *LoggerConfig
		expectError bool
	}{
		{
			name: "valid config - info level",
			config: &LoggerConfig{
				Level:  "info",
				Format: "json",
			},
			expectError: false,
		},
		{
			name: "valid config - debug level",
			config: &LoggerConfig{
				Level:  "debug",
				Format: "console",
			},
			expectError: false,
		},
		{
			name: "invalid level",
			config: &LoggerConfig{
				Level:  "invalid",
				Format: "json",
			},
			expectError: true,
		},
		{
			name: "invalid format",
			config: &LoggerConfig{
				Level:  "info",
				Format: "invalid",
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateLoggerConfig(tt.config)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateDatabaseConfig(t *testing.T) {
	tests := []struct {
		name        string
		config      *DatabaseConfig
		expectError bool
	}{
		{
			name: "valid config",
			config: &DatabaseConfig{
				Host:     "localhost",
				Port:     5432,
				Name:     "test_db",
				User:     "test_user",
				Password: "test_pass",
				SSLMode:  "disable",
			},
			expectError: false,
		},
		{
			name: "missing host",
			config: &DatabaseConfig{
				Port:     5432,
				Name:     "test_db",
				User:     "test_user",
				Password: "test_pass",
			},
			expectError: true,
		},
		{
			name: "invalid port",
			config: &DatabaseConfig{
				Host:     "localhost",
				Port:     -1,
				Name:     "test_db",
				User:     "test_user",
				Password: "test_pass",
			},
			expectError: true,
		},
		{
			name: "missing database name",
			config: &DatabaseConfig{
				Host:     "localhost",
				Port:     5432,
				User:     "test_user",
				Password: "test_pass",
			},
			expectError: true,
		},
		{
			name: "missing user",
			config: &DatabaseConfig{
				Host:     "localhost",
				Port:     5432,
				Name:     "test_db",
				Password: "test_pass",
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateDatabaseConfig(tt.config)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

# Stage 1: Builder
FROM golang:1.22-alpine AS builder

WORKDIR /app

COPY go.mod go.sum ./
RUN go mod download

COPY . .

RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main ./cmd/api/server/main.go

FROM alpine:latest AS final
WORKDIR /app
COPY --from=builder /app/main .
COPY configs/config.yaml ./configs/config.yaml

EXPOSE 8080

CMD ["./main"]

COPY go.mod go.sum ./
RUN go mod download

COPY . .

RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main ./cmd/api/server/main.go

# Stage 2: Runner
FROM alpine:3.19.1@sha256:c5b1261d6d3e43071626931fc004f70149baeba2c8ec672bd4f27761f8e1ad6b

WORKDIR /app

COPY --from=builder /app/main .
COPY configs/config.yaml ./configs/config.yaml

EXPOSE 8080

CMD ["./main"]

package testing

import (
	"context"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"sort"
	"strings"

	"github.com/wongpinter/payment-gateway/internal/infrastructure/database"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/logger"
)

// SeedManager handles loading test seed data
type SeedManager struct {
	db      *database.DB
	seedDir string
}

// NewSeedManager creates a new seed manager
func NewSeedManager(db *database.DB, seedDir string) *SeedManager {
	return &SeedManager{
		db:      db,
		seedDir: seedDir,
	}
}

// LoadAllSeeds loads all seed files in the seed directory
func (sm *SeedManager) LoadAllSeeds(ctx context.Context) error {
	// Get all SQL files in seed directory
	seedFiles, err := sm.getSeedFiles()
	if err != nil {
		return fmt.Errorf("SeedManager.LoadAllSeeds: failed to get seed files: %w", err)
	}

	// Load seeds in order
	for _, file := range seedFiles {
		if err := sm.LoadSeedFile(ctx, file); err != nil {
			return fmt.Errorf("SeedManager.LoadAllSeeds: failed to load seed file %s: %w", file, err)
		}
	}

	logger.LogInfo(ctx, "All seed files loaded successfully", "count", len(seedFiles))
	return nil
}

// LoadSeedFile loads a specific seed file
func (sm *SeedManager) LoadSeedFile(ctx context.Context, filename string) error {
	filePath := filepath.Join(sm.seedDir, filename)

	// Read file content
	content, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("SeedManager.LoadSeedFile: failed to read file %s: %w", filePath, err)
	}

	// Execute SQL content
	if err := sm.executeSQLContent(ctx, string(content)); err != nil {
		return fmt.Errorf("SeedManager.LoadSeedFile: failed to execute SQL from %s: %w", filename, err)
	}

	logger.LogInfo(ctx, "Seed file loaded successfully", "file", filename)
	return nil
}

// LoadSpecificSeeds loads only specified seed files
func (sm *SeedManager) LoadSpecificSeeds(ctx context.Context, seedNames ...string) error {
	for _, seedName := range seedNames {
		filename := seedName
		if !strings.HasSuffix(filename, ".sql") {
			filename += ".sql"
		}

		if err := sm.LoadSeedFile(ctx, filename); err != nil {
			return fmt.Errorf("SeedManager.LoadSpecificSeeds: failed to load seed %s: %w", seedName, err)
		}
	}

	logger.LogInfo(ctx, "Specific seed files loaded successfully", "seeds", seedNames)
	return nil
}

// getSeedFiles returns all SQL files in the seed directory sorted by name
func (sm *SeedManager) getSeedFiles() ([]string, error) {
	var seedFiles []string

	err := filepath.WalkDir(sm.seedDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		if !d.IsDir() && strings.HasSuffix(strings.ToLower(d.Name()), ".sql") {
			// Get relative path from seed directory
			relPath, err := filepath.Rel(sm.seedDir, path)
			if err != nil {
				return err
			}
			seedFiles = append(seedFiles, relPath)
		}

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to walk seed directory: %w", err)
	}

	// Sort files to ensure consistent loading order
	sort.Strings(seedFiles)

	return seedFiles, nil
}

// executeSQLContent executes SQL content, handling multiple statements
func (sm *SeedManager) executeSQLContent(ctx context.Context, content string) error {
	// Split content by semicolons to handle multiple statements
	statements := sm.splitSQLStatements(content)

	for i, statement := range statements {
		statement = strings.TrimSpace(statement)
		if statement == "" {
			continue
		}

		// Skip comments
		if strings.HasPrefix(statement, "--") {
			continue
		}

		// Execute statement
		if _, err := sm.db.Pool.Exec(ctx, statement); err != nil {
			return fmt.Errorf("failed to execute statement %d: %w\nStatement: %s", i+1, err, statement)
		}
	}

	return nil
}

// splitSQLStatements splits SQL content into individual statements
func (sm *SeedManager) splitSQLStatements(content string) []string {
	// Simple split by semicolon - this could be enhanced for more complex SQL
	statements := strings.Split(content, ";")

	var result []string
	for _, stmt := range statements {
		stmt = strings.TrimSpace(stmt)
		if stmt != "" {
			result = append(result, stmt)
		}
	}

	return result
}

// ClearAllData removes all data from tables (but keeps schema)
func (sm *SeedManager) ClearAllData(ctx context.Context) error {
	// Get all table names except schema_migrations
	query := `
		SELECT tablename 
		FROM pg_tables 
		WHERE schemaname = 'public' 
		AND tablename != 'schema_migrations'
		ORDER BY tablename
	`

	rows, err := sm.db.Pool.Query(ctx, query)
	if err != nil {
		return fmt.Errorf("SeedManager.ClearAllData: failed to get table names: %w", err)
	}
	defer rows.Close()

	var tables []string
	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			return fmt.Errorf("SeedManager.ClearAllData: failed to scan table name: %w", err)
		}
		tables = append(tables, tableName)
	}

	// Disable foreign key checks temporarily
	if _, err := sm.db.Pool.Exec(ctx, "SET session_replication_role = replica"); err != nil {
		return fmt.Errorf("SeedManager.ClearAllData: failed to disable foreign key checks: %w", err)
	}

	// Truncate all tables
	for _, table := range tables {
		if _, err := sm.db.Pool.Exec(ctx, fmt.Sprintf("TRUNCATE TABLE %s RESTART IDENTITY", table)); err != nil {
			return fmt.Errorf("SeedManager.ClearAllData: failed to truncate table %s: %w", table, err)
		}
	}

	// Re-enable foreign key checks
	if _, err := sm.db.Pool.Exec(ctx, "SET session_replication_role = DEFAULT"); err != nil {
		return fmt.Errorf("SeedManager.ClearAllData: failed to re-enable foreign key checks: %w", err)
	}

	logger.LogInfo(ctx, "All table data cleared successfully", "tables_count", len(tables))
	return nil
}

// ReloadSeeds clears all data and reloads all seeds
func (sm *SeedManager) ReloadSeeds(ctx context.Context) error {
	if err := sm.ClearAllData(ctx); err != nil {
		return fmt.Errorf("SeedManager.ReloadSeeds: failed to clear data: %w", err)
	}

	if err := sm.LoadAllSeeds(ctx); err != nil {
		return fmt.Errorf("SeedManager.ReloadSeeds: failed to load seeds: %w", err)
	}

	return nil
}

// TestSeedSetup provides a complete seed setup for tests
type TestSeedSetup struct {
	SeedManager *SeedManager
	SeedDir     string
}

// NewTestSeedSetup creates a new test seed setup
func NewTestSeedSetup(db *database.DB, seedDir string) *TestSeedSetup {
	return &TestSeedSetup{
		SeedManager: NewSeedManager(db, seedDir),
		SeedDir:     seedDir,
	}
}

// LoadBasicSeeds loads the basic seed data needed for most tests
func (tss *TestSeedSetup) LoadBasicSeeds(ctx context.Context) error {
	// Load in dependency order: permissions first, then roles, then users
	basicSeeds := []string{"permissions", "roles", "users"}
	return tss.SeedManager.LoadSpecificSeeds(ctx, basicSeeds...)
}

// LoadAllSeeds loads all available seed data
func (tss *TestSeedSetup) LoadAllSeeds(ctx context.Context) error {
	return tss.SeedManager.LoadAllSeeds(ctx)
}

// Reset clears all data and reloads basic seeds
func (tss *TestSeedSetup) Reset(ctx context.Context) error {
	if err := tss.SeedManager.ClearAllData(ctx); err != nil {
		return err
	}
	return tss.LoadBasicSeeds(ctx)
}

// VerifySeeds checks that seed data was loaded correctly
func (tss *TestSeedSetup) VerifySeeds(ctx context.Context) error {
	checks := []struct {
		name  string
		query string
		min   int
	}{
		{"permissions", "SELECT COUNT(*) FROM permissions WHERE id::text LIKE '11111111-%'", 20},
		{"roles", "SELECT COUNT(*) FROM roles WHERE id::text LIKE '550e8400-%'", 5},
		{"users", "SELECT COUNT(*) FROM users WHERE id::text LIKE '770e8400-%'", 8},
		{"role_permissions", "SELECT COUNT(*) FROM role_permissions WHERE role_id::text LIKE '550e8400-%'", 10},
		{"user_roles", "SELECT COUNT(*) FROM user_roles WHERE user_id::text LIKE '770e8400-%'", 9},
	}

	for _, check := range checks {
		var count int
		if err := tss.SeedManager.db.Pool.QueryRow(ctx, check.query).Scan(&count); err != nil {
			return fmt.Errorf("failed to verify %s: %w", check.name, err)
		}

		if count < check.min {
			return fmt.Errorf("insufficient %s loaded: expected at least %d, got %d", check.name, check.min, count)
		}
	}

	return nil
}

// GetTestUserCredentials returns test user credentials for authentication tests
func (tss *TestSeedSetup) GetTestUserCredentials() map[string]TestUser {
	return map[string]TestUser{
		"superadmin": {
			ID:       "770e8400-e29b-41d4-a716-446655440001",
			Email:    "<EMAIL>",
			Password: "password123",
			Name:     "Super Admin User",
			Roles:    []string{"Super Administrator"},
		},
		"admin": {
			ID:       "770e8400-e29b-41d4-a716-446655440002",
			Email:    "<EMAIL>",
			Password: "password123",
			Name:     "Admin User",
			Roles:    []string{"Administrator"},
		},
		"usermanager": {
			ID:       "770e8400-e29b-41d4-a716-446655440003",
			Email:    "<EMAIL>",
			Password: "password123",
			Name:     "User Manager",
			Roles:    []string{"User Manager", "Viewer"},
		},
		"paymentmanager": {
			ID:       "770e8400-e29b-41d4-a716-446655440004",
			Email:    "<EMAIL>",
			Password: "password123",
			Name:     "Payment Manager",
			Roles:    []string{"Payment Manager", "Viewer"},
		},
		"viewer": {
			ID:       "770e8400-e29b-41d4-a716-446655440005",
			Email:    "<EMAIL>",
			Password: "password123",
			Name:     "Viewer User",
			Roles:    []string{"Viewer"},
		},
		"user": {
			ID:       "770e8400-e29b-41d4-a716-446655440006",
			Email:    "<EMAIL>",
			Password: "password123",
			Name:     "Regular User",
			Roles:    []string{"Regular User"},
		},
	}
}

// TestUser represents a test user with credentials
type TestUser struct {
	ID       string
	Email    string
	Password string
	Name     string
	Roles    []string
}

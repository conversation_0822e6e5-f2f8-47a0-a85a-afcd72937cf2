package account

import (
	"errors"
	"time"

	"github.com/google/uuid"
)

// User represents a user in the system
type User struct {
	id           UserID
	email        Email
	passwordHash PasswordHash
	name         string
	isActive     bool
	createdAt    time.Time
	updatedAt    time.Time
	roles        []Role
}

// UserID is a value object representing a user identifier
type UserID struct {
	value uuid.UUID
}

// NewUserID creates a new UserID
func NewUserID() UserID {
	return UserID{value: uuid.New()}
}

// UserIDFromString creates a UserID from a string
func UserIDFromString(s string) (UserID, error) {
	id, err := uuid.Parse(s)
	if err != nil {
		return UserID{}, errors.New("invalid user ID format")
	}
	return UserID{value: id}, nil
}

// String returns the string representation of UserID
func (u UserID) String() string {
	return u.value.String()
}

// UUID returns the underlying UUID
func (u UserID) UUID() uuid.UUID {
	return u.value
}

// Email is a value object representing an email address
type Email struct {
	value string
}

// NewEmail creates a new Email value object with validation
func NewEmail(email string) (Email, error) {
	if email == "" {
		return Email{}, errors.New("email cannot be empty")
	}

	// Basic email validation - in production, use a more robust validation
	if len(email) < 3 || !contains(email, "@") || !contains(email, ".") {
		return Email{}, errors.New("invalid email format")
	}

	return Email{value: email}, nil
}

// String returns the string representation of Email
func (e Email) String() string {
	return e.value
}

// PasswordHash is a value object representing a hashed password
type PasswordHash struct {
	value string
}

// NewPasswordHash creates a new PasswordHash
func NewPasswordHash(hash string) PasswordHash {
	return PasswordHash{value: hash}
}

// String returns the string representation of PasswordHash
func (p PasswordHash) String() string {
	return p.value
}

// NewUser creates a new User entity
func NewUser(email Email, passwordHash PasswordHash, name string) *User {
	now := time.Now()
	return &User{
		id:           NewUserID(),
		email:        email,
		passwordHash: passwordHash,
		name:         name,
		isActive:     true,
		createdAt:    now,
		updatedAt:    now,
		roles:        make([]Role, 0),
	}
}

// NewUserWithID creates a User entity with all fields specified (for repository use)
func NewUserWithID(id UserID, email Email, passwordHash PasswordHash, name string, isActive bool, createdAt, updatedAt time.Time) *User {
	return &User{
		id:           id,
		email:        email,
		passwordHash: passwordHash,
		name:         name,
		isActive:     isActive,
		createdAt:    createdAt,
		updatedAt:    updatedAt,
		roles:        make([]Role, 0),
	}
}

// ID returns the user's ID
func (u *User) ID() UserID {
	return u.id
}

// Email returns the user's email
func (u *User) Email() Email {
	return u.email
}

// PasswordHash returns the user's password hash
func (u *User) PasswordHash() PasswordHash {
	return u.passwordHash
}

// Name returns the user's name
func (u *User) Name() string {
	return u.name
}

// IsActive returns whether the user is active
func (u *User) IsActive() bool {
	return u.isActive
}

// CreatedAt returns when the user was created
func (u *User) CreatedAt() time.Time {
	return u.createdAt
}

// UpdatedAt returns when the user was last updated
func (u *User) UpdatedAt() time.Time {
	return u.updatedAt
}

// Roles returns the user's roles
func (u *User) Roles() []Role {
	return u.roles
}

// UpdatePassword updates the user's password hash
func (u *User) UpdatePassword(newPasswordHash PasswordHash) {
	u.passwordHash = newPasswordHash
	u.updatedAt = time.Now()
}

// UpdateEmail updates the user's email
func (u *User) UpdateEmail(newEmail Email) {
	u.email = newEmail
	u.updatedAt = time.Now()
}

// UpdateName updates the user's name
func (u *User) UpdateName(newName string) {
	u.name = newName
	u.updatedAt = time.Now()
}

// Deactivate deactivates the user
func (u *User) Deactivate() {
	u.isActive = false
	u.updatedAt = time.Now()
}

// Activate activates the user
func (u *User) Activate() {
	u.isActive = true
	u.updatedAt = time.Now()
}

// AddRole adds a role to the user
func (u *User) AddRole(role Role) {
	// Check if role already exists
	for _, existingRole := range u.roles {
		if existingRole.ID() == role.ID() {
			return // Role already exists
		}
	}
	u.roles = append(u.roles, role)
	u.updatedAt = time.Now()
}

// RemoveRole removes a role from the user
func (u *User) RemoveRole(roleID RoleID) {
	for i, role := range u.roles {
		if role.ID() == roleID {
			u.roles = append(u.roles[:i], u.roles[i+1:]...)
			u.updatedAt = time.Now()
			break
		}
	}
}

// HasRole checks if the user has a specific role
func (u *User) HasRole(roleID RoleID) bool {
	for _, role := range u.roles {
		if role.ID() == roleID {
			return true
		}
	}
	return false
}

// HasPermission checks if the user has a specific permission through their roles
func (u *User) HasPermission(permission Permission) bool {
	for _, role := range u.roles {
		if role.HasPermission(permission) {
			return true
		}
	}
	return false
}

// contains is a helper function to check if a string contains a substring
func contains(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

package middleware

import (
	"context"

	"github.com/wongpinter/payment-gateway/internal/infrastructure/logger"
)

// Context key types for type safety
type ContextKey string

const (
	// UserIDKey is the context key for user ID
	UserIDKey ContextKey = "user_id"
)

// GetTraceID extracts trace ID from context
func GetTraceID(ctx context.Context) string {
	if traceID, ok := ctx.Value(logger.TraceIDKey).(string); ok {
		return traceID
	}
	return ""
}

// GetCorrelationID extracts correlation ID from context
func GetCorrelationID(ctx context.Context) string {
	if correlationID, ok := ctx.Value(logger.CorrelationIDKey).(string); ok {
		return correlationID
	}
	return ""
}

// GetUserID extracts user ID from context
func GetUserID(ctx context.Context) string {
	if userID, ok := ctx.Value(UserIDKey).(string); ok {
		return userID
	}
	return ""
}

// WithTraceID adds trace ID to the context
func WithTraceID(ctx context.Context, traceID string) context.Context {
	return logger.WithTraceID(ctx, traceID)
}

// WithCorrelationID adds correlation ID to the context
func WithCorrelationID(ctx context.Context, correlationID string) context.Context {
	return logger.WithCorrelationID(ctx, correlationID)
}

// WithUserID adds user ID to the context
func WithUserID(ctx context.Context, userID string) context.Context {
	return context.WithValue(ctx, UserIDKey, userID)
}

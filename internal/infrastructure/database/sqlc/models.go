// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
)

type Permission struct {
	ID          uuid.UUID          `db:"id" json:"id"`
	Name        string             `db:"name" json:"name"`
	Description pgtype.Text        `db:"description" json:"description"`
	Resource    string             `db:"resource" json:"resource"`
	Action      string             `db:"action" json:"action"`
	IsActive    bool               `db:"is_active" json:"is_active"`
	CreatedAt   pgtype.Timestamptz `db:"created_at" json:"created_at"`
	UpdatedAt   pgtype.Timestamptz `db:"updated_at" json:"updated_at"`
	DeletedAt   pgtype.Timestamptz `db:"deleted_at" json:"deleted_at"`
}

type Role struct {
	ID          uuid.UUID          `db:"id" json:"id"`
	Name        string             `db:"name" json:"name"`
	Description pgtype.Text        `db:"description" json:"description"`
	CreatedAt   pgtype.Timestamptz `db:"created_at" json:"created_at"`
	UpdatedAt   pgtype.Timestamptz `db:"updated_at" json:"updated_at"`
	DeletedAt   pgtype.Timestamptz `db:"deleted_at" json:"deleted_at"`
	IsActive    bool               `db:"is_active" json:"is_active"`
}

type RolePermission struct {
	ID           uuid.UUID          `db:"id" json:"id"`
	RoleID       uuid.UUID          `db:"role_id" json:"role_id"`
	PermissionID uuid.UUID          `db:"permission_id" json:"permission_id"`
	CreatedAt    pgtype.Timestamptz `db:"created_at" json:"created_at"`
	UpdatedAt    pgtype.Timestamptz `db:"updated_at" json:"updated_at"`
	DeletedAt    pgtype.Timestamptz `db:"deleted_at" json:"deleted_at"`
}

type User struct {
	ID           uuid.UUID          `db:"id" json:"id"`
	Name         string             `db:"name" json:"name"`
	Email        string             `db:"email" json:"email"`
	PasswordHash string             `db:"password_hash" json:"password_hash"`
	LastLoginAt  pgtype.Int8        `db:"last_login_at" json:"last_login_at"`
	CreatedAt    pgtype.Timestamptz `db:"created_at" json:"created_at"`
	UpdatedAt    pgtype.Timestamptz `db:"updated_at" json:"updated_at"`
	DeletedAt    pgtype.Timestamptz `db:"deleted_at" json:"deleted_at"`
	IsActive     bool               `db:"is_active" json:"is_active"`
}

type UserCompany struct {
	ID        uuid.UUID          `db:"id" json:"id"`
	UserID    uuid.UUID          `db:"user_id" json:"user_id"`
	CompanyID uuid.UUID          `db:"company_id" json:"company_id"`
	CreatedAt pgtype.Timestamptz `db:"created_at" json:"created_at"`
	UpdatedAt pgtype.Timestamptz `db:"updated_at" json:"updated_at"`
	DeletedAt pgtype.Timestamptz `db:"deleted_at" json:"deleted_at"`
}

type UserRole struct {
	ID        uuid.UUID          `db:"id" json:"id"`
	UserID    uuid.UUID          `db:"user_id" json:"user_id"`
	RoleID    uuid.UUID          `db:"role_id" json:"role_id"`
	CreatedAt pgtype.Timestamptz `db:"created_at" json:"created_at"`
	UpdatedAt pgtype.Timestamptz `db:"updated_at" json:"updated_at"`
	DeletedAt pgtype.Timestamptz `db:"deleted_at" json:"deleted_at"`
}

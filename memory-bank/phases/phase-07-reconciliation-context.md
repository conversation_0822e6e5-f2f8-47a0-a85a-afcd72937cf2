# Phase 7: Reconciliation Bounded Context Implementation

## 1. Overview and Goals

This document outlines the seventh phase of refactoring the `payment-service`, focusing on the implementation of the `reconciliation` bounded context. This phase builds directly on the foundational infrastructure established in Phase 1 and the domain event mechanism from Phase 2.

**Goal for Phase 7:** To implement a secure, robust, and Clean Architecture-compliant `reconciliation` bounded context, responsible for matching internal transaction records with external financial data sources (e.g., bank statements, payment gateway settlement reports) to ensure financial accuracy.

**Objectives for Phase 7:**
1.  Implement core functionalities for importing external statements.
2.  Establish automated reconciliation algorithms (exact, heuristic, partial matching).
3.  Provide mechanisms for manual exception resolution.
4.  Ensure all implementations adhere to the defined coding guidelines and leverage the infrastructure from Phase 1 and domain events from Phase 2.
5.  Develop comprehensive unit, integration, and end-to-end tests for the `reconciliation` context.

**Scope for Phase 7:**
*   Updating database schemas for `reconciliation_batches`, `external_transactions`, `reconciliation_matches`, and `reconciliation_exceptions` to use UUIDs.
*   Implementing `ReconciliationService` for importing statements, running auto-reconciliation, and resolving exceptions.
*   Defining and implementing various matching algorithms.
*   Publishing `TransactionReconciledEvent` to the `billing` context.
*   Developing API endpoints for importing statements, triggering auto-reconciliation, and managing exceptions.
*   Creating initial unit, integration, and E2E tests for the `reconciliation` context.

**Out of Scope for Phase 7:**
*   Integration with external accounting systems.
*   Complex reporting beyond basic reconciliation summaries.
*   Automated discrepancy handling (e.g., write-offs) beyond marking exceptions.
*   Any other bounded contexts not directly related to reconciliation.

## 2. Architectural Principles (Recap from Phase 1, 2, 3, 4, 5 & 6)

This refactoring effort formally adopts and enforces **Clean Architecture** and **Domain-Driven Design (DDD)** principles to create clear boundaries between different layers of the application and promote scalability through Bounded Contexts.

### 2.1. The Dependency Rule (Clean Architecture)

Dependencies must only point inwards: `Infrastructure` -> `Application` -> `Domain`.
*   **Domain Layer**: Contains business entities and rules. It has zero dependencies on any outer layer.
*   **Application Layer**: Contains use cases that orchestrate the business logic. It defines interfaces that the Infrastructure layer implements.
*   **Infrastructure Layer**: Contains all external-facing components (database, payment providers, etc.). It depends on the Application layer.

```mermaid
graph TD
    subgraph "Domain Layer"
        direction LR
        D_Entities[Entities]
        D_Interfaces[Repository Interfaces]
    end

    subgraph "Application Layer"
        direction LR
        A_UseCases[Use Cases] --> D_Interfaces
        A_Interfaces[Provider Interfaces]
    end

    subgraph "Infrastructure Layer"
        direction LR
        I_Handlers[HTTP Handlers] --> A_UseCases
        I_Adapters[Provider Adapters] -- implements --> A_Interfaces
        I_Repos[Repository Implementations] -- implements --> D_Interfaces
    end

    I_Handlers --> A_UseCases
    A_UseCases --> D_Entities
    I_Repos --> D_Entities
    I_Adapters -- implements --> A_Interfaces
    A_UseCases -- depends on --> A_Interfaces
```

### 2.2. Bounded Contexts (Domain-Driven Design)

The `reconciliation` bounded context is the focus of this phase.

*   **`account`**: Manages identity and access control for internal system users (admins, support, etc.).
*   **`organization`**: Manages the external parties involved in transactions (Companies, Partners, Products).
*   **`billing`**: The core transactional context that handles the entire payment lifecycle (Transactions, TransactionItems, TransactionHistory).
*   **`configuration`**: Manages the "how" of payments, including providers and channels (PaymentProvider, PaymentChannel, ProviderChannelMapping).
*   **`notification`**: Responsible for all outgoing communications (e.g., webhooks, emails).
*   **`reconciliation`**: Responsible for matching internal transaction records with external financial data.

### 2.3. Presentation Layer: Ports & Adapters

The presentation layer will adopt a Ports and Adapters approach, living in `/internal/ports` and responsible for translating external requests into calls to the application layer.

*   **`/ports/rest/`**: Contains all HTTP handlers, separated by their "persona" or purpose.
    *   **`openapi/`**: For external client APIs.
    *   **`dashboard/`**: For the internal admin panel API.
    *   **`callback/`**: For incoming webhooks from third parties.
*   **Shared Use Cases**: All handlers will call the same use cases in the `/internal/app` layer, ensuring business logic is centralized and consistently applied.

### 2.4. Target Directory Structure

The new directory structure reflects the DDD approach and Clean Architecture principles. This phase will populate the `internal/app/reconciliation` and `internal/domain/reconciliation` directories.

```
/payment-service/
├── cmd/
│   └── api/
│       ├── server/                # Server setup, DI, and routing
│       │   ├── server.go
│       │   ├── dependencies.go
│       │   └── routes.go
│              └── main.go                # Lean application entry point
├── internal/
│   ├── app/                       # Application layer (use cases, services)
│   │   ├── account/
│   │   ├── organization/
│   │   ├── billing/
│   │   ├── configuration/
│   │   ├── notification/
│   │   ├── reconciliation/        # <-- Populated in this phase
│   │   └── ...
│   ├── domain/                    # Domain layer (entities, value objects, core business logic)
│   │   ├── account/
│   │   ├── organization/
│   │   ├── billing/
│   │   ├── configuration/
│   │   ├── notification/
│   │   ├── reconciliation/        # <-- Populated in this phase
│   │   └── ...
│   ├── ports/                     # Presentation Layer (Handlers)
│   │   └── rest/
│   │       ├── openapi/
│   │       ├── dashboard/
│   │       ├── callback/
│   │       └── ...
│   └── infrastructure/            # Infrastructure layer (external concerns)
│       ├── database/              # PostgreSQL repository implementations
│       ├── provider/              # Adapters for the gateway packages
│       ├── cache/                 # Redis implementation
│       ├── eventbus/              # Domain Event Bus implementation
│       └── locking/               # Distributed locking (e.g., Redis-based)
├── pkg/
│   ├── gateways/                  # External, reusable gateway packages
│   │   ├── xendit/
│   │   │   ├── client.go
│   │   │   ├── go.mod
│   │   └── nicepay/
│   │       ├── client.go
│   │       └── go.mod
│   └── ... (other shared libraries)
├── api/                           # API definitions (e.g., OpenAPI/Swagger specs)
├── db/
│   └── migrations/                # Database migration .sql files
├── docs/                          # Project documentation
└── scripts/                       # Scripts for build, deployment, etc.
```

## 3. Key Architectural Decisions (Recap from Phase 1, 2, 3, 4, 5 & 6)

### 3.1. Manual Dependency Injection

To maintain full control over the application's startup process and avoid external dependencies, **manual dependency injection** will be used. All dependencies will be constructed and "wired" together in the `/cmd/api/server` package.

### 3.2. Externalized Gateway Packages

All third-party payment provider integrations (e.g., Xendit, NicePay) will be built as **standalone, reusable Go packages** located in `/pkg/gateways`.

### 3.3. Internationalization (i18n)

The application will support multiple languages, starting with English (en) and Indonesian (id). This will impact user-facing strings in API responses, validation messages, and notifications.

### 3.4. Domain Event Mechanism

The in-process domain event mechanism established in Phase 2 will be utilized for inter-context communication. The `reconciliation` context will **publish** `TransactionReconciledEvent` to the `billing` context and may **subscribe** to events from `billing` (e.g., `CashInPaidEvent`, `CashOutDisbursedEvent`) to initiate reconciliation processes.

### 3.5. Centralized Fee Calculation

A dedicated fee calculation service will reside within the `billing` bounded context.

### 3.6. Event Sourcing for Transaction Status

Transaction status management will leverage an Event Sourcing pattern, as detailed in the comprehensive [`docs/event_sourcing_strategy.md`](docs/event_sourcing_strategy.md) document.

### 3.7. Reliable Third-Party Notifications (Outbox Pattern)

The Outbox pattern, implemented in Phase 5 within the `billing` context, will ensure that events triggering notifications are reliably stored. The `notification` context will then be responsible for processing these `outbox` entries and sending the actual notifications.

## 4. Standardized Libraries (Recap from Phase 1)

The following libraries will be standardized across the project:

*   **Database Driver:** `pgx/v5`
*   **SQL Code Generator:** `sqlc`
*   **HTTP Framework:** `Gin`
*   **Logger:** `Zerolog`
*   **Configuration:** `Koanf`
*   **HTTP Client:** `Resty v2`
*   **Message Broker:** `go-rabbitmq`
*   **Integration Testing:** `testcontainers-go`
*   **Database Migrations:** `golang-migrate/migrate`
*   **Error Reporting:** `sentry-go`
*   **Internationalization:** `go-i18n`

## 5. Coding Conventions & Guidelines (Recap from Phase 1)

Adherence to these guidelines is mandatory for all contributors to ensure a high-quality, consistent, and maintainable codebase.

### 5.1. General Principles & Idiomatic Go
*   **Rule 1.1 (Simplicity - KISS):** Prefer simple, clear solutions over complex ones.
*   **Rule 1.2 (Effective Go):** Follow the principles outlined in the official [Effective Go](https://go.dev/doc/effective_go) document.
*   **Rule 1.3 (Package Naming):** All package names MUST be short-lowercase.
*   **Rule 1.4 (Variable Naming):** All variable names MUST be `camelCase`.
*   **Rule 1.5 (Interfaces):** Define interfaces to decouple components.
*   **Rule 1.6 (Formatting):** All generated Go code MUST be formatted with `gofmt`.

### 5.2. Project Structure & Clean Architecture
*   **Rule 2.1 (Strict Layering):** The dependency direction `API` -> `Application` -> `Domain` MUST be strictly enforced.
*   **Rule 2.2 (Domain Purity):** The `internal/domain` package MUST NOT import any other project packages.
*   **Rule 2.3 (Thin Handlers):** API handlers (`internal/ports/rest`) MUST only parse requests, call a single application service method, and generate a standardized response.

### 5.3. Error Management Protocol
*   **Rule 3.1 (Error Wrapping):** Errors MUST be wrapped with context using `fmt.Errorf("package.function: %w", err)`.
*   **Rule 3.2 (Custom Domain Errors):** Use custom error variables for predictable business rule failures.
*   **Rule 3.3 (Centralized HTTP Error Mapping):** A dedicated Gin error-handling middleware is the only place where errors are translated into HTTP status codes.
*   **Rule 3.4 (Sentry for Unexpected Errors):** Use `webapi.ErrorWithSentry(c, err)` for all 5xx internal server errors.

### 5.4. Standardized API Responses (webapi package)
*   **Rule 4.1 (Mandatory Helpers):** All HTTP responses MUST be generated exclusively using helper functions from the `internal/ports/rest/webapi` package.
*   **Rule 4.2 (Success Structure):** The mandatory JSON structure for a successful response is: `{"status": "success", "data": { /* Actual response data */ }, "correlationId": "UUID_OF_REQUEST"}`.
*   **Rule 4.3 (Error Structure):** The mandatory JSON structure for an error response is: `{"status": "error", "error": {"code": "UNIQUE_ERROR_CODE", "message": "Human-readable error description."}, "correlationId": "UUID_OF_REQUEST"}`.
*   **Rule 4.4 (Helper Signatures and Usage):** Adhere to the defined signatures for `webapi.Success`, `webapi.SuccessPaginated`, `webapi.Error`, and `webapi.ErrorWithSentry`.

### 5.5. Logging Best Practices (zerolog)
*   **Rule 5.1 (Structured Logging):** All logging MUST be done using the global `zerolog` instance.
*   **Rule 5.2 (Contextual Logging):** Every log statement MUST include the `trace_id` from the context.
*   **Rule 5.3 (Error Logging):** When logging an error, the error object MUST be included using `.Err(err)`.

### 5.6. Concurrency and Graceful Shutdown
*   **Rule 6.1 (Panic Safety):** Any new goroutine MUST use a `defer...recover()` block, logging any recovered panic to Sentry.
*   **Rule 6.2 (Graceful Termination):** Any long-running goroutine MUST accept a `context.Context` and listen on `ctx.Done()`.
*   **Rule 6.3 (Server Shutdown):** `main.go` must implement graceful shutdown for the HTTP server.

### 5.7. Gin Middleware
*   **Rule 7.1 (Tracing Middleware):** MUST be the first middleware, generating a `trace_id`.
*   **Rule 7.2 (Logger Middleware):** MUST log request and response details.
*   **Rule 7.3 (Error Middleware):** MUST be the last middleware to centralize error handling.

### 5.8. Database Access (sqlc with pgx/v5)
*   **Rule 8.1 (SQL Files):** All SQL queries MUST be defined in `.sql` files.
*   **Rule 8.2 (sqlc.yaml Configuration):** The `sqlc.yaml` file MUST be configured to use `sql_package: "pgx/v5"` and appropriate type overrides.
*   **Rule 8.3 (Type Conversion):** All conversions between domain types and `pgtype` MUST be handled in a dedicated `internal/infrastructure/database/converters` package.
*   **Rule 8.4 (Transactions):** Repository methods that perform writes MUST accept `pgx.Tx` as a parameter.

### 5.9. Testing and CI/CD Strategy
*   **Rule 9.1 (Containerized Testing):** All integration and end-to-end tests that require external services (like a database or Redis) MUST use `testcontainers-go`.
*   **Rule 9.2 (Seeding Data for Tests):** For every new `.up.sql` migration file, a corresponding test data seed file MUST be created.
*   **Rule 9.3 (Test Coverage):** API handlers, application services, and repositories MUST have dedicated test files.
*   **Rule 9.4 (CI/CD Gate):** A pull request MUST NOT be allowed to merge into the main branch unless all tests (`go test ./...`) pass.

### 5.10. Internationalization (i18n)
*   **Rule 10.1 (Localization Support):** The application MUST support English (en) and Indonesian (id) locales.

## 6. Database Schemas for Phase 7

This phase will introduce and refine the `reconciliation` related schemas.

### 6.1. Reconciliation Schemas (`reconciliation` Bounded Context)

#### `reconciliation_batches` Table:
```sql
CREATE TABLE reconciliation_batches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    batch_name VARCHAR(255),
    source VARCHAR(50) NOT NULL, -- e.g., 'BANK_STATEMENT', 'PG_SETTLEMENT'
    status VARCHAR(50) NOT NULL, -- 'PENDING', 'PROCESSING', 'COMPLETED', 'FAILED'
    imported_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP WITH TIME ZONE
);
```

#### `external_transactions` Table:
```sql
CREATE TABLE external_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    batch_id UUID NOT NULL REFERENCES reconciliation_batches(id),
    transaction_id_external VARCHAR(255),
    amount DECIMAL(18, 2) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    transaction_date DATE NOT NULL,
    description TEXT,
    status VARCHAR(50) NOT NULL, -- 'UNMATCHED', 'MATCHED', 'PARTIAL_MATCH'
    matched_internal_transaction_id UUID, -- FK to cash_in_transactions or cash_out_transactions
    raw_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### `reconciliation_matches` Table:
```sql
CREATE TABLE reconciliation_matches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    external_transaction_id UUID NOT NULL REFERENCES external_transactions(id),
    internal_transaction_id UUID NOT NULL, -- FK to cash_in_transactions or cash_out_transactions
    match_type VARCHAR(50) NOT NULL, -- 'EXACT', 'HEURISTIC', 'PARTIAL'
    matched_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    matched_by UUID -- User ID or System ID
);
```

#### `reconciliation_exceptions` Table:
```sql
CREATE TABLE reconciliation_exceptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    external_transaction_id UUID NOT NULL REFERENCES external_transactions(id),
    reason TEXT,
    status VARCHAR(50) NOT NULL, -- 'OPEN', 'RESOLVED'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by UUID -- User ID or System ID
);
```

## 7. Detailed Tasks for Phase 7

### 7.1. Reconciliation Domain & Repository Implementation
*   **Task 7.1.1: Define Reconciliation Domain Entities**
    *   Create `internal/domain/reconciliation/reconciliation.go`.
    *   Define structs for `ReconciliationBatch`, `ExternalTransaction`, `ReconciliationMatch`, `ReconciliationException`.
*   **Task 7.1.2: Update Database Migrations**
    *   Create a new migration `db/migrations/000007_reconciliation_tables.up.sql` to define all `reconciliation` related tables. Ensure UUIDs are used for primary keys.
    *   Create corresponding `000007_reconciliation_tables.down.sql`.
*   **Task 7.1.3: Implement Reconciliation Repositories**
    *   Create `internal/infrastructure/database/reconciliation/reconciliation_repository.go`.
    *   Implement repository interfaces for all `reconciliation` entities using `sqlc`-generated code.
    *   Ensure all `pgtype` conversions are handled by `internal/infrastructure/database/converters`.

### 7.2. Reconciliation Application Use Cases
*   **Task 7.2.1: Implement Reconciliation Service**
    *   Create `internal/app/reconciliation/reconciliation_service.go`.
    *   Implement `ImportExternalStatement(ctx context.Context, fileData []byte, source string)`: Parses external file, creates `reconciliation_batch`, populates `external_transactions`.
    *   Implement `RunAutoReconciliation(ctx context.Context, batchID UUID)`:
        *   Iterates through `external_transactions` with `status: UNMATCHED`.
        *   Queries `billing` context (via interface) for internal transactions.
        *   Applies matching algorithms (exact, heuristic, partial).
        *   Creates `reconciliation_match` or `reconciliation_exception` records.
        *   Publishes `TransactionReconciledEvent` to `billing` context using `EventPublisher`.
    *   Implement `ResolveException(ctx context.Context, exceptionID UUID, internalTransactionID UUID, resolvedBy UUID)`: Allows manual resolution of exceptions.
    *   Implement `GetReconciliationSummary(ctx context.Context, batchID UUID)`: Provides statistics.
*   **Task 7.2.2: Define Matching Algorithms**
    *   Create `internal/app/reconciliation/matching_algorithms.go`.
    *   Implement functions for `ExactMatch`, `HeuristicMatch`, `PartialMatch`.

### 7.3. Reconciliation Presentation Layer (API)
*   **Task 7.3.1: Implement Reconciliation Handlers**
    *   Create `internal/ports/rest/dashboard/reconciliation_handler.go`.
    *   Implement handlers for `POST /reconciliation/import`, `POST /reconciliation/batches/{id}/run-auto`, `GET /reconciliation/batches/{id}/summary`, `GET /reconciliation/exceptions`, `POST /reconciliation/exceptions/{id}/resolve`.
*   **Task 7.3.2: Update Main Application Wiring**
    *   Modify `cmd/api/server/dependencies.go` to wire up the new `Reconciliation` repositories and services.
    *   Modify `cmd/api/server/routes.go` to define the new `reconciliation` endpoints, ensuring proper authentication and authorization middleware is applied.
    *   Register `reconciliation` event consumers (if any) with the `InMemoryEventBus`.

### 7.4. Testing and Validation
*   **Task 7.4.1: Update Seed Data**
    *   Create `testdata/seeds/000007_seed_reconciliation_data.sql` with sample data for all `reconciliation` tables.
*   **Task 7.4.2: Implement Unit Tests**
    *   Write unit tests for `internal/app/reconciliation` services (mocking repositories, `EventPublisher`, and `billing` context interfaces).
    *   Write unit tests for matching algorithms.
*   **Task 7.4.3: Implement Integration Tests**
    *   Write integration tests for `internal/infrastructure/database/reconciliation` repositories using `testcontainers-go` for PostgreSQL.
    *   Write integration tests for `internal/app/reconciliation` services using `testcontainers-go` for PostgreSQL and mocked external services.
*   **Task 7.4.4: Implement End-to-End Tests**
    *   Write E2E tests for `reconciliation` API endpoints using `SetupTestServer()` from Phase 1.
    *   Test importing statements, running auto-reconciliation, and resolving exceptions.
    *   Verify that `TransactionReconciledEvent` is published to the `billing` context.
*   **Task 7.4.5: Update Postman Collection**
    *   Create `docs/postman/collections/reconciliation.postman_collection.json` with requests for all new `reconciliation` endpoints.

## 8. Phase 7 Deliverables

*   Fully implemented `reconciliation` bounded context with functionalities for importing external statements, auto-reconciliation, and exception management.
*   Updated database schemas for `reconciliation_batches`, `external_transactions`, `reconciliation_matches`, and `reconciliation_exceptions`.
*   Working matching algorithms.
*   Publishing of `TransactionReconciledEvent` to the `billing` context.
*   Comprehensive unit, integration, and E2E tests for the `reconciliation` context.
*   Updated Postman collection for `reconciliation` endpoints.
*   All code adhering to the defined coding guidelines.

## 9. Phase 7 Diagram:

```mermaid
graph TD
    subgraph "Phase 7: Reconciliation Bounded Context Implementation"
        A[7.1. Reconciliation Domain & Repository] --> B(7.2. Reconciliation Application Use Cases)
        B --> C(7.3. Reconciliation Presentation Layer)
        C --> D(7.4. Testing and Validation)
        A --> D
        B --> D
        C --> D
    end

    subgraph "7.1. Reconciliation Domain & Repository"
        A1[7.1.1: Define Reconciliation Domain Entities]
        A2[7.1.2: Update Database Migrations]
        A3[7.1.3: Implement Reconciliation Repositories]
    end

    subgraph "7.2. Reconciliation Application Use Cases"
        B1[7.2.1: Implement Reconciliation Service]
        B2[7.2.2: Define Matching Algorithms]
    end

    subgraph "7.3. Reconciliation Presentation Layer (API)"
        C1[7.3.1: Implement Reconciliation Handlers]
        C2[7.3.2: Update Main Application Wiring]
    end

    subgraph "7.4. Testing and Validation"
        D1[7.4.1: Update Seed Data]
        D2[7.4.2: Implement Unit Tests]
        D3[7.4.3: Implement Integration Tests]
        D4[7.4.4: Implement End-to-End Tests]
        D5[7.4.5: Update Postman Collection]
    end

    style A1 fill:#f9f,stroke:#333,stroke-width:2px
    style A2 fill:#f9f,stroke:#333,stroke-width:2px
    style A3 fill:#f9f,stroke:#333,stroke-width:2px

    style B1 fill:#bbf,stroke:#333,stroke-width:2px
    style B2 fill:#bbf,stroke:#333,stroke-width:2px

    style C1 fill:#ccf,stroke:#333,stroke-width:2px
    style C2 fill:#ccf,stroke:#333,stroke-width:2px

    style D1 fill:#ddf,stroke:#333,stroke-width:2px
    style D2 fill:#ddf,stroke:#333,stroke-width:2px
    style D3 fill:#ddf,stroke:#333,stroke-width:2px
    style D4 fill:#ddf,stroke:#333,stroke-width:2px
    style D5 fill:#ddf,stroke:#333,stroke-width:2px
package security

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/wongpinter/payment-gateway/internal/domain/account"
)

func TestJWTManager_GenerateAccessToken(t *testing.T) {
	secretKey := "test-secret-key-for-jwt-testing-purposes"
	manager := NewJWTManager(secretKey, time.Hour, time.Hour*24*7)

	t.Run("successful token generation", func(t *testing.T) {
		userID := account.NewUserID()

		token, err := manager.GenerateAccessToken(userID.String())

		require.NoError(t, err)
		assert.NotEmpty(t, token)
		assert.Contains(t, token, ".")
	})

	t.Run("empty user ID", func(t *testing.T) {
		token, err := manager.GenerateAccessToken("")

		require.NoError(t, err)
		assert.NotEmpty(t, token)
	})

	t.Run("different user IDs produce different tokens", func(t *testing.T) {
		userID1 := account.NewUserID().String()
		userID2 := account.NewUserID().String()

		token1, err1 := manager.GenerateAccessToken(userID1)
		token2, err2 := manager.GenerateAccessToken(userID2)

		require.NoError(t, err1)
		require.NoError(t, err2)
		assert.NotEqual(t, token1, token2)
	})

	t.Run("same user ID produces different tokens due to timestamp", func(t *testing.T) {
		userID := account.NewUserID().String()

		token1, err1 := manager.GenerateAccessToken(userID)
		time.Sleep(time.Millisecond) // Ensure different timestamp
		token2, err2 := manager.GenerateAccessToken(userID)

		require.NoError(t, err1)
		require.NoError(t, err2)
		assert.NotEqual(t, token1, token2)
	})
}

func TestJWTManager_GenerateRefreshToken(t *testing.T) {
	secretKey := "test-secret-key-for-jwt-testing-purposes"
	manager := NewJWTManager(secretKey, time.Hour, time.Hour*24*7)

	t.Run("successful refresh token generation", func(t *testing.T) {
		userID := account.NewUserID()

		token, err := manager.GenerateRefreshToken(userID.String())

		require.NoError(t, err)
		assert.NotEmpty(t, token)
		assert.Contains(t, token, ".")
	})

	t.Run("empty user ID", func(t *testing.T) {
		token, err := manager.GenerateRefreshToken("")

		require.NoError(t, err)
		assert.NotEmpty(t, token)
	})

	t.Run("different user IDs produce different refresh tokens", func(t *testing.T) {
		userID1 := account.NewUserID().String()
		userID2 := account.NewUserID().String()

		token1, err1 := manager.GenerateRefreshToken(userID1)
		token2, err2 := manager.GenerateRefreshToken(userID2)

		require.NoError(t, err1)
		require.NoError(t, err2)
		assert.NotEqual(t, token1, token2)
	})
}

func TestJWTManager_ValidateAccessToken(t *testing.T) {
	secretKey := "test-secret-key-for-jwt-testing-purposes"
	manager := NewJWTManager(secretKey, time.Hour, time.Hour*24*7)

	t.Run("successful token validation", func(t *testing.T) {
		userID := account.NewUserID().String()

		token, err := manager.GenerateAccessToken(userID)
		require.NoError(t, err)

		claims, err := manager.ValidateAccessToken(token)

		require.NoError(t, err)
		assert.Equal(t, userID, claims.UserID)
		assert.Equal(t, "access", claims.Type)
		assert.True(t, claims.ExpiresAt.After(time.Now()))
	})

	t.Run("invalid token format", func(t *testing.T) {
		invalidToken := "invalid.token.format"

		claims, err := manager.ValidateAccessToken(invalidToken)

		assert.Error(t, err)
		assert.Nil(t, claims)
	})

	t.Run("empty token", func(t *testing.T) {
		claims, err := manager.ValidateAccessToken("")

		assert.Error(t, err)
		assert.Nil(t, claims)
	})

	t.Run("token with wrong secret", func(t *testing.T) {
		wrongManager := NewJWTManager("wrong-secret", time.Hour, time.Hour*24*7)
		userID := account.NewUserID().String()

		token, err := wrongManager.GenerateAccessToken(userID)
		require.NoError(t, err)

		claims, err := manager.ValidateAccessToken(token)

		assert.Error(t, err)
		assert.Nil(t, claims)
	})

	t.Run("refresh token used as access token", func(t *testing.T) {
		userID := account.NewUserID().String()

		refreshToken, err := manager.GenerateRefreshToken(userID)
		require.NoError(t, err)

		claims, err := manager.ValidateAccessToken(refreshToken)

		assert.Error(t, err)
		assert.Nil(t, claims)
	})
}

func TestJWTManager_ValidateRefreshToken(t *testing.T) {
	secretKey := "test-secret-key-for-jwt-testing-purposes"
	manager := NewJWTManager(secretKey, time.Hour, time.Hour*24*7)

	t.Run("successful refresh token validation", func(t *testing.T) {
		userID := account.NewUserID().String()

		token, err := manager.GenerateRefreshToken(userID)
		require.NoError(t, err)

		claims, err := manager.ValidateRefreshToken(token)

		require.NoError(t, err)
		assert.Equal(t, userID, claims.UserID)
		assert.Equal(t, "refresh", claims.Type)
		assert.True(t, claims.ExpiresAt.After(time.Now()))
	})

	t.Run("invalid refresh token format", func(t *testing.T) {
		invalidToken := "invalid.refresh.token"

		claims, err := manager.ValidateRefreshToken(invalidToken)

		assert.Error(t, err)
		assert.Nil(t, claims)
	})

	t.Run("access token used as refresh token", func(t *testing.T) {
		userID := account.NewUserID().String()

		accessToken, err := manager.GenerateAccessToken(userID)
		require.NoError(t, err)

		claims, err := manager.ValidateRefreshToken(accessToken)

		assert.Error(t, err)
		assert.Nil(t, claims)
	})
}

func TestJWTManager_TokenExpiration(t *testing.T) {
	secretKey := "test-secret-key-for-jwt-testing-purposes"
	shortExpiry := time.Millisecond * 100
	manager := NewJWTManager(secretKey, shortExpiry, shortExpiry)

	t.Run("expired access token", func(t *testing.T) {
		userID := account.NewUserID().String()

		token, err := manager.GenerateAccessToken(userID)
		require.NoError(t, err)

		// Wait for token to expire
		time.Sleep(shortExpiry + time.Millisecond*50)

		claims, err := manager.ValidateAccessToken(token)

		assert.Error(t, err)
		assert.Nil(t, claims)
	})

	t.Run("expired refresh token", func(t *testing.T) {
		userID := account.NewUserID().String()

		token, err := manager.GenerateRefreshToken(userID)
		require.NoError(t, err)

		// Wait for token to expire
		time.Sleep(shortExpiry + time.Millisecond*50)

		claims, err := manager.ValidateRefreshToken(token)

		assert.Error(t, err)
		assert.Nil(t, claims)
	})
}

func TestJWTManager_TokenRoundTrip(t *testing.T) {
	secretKey := "test-secret-key-for-jwt-testing-purposes"
	manager := NewJWTManager(secretKey, time.Hour, time.Hour*24*7)

	testCases := []struct {
		name   string
		userID string
	}{
		{"normal UUID", account.NewUserID().String()},
		{"empty user ID", ""},
		{"special characters", "<EMAIL>"},
		{"numeric ID", "12345"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Test access token round trip
			accessToken, err := manager.GenerateAccessToken(tc.userID)
			require.NoError(t, err)

			accessClaims, err := manager.ValidateAccessToken(accessToken)
			require.NoError(t, err)
			assert.Equal(t, tc.userID, accessClaims.UserID)
			assert.Equal(t, "access", accessClaims.Type)

			// Test refresh token round trip
			refreshToken, err := manager.GenerateRefreshToken(tc.userID)
			require.NoError(t, err)

			refreshClaims, err := manager.ValidateRefreshToken(refreshToken)
			require.NoError(t, err)
			assert.Equal(t, tc.userID, refreshClaims.UserID)
			assert.Equal(t, "refresh", refreshClaims.Type)
		})
	}
}

func TestJWTManager_Configuration(t *testing.T) {
	t.Run("different secret keys produce incompatible tokens", func(t *testing.T) {
		manager1 := NewJWTManager("secret1", time.Hour, time.Hour*24*7)
		manager2 := NewJWTManager("secret2", time.Hour, time.Hour*24*7)

		userID := account.NewUserID().String()

		token, err := manager1.GenerateAccessToken(userID)
		require.NoError(t, err)

		claims, err := manager2.ValidateAccessToken(token)

		assert.Error(t, err)
		assert.Nil(t, claims)
	})

	t.Run("different expiry times work correctly", func(t *testing.T) {
		shortManager := NewJWTManager("secret", time.Minute, time.Hour)
		longManager := NewJWTManager("secret", time.Hour*24, time.Hour*24*30)

		userID := account.NewUserID().String()

		shortToken, err := shortManager.GenerateAccessToken(userID)
		require.NoError(t, err)

		longToken, err := longManager.GenerateAccessToken(userID)
		require.NoError(t, err)

		// Both should validate with their respective managers
		shortClaims, err := shortManager.ValidateAccessToken(shortToken)
		require.NoError(t, err)
		assert.Equal(t, userID, shortClaims.UserID)

		longClaims, err := longManager.ValidateAccessToken(longToken)
		require.NoError(t, err)
		assert.Equal(t, userID, longClaims.UserID)

		// Cross-validation should also work (same secret)
		crossClaims, err := longManager.ValidateAccessToken(shortToken)
		require.NoError(t, err)
		assert.Equal(t, userID, crossClaims.UserID)
	})
}

package logger

import (
	"context"
	"fmt"
	"io"
	"os"
	"strings"
	"time"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/config"
)

// ContextKey is the type for context keys used in logging
type ContextKey string

const (
	// TraceIDKey is the context key for trace ID
	TraceIDKey ContextKey = "trace_id"
	// UserIDKey is the context key for user ID
	UserIDKey ContextKey = "user_id"
	// CorrelationIDKey is the context key for correlation ID
	CorrelationIDKey ContextKey = "correlation_id"
)

// Init initializes the global zerolog logger based on configuration
// This function adheres to Rule 5.1 (Structured Logging)
func Init(cfg *config.LoggerConfig) error {
	// Set global log level
	level, err := parseLogLevel(cfg.Level)
	if err != nil {
		return fmt.Errorf("logger.Init: invalid log level: %w", err)
	}
	zerolog.SetGlobalLevel(level)

	// Configure output writer based on format
	var writer io.Writer
	switch cfg.Format {
	case "console":
		// Console output with colors for development
		writer = zerolog.ConsoleWriter{
			Out:        os.Stdout,
			TimeFormat: time.RFC3339,
			NoColor:    !cfg.Color,
		}
	case "json":
		// JSON output for production
		writer = os.Stdout
	default:
		return fmt.Errorf("logger.Init: unsupported log format: %s", cfg.Format)
	}

	// Configure global logger
	logger := zerolog.New(writer)

	// Add timestamp if enabled
	if cfg.Timestamp {
		logger = logger.With().Timestamp().Logger()
	}

	// Add caller information if enabled
	if cfg.Caller {
		logger = logger.With().Caller().Logger()
	}

	// Set as global logger
	log.Logger = logger

	return nil
}

// parseLogLevel converts string log level to zerolog.Level
func parseLogLevel(level string) (zerolog.Level, error) {
	switch strings.ToLower(level) {
	case "trace":
		return zerolog.TraceLevel, nil
	case "debug":
		return zerolog.DebugLevel, nil
	case "info":
		return zerolog.InfoLevel, nil
	case "warn", "warning":
		return zerolog.WarnLevel, nil
	case "error":
		return zerolog.ErrorLevel, nil
	case "fatal":
		return zerolog.FatalLevel, nil
	case "panic":
		return zerolog.PanicLevel, nil
	default:
		return zerolog.InfoLevel, fmt.Errorf("unknown log level: %s", level)
	}
}

// WithTraceID adds trace ID to the logger context
// This function adheres to Rule 5.2 (Contextual Logging)
func WithTraceID(ctx context.Context, traceID string) context.Context {
	return context.WithValue(ctx, TraceIDKey, traceID)
}

// WithUserID adds user ID to the logger context
func WithUserID(ctx context.Context, userID string) context.Context {
	return context.WithValue(ctx, UserIDKey, userID)
}

// WithCorrelationID adds correlation ID to the logger context
func WithCorrelationID(ctx context.Context, correlationID string) context.Context {
	return context.WithValue(ctx, CorrelationIDKey, correlationID)
}

// FromContext creates a logger with context values
// This function adheres to Rule 5.2 (Contextual Logging)
func FromContext(ctx context.Context) *zerolog.Logger {
	logger := log.With()

	// Add trace ID if present
	if traceID, ok := ctx.Value(TraceIDKey).(string); ok && traceID != "" {
		logger = logger.Str("trace_id", traceID)
	}

	// Add user ID if present
	if userID, ok := ctx.Value(UserIDKey).(string); ok && userID != "" {
		logger = logger.Str("user_id", userID)
	}

	// Add correlation ID if present
	if correlationID, ok := ctx.Value(CorrelationIDKey).(string); ok && correlationID != "" {
		logger = logger.Str("correlation_id", correlationID)
	}

	l := logger.Logger()
	return &l
}

// LogError logs an error with context
// This function adheres to Rule 5.3 (Error Logging)
func LogError(ctx context.Context, err error, message string, fields ...interface{}) {
	logger := FromContext(ctx)
	event := logger.Error().Err(err)

	// Add additional fields in pairs
	for i := 0; i < len(fields)-1; i += 2 {
		if key, ok := fields[i].(string); ok {
			event = event.Interface(key, fields[i+1])
		}
	}

	event.Msg(message)
}

// LogInfo logs an info message with context
func LogInfo(ctx context.Context, message string, fields ...interface{}) {
	logger := FromContext(ctx)
	event := logger.Info()

	// Add additional fields in pairs
	for i := 0; i < len(fields)-1; i += 2 {
		if key, ok := fields[i].(string); ok {
			event = event.Interface(key, fields[i+1])
		}
	}

	event.Msg(message)
}

// LogWarn logs a warning message with context
func LogWarn(ctx context.Context, message string, fields ...interface{}) {
	logger := FromContext(ctx)
	event := logger.Warn()

	// Add additional fields in pairs
	for i := 0; i < len(fields)-1; i += 2 {
		if key, ok := fields[i].(string); ok {
			event = event.Interface(key, fields[i+1])
		}
	}

	event.Msg(message)
}

// LogDebug logs a debug message with context
func LogDebug(ctx context.Context, message string, fields ...interface{}) {
	logger := FromContext(ctx)
	event := logger.Debug()

	// Add additional fields in pairs
	for i := 0; i < len(fields)-1; i += 2 {
		if key, ok := fields[i].(string); ok {
			event = event.Interface(key, fields[i+1])
		}
	}

	event.Msg(message)
}

// LogTrace logs a trace message with context
func LogTrace(ctx context.Context, message string, fields ...interface{}) {
	logger := FromContext(ctx)
	event := logger.Trace()

	// Add additional fields in pairs
	for i := 0; i < len(fields)-1; i += 2 {
		if key, ok := fields[i].(string); ok {
			event = event.Interface(key, fields[i+1])
		}
	}

	event.Msg(message)
}

// LogFatal logs a fatal message with context and exits
func LogFatal(ctx context.Context, message string, fields ...interface{}) {
	logger := FromContext(ctx)
	event := logger.Fatal()

	// Add additional fields in pairs
	for i := 0; i < len(fields)-1; i += 2 {
		if key, ok := fields[i].(string); ok {
			event = event.Interface(key, fields[i+1])
		}
	}

	event.Msg(message)
}

// GetGlobalLogger returns the global zerolog logger
func GetGlobalLogger() *zerolog.Logger {
	return &log.Logger
}

// SetGlobalLogger sets the global zerolog logger
func SetGlobalLogger(logger *zerolog.Logger) {
	log.Logger = *logger
}

package config

import (
	"fmt"
	"strings"
	"time"

	"github.com/knadh/koanf/parsers/yaml"
	"github.com/knadh/koanf/providers/env"
	"github.com/knadh/koanf/providers/file"
	"github.com/knadh/koanf/v2"
)

// Config represents the application configuration structure
type Config struct {
	Server           ServerConfig           `koanf:"server"`
	Logger           LoggerConfig           `koanf:"logger"`
	Database         DatabaseConfig         `koanf:"database"`
	Redis            RedisConfig            `koanf:"redis"`
	JWT              JWTConfig              `koanf:"jwt"`
	Sentry           SentryConfig           `koanf:"sentry"`
	I18n             I18nConfig             `koanf:"i18n"`
	ExternalServices ExternalServicesConfig `koanf:"external_services"`
	RateLimiting     RateLimitingConfig     `koanf:"rate_limiting"`
	Monitoring       MonitoringConfig       `koanf:"monitoring"`
	Security         SecurityConfig         `koanf:"security"`
}

type ServerConfig struct {
	Host            string        `koanf:"host"`
	Port            int           `koanf:"port"`
	ReadTimeout     time.Duration `koanf:"read_timeout"`
	WriteTimeout    time.Duration `koanf:"write_timeout"`
	IdleTimeout     time.Duration `koanf:"idle_timeout"`
	ShutdownTimeout time.Duration `koanf:"shutdown_timeout"`
	TrustedProxies  []string      `koanf:"trusted_proxies"`
	CORS            CORSConfig    `koanf:"cors"`
}

type CORSConfig struct {
	AllowedOrigins   []string `koanf:"allowed_origins"`
	AllowedMethods   []string `koanf:"allowed_methods"`
	AllowedHeaders   []string `koanf:"allowed_headers"`
	ExposedHeaders   []string `koanf:"exposed_headers"`
	AllowCredentials bool     `koanf:"allow_credentials"`
	MaxAge           int      `koanf:"max_age"`
}

type LoggerConfig struct {
	Level     string `koanf:"level"`
	Format    string `koanf:"format"`
	Caller    bool   `koanf:"caller"`
	Timestamp bool   `koanf:"timestamp"`
	Color     bool   `koanf:"color"`
}

type DatabaseConfig struct {
	Host            string        `koanf:"host"`
	Port            int           `koanf:"port"`
	Name            string        `koanf:"name"`
	User            string        `koanf:"user"`
	Password        string        `koanf:"password"`
	SSLMode         string        `koanf:"ssl_mode"`
	MaxOpenConns    int           `koanf:"max_open_conns"`
	MaxIdleConns    int           `koanf:"max_idle_conns"`
	ConnMaxLifetime time.Duration `koanf:"conn_max_lifetime"`
	ConnMaxIdleTime time.Duration `koanf:"conn_max_idle_time"`
	MigrationPath   string        `koanf:"migration_path"`
}

type RedisConfig struct {
	Host         string        `koanf:"host"`
	Port         int           `koanf:"port"`
	Password     string        `koanf:"password"`
	DB           int           `koanf:"db"`
	PoolSize     int           `koanf:"pool_size"`
	MinIdleConns int           `koanf:"min_idle_conns"`
	DialTimeout  time.Duration `koanf:"dial_timeout"`
	ReadTimeout  time.Duration `koanf:"read_timeout"`
	WriteTimeout time.Duration `koanf:"write_timeout"`
	PoolTimeout  time.Duration `koanf:"pool_timeout"`
	IdleTimeout  time.Duration `koanf:"idle_timeout"`
}

type JWTConfig struct {
	Secret               string        `koanf:"secret"`
	Issuer               string        `koanf:"issuer"`
	Audience             string        `koanf:"audience"`
	AccessTokenDuration  time.Duration `koanf:"access_token_duration"`
	RefreshTokenDuration time.Duration `koanf:"refresh_token_duration"`
}

type SentryConfig struct {
	DSN              string  `koanf:"dsn"`
	Environment      string  `koanf:"environment"`
	Debug            bool    `koanf:"debug"`
	SampleRate       float64 `koanf:"sample_rate"`
	TracesSampleRate float64 `koanf:"traces_sample_rate"`
}

type I18nConfig struct {
	DefaultLanguage    string   `koanf:"default_language"`
	SupportedLanguages []string `koanf:"supported_languages"`
	FallbackLanguage   string   `koanf:"fallback_language"`
}

type ExternalServicesConfig struct {
	Xendit  XenditConfig  `koanf:"xendit"`
	NicePay NicePayConfig `koanf:"nicepay"`
}

type XenditConfig struct {
	BaseURL      string        `koanf:"base_url"`
	SecretKey    string        `koanf:"secret_key"`
	WebhookToken string        `koanf:"webhook_token"`
	Timeout      time.Duration `koanf:"timeout"`
}

type NicePayConfig struct {
	BaseURL    string        `koanf:"base_url"`
	MerchantID string        `koanf:"merchant_id"`
	SecretKey  string        `koanf:"secret_key"`
	Timeout    time.Duration `koanf:"timeout"`
}

type RateLimitingConfig struct {
	Enabled           bool `koanf:"enabled"`
	RequestsPerMinute int  `koanf:"requests_per_minute"`
	Burst             int  `koanf:"burst"`
}

type MonitoringConfig struct {
	MetricsEnabled        bool          `koanf:"metrics_enabled"`
	HealthCheckInterval   time.Duration `koanf:"health_check_interval"`
}

type SecurityConfig struct {
	BcryptCost        int           `koanf:"bcrypt_cost"`
	MaxLoginAttempts  int           `koanf:"max_login_attempts"`
	LockoutDuration   time.Duration `koanf:"lockout_duration"`
}

// Load loads configuration from file and environment variables
func Load(configPath string) (*Config, error) {
	k := koanf.New(".")

	// Load from YAML file
	if err := k.Load(file.Provider(configPath), yaml.Parser()); err != nil {
		return nil, fmt.Errorf("config.Load: failed to load config file: %w", err)
	}

	// Load from environment variables with prefix PAYMENT_GATEWAY_
	// Environment variables should be in format: PAYMENT_GATEWAY_SECTION_KEY
	// Example: PAYMENT_GATEWAY_SERVER_PORT=8080
	if err := k.Load(env.Provider("PAYMENT_GATEWAY_", ".", func(s string) string {
		// Convert PAYMENT_GATEWAY_SERVER_PORT to server.port
		return strings.Replace(strings.ToLower(
			strings.TrimPrefix(s, "PAYMENT_GATEWAY_")), "_", ".", -1)
	}), nil); err != nil {
		return nil, fmt.Errorf("config.Load: failed to load environment variables: %w", err)
	}

	var cfg Config
	if err := k.Unmarshal("", &cfg); err != nil {
		return nil, fmt.Errorf("config.Load: failed to unmarshal config: %w", err)
	}

	// Validate configuration
	if err := validate(&cfg); err != nil {
		return nil, fmt.Errorf("config.Load: validation failed: %w", err)
	}

	return &cfg, nil
}

// validate performs basic validation on the configuration
func validate(cfg *Config) error {
	if cfg.Server.Port <= 0 || cfg.Server.Port > 65535 {
		return fmt.Errorf("invalid server port: %d", cfg.Server.Port)
	}

	if cfg.Database.Host == "" {
		return fmt.Errorf("database host is required")
	}

	if cfg.Database.Name == "" {
		return fmt.Errorf("database name is required")
	}

	if cfg.JWT.Secret == "" || cfg.JWT.Secret == "your-super-secret-jwt-key-change-this-in-production" {
		return fmt.Errorf("JWT secret must be set and not use default value")
	}

	validLogLevels := map[string]bool{
		"trace": true, "debug": true, "info": true, "warn": true, "error": true, "fatal": true, "panic": true,
	}
	if !validLogLevels[cfg.Logger.Level] {
		return fmt.Errorf("invalid log level: %s", cfg.Logger.Level)
	}

	validLogFormats := map[string]bool{"console": true, "json": true}
	if !validLogFormats[cfg.Logger.Format] {
		return fmt.Errorf("invalid log format: %s", cfg.Logger.Format)
	}

	return nil
}

// GetDatabaseDSN returns the PostgreSQL connection string
func (c *DatabaseConfig) GetDatabaseDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		c.Host, c.Port, c.User, c.Password, c.Name, c.SSLMode)
}

// GetRedisAddr returns the Redis connection address
func (c *RedisConfig) GetRedisAddr() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}

// GetServerAddr returns the server address
func (c *ServerConfig) GetServerAddr() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}

package routes

import (
	"github.com/gin-gonic/gin"

	"github.com/wongpinter/payment-gateway/internal/interfaces/http/handler"
	"github.com/wongpinter/payment-gateway/internal/interfaces/http/middleware"
)

// AuthRoutes sets up authentication-related routes
func AuthRoutes(router *gin.RouterGroup, authHandler *handler.AuthHandler, authMiddleware *middleware.AuthMiddleware) {
	// Public auth routes (no authentication required)
	auth := router.Group("/auth")
	{
		auth.POST("/register", authHandler.Register)
		auth.POST("/login", authHandler.Login)
		auth.POST("/refresh", authHandler.RefreshToken)
		auth.POST("/logout", authHandler.Logout)
	}

	// Protected auth routes (authentication required)
	authProtected := router.Group("/auth")
	authProtected.Use(authMiddleware.RequireAuth())
	{
		authProtected.GET("/profile", authHandler.GetProfile)
		authProtected.POST("/change-password", authHandler.ChangePassword)
	}
}

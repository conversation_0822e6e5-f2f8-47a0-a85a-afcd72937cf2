package account

import (
	"time"

	"github.com/google/uuid"
)

// DomainEvent represents a domain event in the account context
type DomainEvent interface {
	EventID() string
	EventName() string
	EventTimestamp() time.Time
	AggregateID() string
}

// BaseEvent provides common fields for all domain events
type BaseEvent struct {
	ID        string    `json:"id"`
	Name      string    `json:"name"`
	Timestamp time.Time `json:"timestamp"`
	Aggregate string    `json:"aggregate_id"`
}

// EventID returns the event ID
func (e BaseEvent) EventID() string {
	return e.ID
}

// EventName returns the event name
func (e BaseEvent) EventName() string {
	return e.Name
}

// EventTimestamp returns the event timestamp
func (e BaseEvent) EventTimestamp() time.Time {
	return e.Timestamp
}

// AggregateID returns the aggregate ID
func (e BaseEvent) AggregateID() string {
	return e.Aggregate
}

// UserRegisteredEvent is published when a new user is registered
type UserRegisteredEvent struct {
	BaseEvent
	UserID UserID `json:"user_id"`
	Email  Email  `json:"email"`
	Name   string `json:"name"`
}

// NewUserRegisteredEvent creates a new UserRegisteredEvent
func NewUserRegisteredEvent(userID UserID, email Email, name string) *UserRegisteredEvent {
	return &UserRegisteredEvent{
		BaseEvent: BaseEvent{
			ID:        uuid.New().String(),
			Name:      "UserRegistered",
			Timestamp: time.Now(),
			Aggregate: userID.String(),
		},
		UserID: userID,
		Email:  email,
		Name:   name,
	}
}

// UserLoggedInEvent is published when a user logs in
type UserLoggedInEvent struct {
	BaseEvent
	UserID    UserID    `json:"user_id"`
	Email     Email     `json:"email"`
	LoginTime time.Time `json:"login_time"`
	IPAddress string    `json:"ip_address"`
	UserAgent string    `json:"user_agent"`
}

// NewUserLoggedInEvent creates a new UserLoggedInEvent
func NewUserLoggedInEvent(userID UserID, email Email, ipAddress, userAgent string) *UserLoggedInEvent {
	return &UserLoggedInEvent{
		BaseEvent: BaseEvent{
			ID:        uuid.New().String(),
			Name:      "UserLoggedIn",
			Timestamp: time.Now(),
			Aggregate: userID.String(),
		},
		UserID:    userID,
		Email:     email,
		LoginTime: time.Now(),
		IPAddress: ipAddress,
		UserAgent: userAgent,
	}
}

// UserLoggedOutEvent is published when a user logs out
type UserLoggedOutEvent struct {
	BaseEvent
	UserID     UserID    `json:"user_id"`
	Email      Email     `json:"email"`
	LogoutTime time.Time `json:"logout_time"`
}

// NewUserLoggedOutEvent creates a new UserLoggedOutEvent
func NewUserLoggedOutEvent(userID UserID, email Email) *UserLoggedOutEvent {
	return &UserLoggedOutEvent{
		BaseEvent: BaseEvent{
			ID:        uuid.New().String(),
			Name:      "UserLoggedOut",
			Timestamp: time.Now(),
			Aggregate: userID.String(),
		},
		UserID:     userID,
		Email:      email,
		LogoutTime: time.Now(),
	}
}

// UserPasswordChangedEvent is published when a user changes their password
type UserPasswordChangedEvent struct {
	BaseEvent
	UserID     UserID    `json:"user_id"`
	Email      Email     `json:"email"`
	ChangeTime time.Time `json:"change_time"`
}

// NewUserPasswordChangedEvent creates a new UserPasswordChangedEvent
func NewUserPasswordChangedEvent(userID UserID, email Email) *UserPasswordChangedEvent {
	return &UserPasswordChangedEvent{
		BaseEvent: BaseEvent{
			ID:        uuid.New().String(),
			Name:      "UserPasswordChanged",
			Timestamp: time.Now(),
			Aggregate: userID.String(),
		},
		UserID:     userID,
		Email:      email,
		ChangeTime: time.Now(),
	}
}

// UserDeactivatedEvent is published when a user is deactivated
type UserDeactivatedEvent struct {
	BaseEvent
	UserID           UserID    `json:"user_id"`
	Email            Email     `json:"email"`
	DeactivationTime time.Time `json:"deactivation_time"`
	Reason           string    `json:"reason"`
}

// NewUserDeactivatedEvent creates a new UserDeactivatedEvent
func NewUserDeactivatedEvent(userID UserID, email Email, reason string) *UserDeactivatedEvent {
	return &UserDeactivatedEvent{
		BaseEvent: BaseEvent{
			ID:        uuid.New().String(),
			Name:      "UserDeactivated",
			Timestamp: time.Now(),
			Aggregate: userID.String(),
		},
		UserID:           userID,
		Email:            email,
		DeactivationTime: time.Now(),
		Reason:           reason,
	}
}

// UserActivatedEvent is published when a user is activated
type UserActivatedEvent struct {
	BaseEvent
	UserID         UserID    `json:"user_id"`
	Email          Email     `json:"email"`
	ActivationTime time.Time `json:"activation_time"`
}

// NewUserActivatedEvent creates a new UserActivatedEvent
func NewUserActivatedEvent(userID UserID, email Email) *UserActivatedEvent {
	return &UserActivatedEvent{
		BaseEvent: BaseEvent{
			ID:        uuid.New().String(),
			Name:      "UserActivated",
			Timestamp: time.Now(),
			Aggregate: userID.String(),
		},
		UserID:         userID,
		Email:          email,
		ActivationTime: time.Now(),
	}
}

// RoleAssignedEvent is published when a role is assigned to a user
type RoleAssignedEvent struct {
	BaseEvent
	UserID         UserID    `json:"user_id"`
	RoleID         RoleID    `json:"role_id"`
	RoleName       string    `json:"role_name"`
	AssignmentTime time.Time `json:"assignment_time"`
}

// NewRoleAssignedEvent creates a new RoleAssignedEvent
func NewRoleAssignedEvent(userID UserID, roleID RoleID, roleName string) *RoleAssignedEvent {
	return &RoleAssignedEvent{
		BaseEvent: BaseEvent{
			ID:        uuid.New().String(),
			Name:      "RoleAssigned",
			Timestamp: time.Now(),
			Aggregate: userID.String(),
		},
		UserID:         userID,
		RoleID:         roleID,
		RoleName:       roleName,
		AssignmentTime: time.Now(),
	}
}

// RoleRevokedEvent is published when a role is revoked from a user
type RoleRevokedEvent struct {
	BaseEvent
	UserID         UserID    `json:"user_id"`
	RoleID         RoleID    `json:"role_id"`
	RoleName       string    `json:"role_name"`
	RevocationTime time.Time `json:"revocation_time"`
}

// NewRoleRevokedEvent creates a new RoleRevokedEvent
func NewRoleRevokedEvent(userID UserID, roleID RoleID, roleName string) *RoleRevokedEvent {
	return &RoleRevokedEvent{
		BaseEvent: BaseEvent{
			ID:        uuid.New().String(),
			Name:      "RoleRevoked",
			Timestamp: time.Now(),
			Aggregate: userID.String(),
		},
		UserID:         userID,
		RoleID:         roleID,
		RoleName:       roleName,
		RevocationTime: time.Now(),
	}
}

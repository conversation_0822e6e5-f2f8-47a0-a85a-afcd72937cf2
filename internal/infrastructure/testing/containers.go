package testing

import (
	"context"
	"fmt"
	"time"

	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/modules/postgres"
	"github.com/testcontainers/testcontainers-go/modules/redis"
	"github.com/testcontainers/testcontainers-go/wait"

	"github.com/wongpinter/payment-gateway/internal/infrastructure/config"
)

// TestContainers manages test containers for integration testing
type TestContainers struct {
	PostgresContainer *postgres.PostgresContainer
	RedisContainer    *redis.RedisContainer
	PostgresConfig    *config.DatabaseConfig
	RedisConfig       *config.RedisConfig
}

// SetupTestContainers creates and starts test containers
func SetupTestContainers(ctx context.Context) (*TestContainers, error) {
	tc := &TestContainers{}

	// Setup PostgreSQL container
	if err := tc.setupPostgres(ctx); err != nil {
		return nil, fmt.Errorf("testing.SetupTestContainers: failed to setup postgres: %w", err)
	}

	// Setup Redis container
	if err := tc.setupRedis(ctx); err != nil {
		tc.Cleanup(ctx) // Cleanup postgres if redis fails
		return nil, fmt.Errorf("testing.SetupTestContainers: failed to setup redis: %w", err)
	}

	return tc, nil
}

// setupPostgres creates and configures PostgreSQL test container
func (tc *TestContainers) setupPostgres(ctx context.Context) error {
	// Create PostgreSQL container
	postgresContainer, err := postgres.Run(ctx,
		"postgres:15-alpine",
		postgres.WithDatabase("payment_gateway_test"),
		postgres.WithUsername("test_user"),
		postgres.WithPassword("test_password"),
		testcontainers.WithWaitStrategy(
			wait.ForLog("database system is ready to accept connections").
				WithOccurrence(2).
				WithStartupTimeout(30*time.Second),
		),
	)
	if err != nil {
		return fmt.Errorf("failed to start postgres container: %w", err)
	}

	tc.PostgresContainer = postgresContainer

	// Get connection details
	host, err := postgresContainer.Host(ctx)
	if err != nil {
		return fmt.Errorf("failed to get postgres host: %w", err)
	}

	port, err := postgresContainer.MappedPort(ctx, "5432")
	if err != nil {
		return fmt.Errorf("failed to get postgres port: %w", err)
	}

	// Create database config
	tc.PostgresConfig = &config.DatabaseConfig{
		Host:            host,
		Port:            port.Int(),
		Name:            "payment_gateway_test",
		User:            "test_user",
		Password:        "test_password",
		SSLMode:         "disable",
		MaxOpenConns:    10,
		MaxIdleConns:    5,
		ConnMaxLifetime: 5 * time.Minute,
		ConnMaxIdleTime: 5 * time.Minute,
		MigrationPath:   "file://db/migrations",
	}

	return nil
}

// setupRedis creates and configures Redis test container
func (tc *TestContainers) setupRedis(ctx context.Context) error {
	// Create Redis container
	redisContainer, err := redis.Run(ctx,
		"redis:7-alpine",
		testcontainers.WithWaitStrategy(
			wait.ForLog("Ready to accept connections").
				WithStartupTimeout(30*time.Second),
		),
	)
	if err != nil {
		return fmt.Errorf("failed to start redis container: %w", err)
	}

	tc.RedisContainer = redisContainer

	// Get connection details
	host, err := redisContainer.Host(ctx)
	if err != nil {
		return fmt.Errorf("failed to get redis host: %w", err)
	}

	port, err := redisContainer.MappedPort(ctx, "6379")
	if err != nil {
		return fmt.Errorf("failed to get redis port: %w", err)
	}

	// Create Redis config
	tc.RedisConfig = &config.RedisConfig{
		Host:         host,
		Port:         port.Int(),
		Password:     "",
		DB:           0,
		PoolSize:     5,
		MinIdleConns: 2,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		PoolTimeout:  4 * time.Second,
		IdleTimeout:  5 * time.Minute,
	}

	return nil
}

// Cleanup terminates all test containers
func (tc *TestContainers) Cleanup(ctx context.Context) error {
	var errs []error

	if tc.PostgresContainer != nil {
		if err := tc.PostgresContainer.Terminate(ctx); err != nil {
			errs = append(errs, fmt.Errorf("failed to terminate postgres container: %w", err))
		}
	}

	if tc.RedisContainer != nil {
		if err := tc.RedisContainer.Terminate(ctx); err != nil {
			errs = append(errs, fmt.Errorf("failed to terminate redis container: %w", err))
		}
	}

	if len(errs) > 0 {
		return fmt.Errorf("cleanup errors: %v", errs)
	}

	return nil
}

// GetPostgresDSN returns the PostgreSQL connection string for tests
func (tc *TestContainers) GetPostgresDSN() string {
	return tc.PostgresConfig.GetDatabaseDSN()
}

// GetRedisAddr returns the Redis connection address for tests
func (tc *TestContainers) GetRedisAddr() string {
	return tc.RedisConfig.GetRedisAddr()
}

// WaitForHealthy waits for all containers to be healthy
func (tc *TestContainers) WaitForHealthy(ctx context.Context) error {
	// Wait for PostgreSQL
	if tc.PostgresContainer != nil {
		if _, err := tc.PostgresContainer.ConnectionString(ctx); err != nil {
			return fmt.Errorf("postgres container not healthy: %w", err)
		}
	}

	// Redis doesn't have a built-in health check, but we can test connection
	// This will be handled in the actual test setup

	return nil
}

// TestContainerManager provides a singleton pattern for test containers
type TestContainerManager struct {
	containers *TestContainers
	ctx        context.Context
}

var globalTestManager *TestContainerManager

// GetTestContainers returns the global test container manager
func GetTestContainers() *TestContainerManager {
	return globalTestManager
}

// InitTestContainers initializes the global test container manager
func InitTestContainers(ctx context.Context) error {
	containers, err := SetupTestContainers(ctx)
	if err != nil {
		return fmt.Errorf("testing.InitTestContainers: %w", err)
	}

	globalTestManager = &TestContainerManager{
		containers: containers,
		ctx:        ctx,
	}

	return nil
}

// CleanupTestContainers cleans up the global test container manager
func CleanupTestContainers() error {
	if globalTestManager != nil {
		err := globalTestManager.containers.Cleanup(globalTestManager.ctx)
		globalTestManager = nil
		return err
	}
	return nil
}

// GetTestDatabaseConfig returns test database configuration
func GetTestDatabaseConfig() *config.DatabaseConfig {
	if globalTestManager != nil {
		return globalTestManager.containers.PostgresConfig
	}
	return nil
}

// GetTestRedisConfig returns test Redis configuration
func GetTestRedisConfig() *config.RedisConfig {
	if globalTestManager != nil {
		return globalTestManager.containers.RedisConfig
	}
	return nil
}

-- Rollback initial schema migration

-- Drop triggers
DROP TRIGGER IF EXISTS update_user_companies_updated_at ON user_companies;
DROP TRIGGER IF EXISTS update_role_permissions_updated_at ON role_permissions;
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
DROP TRIGGER IF EXISTS update_roles_updated_at ON roles;

-- Drop trigger function
DROP FUNCTION IF EXISTS update_updated_at_column();

-- Drop indexes
DROP INDEX IF EXISTS idx_user_companies_company_id;
DROP INDEX IF EXISTS idx_user_companies_user_id;
DROP INDEX IF EXISTS idx_role_permissions_menu_id;
DROP INDEX IF EXISTS idx_role_permissions_role_id;
DROP INDEX IF EXISTS idx_roles_status;
DROP INDEX IF EXISTS idx_roles_code;
DROP INDEX IF EXISTS idx_users_status;
DROP INDEX IF EXISTS idx_users_role_id;
DROP INDEX IF EXISTS idx_users_email;

-- Drop tables in reverse order (respecting foreign key constraints)
DROP TABLE IF EXISTS user_companies;
DROP TABLE IF EXISTS role_permissions;
DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS roles;

-- Drop extensions (optional, might be used by other schemas)
-- DROP EXTENSION IF EXISTS "uuid-ossp";

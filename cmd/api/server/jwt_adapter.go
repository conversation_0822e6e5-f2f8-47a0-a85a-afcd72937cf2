package main

import (
	"context"
	"time"

	"github.com/wongpinter/payment-gateway/internal/application/account/service"
	"github.com/wongpinter/payment-gateway/internal/domain/account"
	"github.com/wongpinter/payment-gateway/internal/infrastructure/security"
)

// JWTAdapter adapts the security.JWTManager to implement service.JWTManager interface
type JWTAdapter struct {
	jwtManager *security.JWTManager
}

// NewJWTAdapter creates a new JWTAdapter
func NewJWTAdapter(jwtManager *security.JWTManager) *JWTAdapter {
	return &JWTAdapter{
		jwtManager: jwtManager,
	}
}

// GenerateAccessToken generates an access token
func (a *JWTAdapter) GenerateAccessToken(ctx context.Context, user *account.User, roles []account.Role) (string, time.Time, error) {
	return a.jwtManager.GenerateAccessToken(ctx, user, roles)
}

// GenerateRefreshToken generates a refresh token
func (a *J<PERSON>TAdapter) GenerateRefreshToken(ctx context.Context, user *account.User) (string, time.Time, error) {
	return a.jwtManager.GenerateRefreshToken(ctx, user)
}

// ValidateAccessToken validates an access token and converts the claims
func (a *JWTAdapter) ValidateAccessToken(ctx context.Context, tokenString string) (*service.AccessTokenClaims, error) {
	claims, err := a.jwtManager.ValidateAccessToken(ctx, tokenString)
	if err != nil {
		return nil, err
	}

	// Convert security.AccessTokenClaims to service.AccessTokenClaims
	return &service.AccessTokenClaims{
		UserID: claims.UserID,
		Email:  claims.Email,
		Name:   claims.Name,
		Roles:  claims.Roles,
	}, nil
}

// ValidateRefreshToken validates a refresh token and converts the claims
func (a *JWTAdapter) ValidateRefreshToken(ctx context.Context, tokenString string) (*service.RefreshTokenClaims, error) {
	claims, err := a.jwtManager.ValidateRefreshToken(ctx, tokenString)
	if err != nil {
		return nil, err
	}

	// Convert security.RefreshTokenClaims to service.RefreshTokenClaims
	return &service.RefreshTokenClaims{
		UserID: claims.UserID,
	}, nil
}

// BlacklistToken blacklists a token
func (a *JWTAdapter) BlacklistToken(ctx context.Context, tokenString string) error {
	return a.jwtManager.BlacklistToken(ctx, tokenString)
}

// BlacklistUserTokens blacklists all tokens for a user
func (a *JWTAdapter) BlacklistUserTokens(ctx context.Context, userID string, expiresAt time.Time) error {
	return a.jwtManager.BlacklistUserTokens(ctx, userID, expiresAt)
}

// ExtractTokenID extracts the token ID from a token string
func (a *JWTAdapter) ExtractTokenID(tokenString string) (string, error) {
	return a.jwtManager.ExtractTokenID(tokenString)
}

// GetTokenTTLs returns the token TTLs
func (a *JWTAdapter) GetTokenTTLs() (accessTTL, refreshTTL time.Duration) {
	return a.jwtManager.GetTokenTTLs()
}

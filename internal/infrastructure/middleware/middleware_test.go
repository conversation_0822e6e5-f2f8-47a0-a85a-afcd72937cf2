package middleware

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"github.com/wongpinter/payment-gateway/internal/infrastructure/config"
)

func TestTracingMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	router := gin.New()
	router.Use(TracingMiddleware())

	router.GET("/test", func(c *gin.Context) {
		// Check that trace_id and correlation_id are set in context
		traceID := GetTraceID(c.Request.Context())
		correlationID := GetCorrelationID(c.Request.Context())

		assert.NotEmpty(t, traceID)
		assert.NotEmpty(t, correlationID)

		c.JSON(http.StatusOK, gin.H{
			"trace_id":       traceID,
			"correlation_id": correlationID,
		})
	})

	req := httptest.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// Check response headers
	assert.NotEmpty(t, w.Header().Get("X-Trace-ID"))
	assert.NotEmpty(t, w.Header().Get("X-Correlation-ID"))
}

func TestTracingMiddlewareWithExistingCorrelationID(t *testing.T) {
	gin.SetMode(gin.TestMode)

	router := gin.New()
	router.Use(TracingMiddleware())

	router.GET("/test", func(c *gin.Context) {
		correlationID := GetCorrelationID(c.Request.Context())
		c.JSON(http.StatusOK, gin.H{"correlation_id": correlationID})
	})

	req := httptest.NewRequest("GET", "/test", nil)
	req.Header.Set("X-Correlation-ID", "existing-correlation-id")
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "existing-correlation-id", w.Header().Get("X-Correlation-ID"))
}

func TestLoggingMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	router := gin.New()
	router.Use(TracingMiddleware()) // Need tracing for correlation ID
	router.Use(LoggingMiddleware())

	router.GET("/test", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "test"})
	})

	req := httptest.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}

func TestDetailedLoggingMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	router := gin.New()
	router.Use(TracingMiddleware())
	router.Use(DetailedLoggingMiddleware())

	router.POST("/test", func(c *gin.Context) {
		c.JSON(http.StatusCreated, gin.H{"created": true})
	})

	req := httptest.NewRequest("POST", "/test", nil)
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusCreated, w.Code)
}

func TestLocalizationMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	i18nConfig := &config.I18nConfig{
		DefaultLanguage:    "en",
		FallbackLanguage:   "en",
		SupportedLanguages: []string{"en", "id"},
	}

	router := gin.New()
	router.Use(LocalizationMiddleware(i18nConfig))

	tests := []struct {
		name           string
		acceptLanguage string
		queryLang      string
		expectedLang   string
	}{
		{
			name:         "default language",
			expectedLang: "en",
		},
		{
			name:           "accept-language header",
			acceptLanguage: "id",
			expectedLang:   "id",
		},
		{
			name:           "query parameter overrides header",
			acceptLanguage: "id",
			queryLang:      "en",
			expectedLang:   "en",
		},
		{
			name:           "unsupported language falls back to default",
			acceptLanguage: "fr",
			expectedLang:   "en",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			router.GET("/test", func(c *gin.Context) {
				lang := GetLanguage(c.Request.Context())
				c.JSON(http.StatusOK, gin.H{"language": lang})
			})

			url := "/test"
			if tt.queryLang != "" {
				url += "?lang=" + tt.queryLang
			}

			req := httptest.NewRequest("GET", url, nil)
			if tt.acceptLanguage != "" {
				req.Header.Set("Accept-Language", tt.acceptLanguage)
			}
			w := httptest.NewRecorder()

			router.ServeHTTP(w, req)

			assert.Equal(t, http.StatusOK, w.Code)
		})
	}
}

func TestErrorMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	router := gin.New()
	router.Use(TracingMiddleware())
	router.Use(ErrorMiddleware())

	router.GET("/panic", func(c *gin.Context) {
		panic("test panic")
	})

	req := httptest.NewRequest("GET", "/panic", nil)
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestErrorHandlerMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	router := gin.New()
	router.Use(TracingMiddleware())
	router.Use(ErrorHandlerMiddleware())

	router.GET("/error", func(c *gin.Context) {
		c.Error(gin.Error{
			Err:  assert.AnError,
			Type: gin.ErrorTypePublic,
		})
	})

	req := httptest.NewRequest("GET", "/error", nil)
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestContextHelpers(t *testing.T) {
	ctx := context.Background()

	// Test setting and getting trace ID
	ctx = WithTraceID(ctx, "test-trace-id")
	traceID := GetTraceID(ctx)
	assert.Equal(t, "test-trace-id", traceID)

	// Test setting and getting correlation ID
	ctx = WithCorrelationID(ctx, "test-correlation-id")
	correlationID := GetCorrelationID(ctx)
	assert.Equal(t, "test-correlation-id", correlationID)

	// Test setting and getting language
	ctx = WithLanguage(ctx, "id")
	language := GetLanguage(ctx)
	assert.Equal(t, "id", language)

	// Test setting and getting user ID
	ctx = WithUserID(ctx, "test-user-id")
	userID := GetUserID(ctx)
	assert.Equal(t, "test-user-id", userID)
}

-- name: CreateUser :one
INSERT INTO users (
    id, name, email, password_hash, is_active, created_at, updated_at
) VALUES (
    $1, $2, $3, $4, $5, $6, $7
) RETURNING *;

-- name: GetUserByID :one
SELECT * FROM users 
WHERE id = $1 AND deleted_at IS NULL;

-- name: GetUserByEmail :one
SELECT * FROM users 
WHERE email = $1 AND deleted_at IS NULL;

-- name: UpdateUser :one
UPDATE users 
SET 
    name = $2,
    email = $3,
    password_hash = $4,
    is_active = $5,
    updated_at = $6
WHERE id = $1 AND deleted_at IS NULL
RETURNING *;

-- name: DeleteUser :exec
UPDATE users 
SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND deleted_at IS NULL;

-- name: ListUsers :many
SELECT * FROM users 
WHERE deleted_at IS NULL
ORDER BY created_at DESC
LIMIT $1 OFFSET $2;

-- name: CountUsers :one
SELECT COUNT(*) FROM users 
WHERE deleted_at IS NULL;

-- name: ExistsByEmail :one
SELECT EXISTS(
    SELECT 1 FROM users 
    WHERE email = $1 AND deleted_at IS NULL
);

-- name: GetUserRoles :many
SELECT r.* FROM roles r
INNER JOIN user_roles ur ON r.id = ur.role_id
WHERE ur.user_id = $1 
    AND ur.deleted_at IS NULL 
    AND r.deleted_at IS NULL
    AND r.is_active = TRUE
ORDER BY r.name;

-- name: AssignUserRole :exec
INSERT INTO user_roles (id, user_id, role_id, created_at, updated_at)
VALUES (gen_random_uuid(), $1, $2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (user_id, role_id) DO NOTHING;

-- name: RevokeUserRole :exec
UPDATE user_roles 
SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
WHERE user_id = $1 AND role_id = $2 AND deleted_at IS NULL;

-- name: GetUsersWithRole :many
SELECT u.* FROM users u
INNER JOIN user_roles ur ON u.id = ur.user_id
WHERE ur.role_id = $1 
    AND ur.deleted_at IS NULL 
    AND u.deleted_at IS NULL
    AND u.is_active = TRUE
ORDER BY u.name;

-- name: GetUserWithRoles :one
SELECT 
    u.id,
    u.name,
    u.email,
    u.password_hash,
    u.is_active,
    u.last_login_at,
    u.created_at,
    u.updated_at,
    COALESCE(
        JSON_AGG(
            JSON_BUILD_OBJECT(
                'id', r.id,
                'name', r.name,
                'description', r.description,
                'is_active', r.is_active
            )
        ) FILTER (WHERE r.id IS NOT NULL), 
        '[]'::json
    ) as roles
FROM users u
LEFT JOIN user_roles ur ON u.id = ur.user_id AND ur.deleted_at IS NULL
LEFT JOIN roles r ON ur.role_id = r.id AND r.deleted_at IS NULL AND r.is_active = TRUE
WHERE u.id = $1 AND u.deleted_at IS NULL
GROUP BY u.id, u.name, u.email, u.password_hash, u.is_active, u.last_login_at, u.created_at, u.updated_at;

-- name: UpdateLastLogin :exec
UPDATE users
SET last_login_at = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 AND deleted_at IS NULL;

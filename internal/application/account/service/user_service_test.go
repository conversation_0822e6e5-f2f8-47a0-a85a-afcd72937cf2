package service

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/wongpinter/payment-gateway/internal/domain/account"
)

func TestUserService_RegisterUser(t *testing.T) {
	t.Run("successful user registration", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}

		// Create service
		userService := NewUserService(userRepo, roleRepo, passwordHasher, eventPublisher)

		// Create default role for test
		defaultRole := account.NewRole("user", "Default user role")

		// Setup expectations
		userRepo.On("ExistsByEmail", mock.Anything, mock.AnythingOfType("account.Email")).Return(false, nil)
		passwordHasher.On("Hash", "password123").Return("hashed_password", nil)
		userRepo.On("Create", mock.Anything, mock.AnythingOfType("*account.User")).Return(nil)
		roleRepo.On("GetByName", mock.Anything, "user").Return(defaultRole, nil)
		userRepo.On("AssignRole", mock.Anything, mock.AnythingOfType("account.UserID"), defaultRole.ID()).Return(nil)
		eventPublisher.On("Publish", mock.Anything, mock.AnythingOfType("*account.UserRegisteredEvent")).Return(nil)

		// Execute
		resp, err := userService.RegisterUser(context.Background(), "<EMAIL>", "password123", "Test User")

		// Assert
		require.NoError(t, err)
		assert.NotEmpty(t, resp.ID().String())
		assert.Equal(t, "<EMAIL>", resp.Email().String())
		assert.Equal(t, "Test User", resp.Name())
		assert.True(t, resp.IsActive())

		// Verify expectations
		userRepo.AssertExpectations(t)
		roleRepo.AssertExpectations(t)
		passwordHasher.AssertExpectations(t)
		eventPublisher.AssertExpectations(t)
	})

	t.Run("invalid email format", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}

		// Create service
		userService := NewUserService(userRepo, roleRepo, passwordHasher, eventPublisher)

		// Execute
		_, err := userService.RegisterUser(context.Background(), "invalid-email", "password123", "Test User")

		// Assert
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid email format")
	})

	t.Run("email already exists", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}

		// Create service
		userService := NewUserService(userRepo, roleRepo, passwordHasher, eventPublisher)

		// Setup expectations
		userRepo.On("ExistsByEmail", mock.Anything, mock.AnythingOfType("account.Email")).Return(true, nil)

		// Execute
		_, err := userService.RegisterUser(context.Background(), "<EMAIL>", "password123", "Test User")

		// Assert
		assert.Error(t, err)
		assert.Equal(t, account.ErrUserAlreadyExists, err)

		// Verify expectations
		userRepo.AssertExpectations(t)
	})

	t.Run("password hashing fails", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}

		// Create service
		userService := NewUserService(userRepo, roleRepo, passwordHasher, eventPublisher)

		// Setup expectations
		userRepo.On("ExistsByEmail", mock.Anything, mock.AnythingOfType("account.Email")).Return(false, nil)
		passwordHasher.On("Hash", "password123").Return("", errors.New("hashing failed"))

		// Execute
		_, err := userService.RegisterUser(context.Background(), "<EMAIL>", "password123", "Test User")

		// Assert
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "hashing failed")

		// Verify expectations
		userRepo.AssertExpectations(t)
		passwordHasher.AssertExpectations(t)
	})
}

func TestUserService_GetUserByID(t *testing.T) {
	t.Run("successful user retrieval", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}

		// Create service
		userService := NewUserService(userRepo, roleRepo, passwordHasher, eventPublisher)

		// Setup test data
		userID := account.NewUserID()
		email, _ := account.NewEmail("<EMAIL>")
		passwordHash := account.NewPasswordHash("hashed_password")
		user := account.NewUserWithID(userID, email, passwordHash, "Test User", true, time.Now(), time.Now())

		// Setup expectations
		userRepo.On("GetByID", mock.Anything, userID).Return(user, nil)

		// Execute
		resp, err := userService.GetUserByID(context.Background(), userID)

		// Assert
		require.NoError(t, err)
		assert.Equal(t, userID.String(), resp.ID().String())
		assert.Equal(t, "<EMAIL>", resp.Email().String())
		assert.Equal(t, "Test User", resp.Name())
		assert.True(t, resp.IsActive())

		// Verify expectations
		userRepo.AssertExpectations(t)
	})

	t.Run("invalid user ID format", func(t *testing.T) {
		// Execute - test with invalid UUID string
		_, err := account.UserIDFromString("invalid-uuid")

		// Assert - UserIDFromString should return an error for invalid UUID
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid user ID format")
	})

	t.Run("user not found", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}

		// Create service
		userService := NewUserService(userRepo, roleRepo, passwordHasher, eventPublisher)

		// Setup test data
		userID := account.NewUserID()

		// Setup expectations
		userRepo.On("GetByID", mock.Anything, userID).Return(nil, account.ErrUserNotFound)

		// Execute
		_, err := userService.GetUserByID(context.Background(), userID)

		// Assert
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "user not found")

		// Verify expectations
		userRepo.AssertExpectations(t)
	})
}

func TestUserService_UpdateUser(t *testing.T) {
	t.Run("successful user update", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}

		// Create service
		userService := NewUserService(userRepo, roleRepo, passwordHasher, eventPublisher)

		// Setup test data
		userID := account.NewUserID()
		email, _ := account.NewEmail("<EMAIL>")
		passwordHash := account.NewPasswordHash("hashed_password")
		user := account.NewUserWithID(userID, email, passwordHash, "Old Name", true, time.Now(), time.Now())

		// Setup expectations
		userRepo.On("GetByID", mock.Anything, userID).Return(user, nil)
		userRepo.On("Update", mock.Anything, user).Return(nil)

		// Execute
		resp, err := userService.UpdateUser(context.Background(), userID, "New Name", "")

		// Assert
		require.NoError(t, err)
		assert.Equal(t, userID.String(), resp.ID().String())
		assert.Equal(t, "New Name", resp.Name())

		// Verify expectations
		userRepo.AssertExpectations(t)
	})

	t.Run("user not found", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}

		// Create service
		userService := NewUserService(userRepo, roleRepo, passwordHasher, eventPublisher)

		// Setup test data
		userID := account.NewUserID()

		// Setup expectations
		userRepo.On("GetByID", mock.Anything, userID).Return(nil, account.ErrUserNotFound)

		// Execute
		_, err := userService.UpdateUser(context.Background(), userID, "New Name", "")

		// Assert
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "user not found")

		// Verify expectations
		userRepo.AssertExpectations(t)
	})
}

func TestUserService_ChangePassword(t *testing.T) {
	t.Run("successful password change", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}

		// Create service
		userService := NewUserService(userRepo, roleRepo, passwordHasher, eventPublisher)

		// Setup test data
		userID := account.NewUserID()
		email, _ := account.NewEmail("<EMAIL>")
		passwordHash := account.NewPasswordHash("old_hashed_password")
		user := account.NewUserWithID(userID, email, passwordHash, "Test User", true, time.Now(), time.Now())

		// Setup expectations
		userRepo.On("GetByID", mock.Anything, userID).Return(user, nil)
		passwordHasher.On("Verify", "old_password", "old_hashed_password").Return(nil)
		passwordHasher.On("Hash", "new_password").Return("new_hashed_password", nil)
		userRepo.On("Update", mock.Anything, user).Return(nil)
		eventPublisher.On("Publish", mock.Anything, mock.AnythingOfType("*account.UserPasswordChangedEvent")).Return(nil)

		// Execute
		err := userService.ChangePassword(context.Background(), userID, "old_password", "new_password")

		// Assert
		require.NoError(t, err)

		// Verify expectations
		userRepo.AssertExpectations(t)
		passwordHasher.AssertExpectations(t)
		eventPublisher.AssertExpectations(t)
	})

	t.Run("invalid old password", func(t *testing.T) {
		// Setup mocks
		userRepo := &MockUserRepository{}
		roleRepo := &MockRoleRepository{}
		passwordHasher := &MockPasswordHasher{}
		eventPublisher := &MockEventPublisher{}

		// Create service
		userService := NewUserService(userRepo, roleRepo, passwordHasher, eventPublisher)

		// Setup test data
		userID := account.NewUserID()
		email, _ := account.NewEmail("<EMAIL>")
		passwordHash := account.NewPasswordHash("old_hashed_password")
		user := account.NewUserWithID(userID, email, passwordHash, "Test User", true, time.Now(), time.Now())

		// Setup expectations
		userRepo.On("GetByID", mock.Anything, userID).Return(user, nil)
		passwordHasher.On("Verify", "wrong_password", "old_hashed_password").Return(errors.New("password mismatch"))

		// Execute
		err := userService.ChangePassword(context.Background(), userID, "wrong_password", "new_password")

		// Assert
		assert.Error(t, err)
		assert.Equal(t, account.ErrInvalidCredentials, err)

		// Verify expectations
		userRepo.AssertExpectations(t)
		passwordHasher.AssertExpectations(t)
	})
}

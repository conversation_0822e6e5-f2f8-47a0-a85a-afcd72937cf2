package repository

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"

	"github.com/wongpinter/payment-gateway/internal/domain/account"
)

// SessionRepository implements the account.SessionRepository interface using Redis
type SessionRepository struct {
	client *redis.Client
}

// NewSessionRepository creates a new SessionRepository
func NewSessionRepository(client *redis.Client) *SessionRepository {
	return &SessionRepository{
		client: client,
	}
}

// sessionData represents the data stored in Redis for a session
type sessionData struct {
	ID           string    `json:"id"`
	UserID       string    `json:"user_id"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresAt    time.Time `json:"expires_at"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// Create saves a new session to Redis
func (r *SessionRepository) Create(ctx context.Context, session *account.Session) error {
	data := sessionData{
		ID:           session.ID().String(),
		UserID:       session.UserID().String(),
		RefreshToken: session.RefreshToken(),
		ExpiresAt:    session.ExpiresAt(),
		CreatedAt:    session.CreatedAt(),
		UpdatedAt:    session.UpdatedAt(),
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("session_repository.Create: failed to marshal session: %w", err)
	}

	// Calculate TTL based on expiration time
	ttl := time.Until(session.ExpiresAt())
	if ttl <= 0 {
		return fmt.Errorf("session_repository.Create: session already expired")
	}

	// Store session by ID
	sessionKey := fmt.Sprintf("session:id:%s", session.ID().String())
	if err := r.client.Set(ctx, sessionKey, jsonData, ttl).Err(); err != nil {
		return fmt.Errorf("session_repository.Create: failed to store session by ID: %w", err)
	}

	// Store session by refresh token for quick lookup
	tokenKey := fmt.Sprintf("session:token:%s", session.RefreshToken())
	if err := r.client.Set(ctx, tokenKey, session.ID().String(), ttl).Err(); err != nil {
		// Clean up the session key if token key fails
		_ = r.client.Del(ctx, sessionKey)
		return fmt.Errorf("session_repository.Create: failed to store session by token: %w", err)
	}

	// Add to user's session set
	userKey := fmt.Sprintf("user:sessions:%s", session.UserID().String())
	if err := r.client.SAdd(ctx, userKey, session.ID().String()).Err(); err != nil {
		// Clean up on failure
		_ = r.client.Del(ctx, sessionKey, tokenKey)
		return fmt.Errorf("session_repository.Create: failed to add to user sessions: %w", err)
	}

	// Set expiration for user sessions set (longer than individual sessions)
	_ = r.client.Expire(ctx, userKey, ttl+time.Hour)

	return nil
}

// GetByID retrieves a session by its ID
func (r *SessionRepository) GetByID(ctx context.Context, id account.SessionID) (*account.Session, error) {
	sessionKey := fmt.Sprintf("session:id:%s", id.String())
	
	jsonData, err := r.client.Get(ctx, sessionKey).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return nil, account.ErrSessionNotFound
		}
		return nil, fmt.Errorf("session_repository.GetByID: %w", err)
	}

	var data sessionData
	if err := json.Unmarshal([]byte(jsonData), &data); err != nil {
		return nil, fmt.Errorf("session_repository.GetByID: failed to unmarshal session: %w", err)
	}

	return r.dataToSession(data)
}

// GetByRefreshToken retrieves a session by its refresh token
func (r *SessionRepository) GetByRefreshToken(ctx context.Context, refreshToken string) (*account.Session, error) {
	tokenKey := fmt.Sprintf("session:token:%s", refreshToken)
	
	sessionID, err := r.client.Get(ctx, tokenKey).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return nil, account.ErrSessionNotFound
		}
		return nil, fmt.Errorf("session_repository.GetByRefreshToken: %w", err)
	}

	id, err := account.SessionIDFromString(sessionID)
	if err != nil {
		return nil, fmt.Errorf("session_repository.GetByRefreshToken: invalid session ID: %w", err)
	}

	return r.GetByID(ctx, id)
}

// Update updates an existing session in Redis
func (r *SessionRepository) Update(ctx context.Context, session *account.Session) error {
	// Check if session exists
	sessionKey := fmt.Sprintf("session:id:%s", session.ID().String())
	exists, err := r.client.Exists(ctx, sessionKey).Result()
	if err != nil {
		return fmt.Errorf("session_repository.Update: %w", err)
	}
	if exists == 0 {
		return account.ErrSessionNotFound
	}

	// Get old session data to clean up old refresh token
	oldJsonData, err := r.client.Get(ctx, sessionKey).Result()
	if err != nil {
		return fmt.Errorf("session_repository.Update: failed to get old session: %w", err)
	}

	var oldData sessionData
	if err := json.Unmarshal([]byte(oldJsonData), &oldData); err != nil {
		return fmt.Errorf("session_repository.Update: failed to unmarshal old session: %w", err)
	}

	// Prepare new session data
	data := sessionData{
		ID:           session.ID().String(),
		UserID:       session.UserID().String(),
		RefreshToken: session.RefreshToken(),
		ExpiresAt:    session.ExpiresAt(),
		CreatedAt:    session.CreatedAt(),
		UpdatedAt:    session.UpdatedAt(),
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("session_repository.Update: failed to marshal session: %w", err)
	}

	// Calculate TTL
	ttl := time.Until(session.ExpiresAt())
	if ttl <= 0 {
		return fmt.Errorf("session_repository.Update: session already expired")
	}

	// Update session data
	if err := r.client.Set(ctx, sessionKey, jsonData, ttl).Err(); err != nil {
		return fmt.Errorf("session_repository.Update: failed to update session: %w", err)
	}

	// Remove old refresh token key if it changed
	if oldData.RefreshToken != session.RefreshToken() {
		oldTokenKey := fmt.Sprintf("session:token:%s", oldData.RefreshToken)
		_ = r.client.Del(ctx, oldTokenKey)

		// Add new refresh token key
		newTokenKey := fmt.Sprintf("session:token:%s", session.RefreshToken())
		if err := r.client.Set(ctx, newTokenKey, session.ID().String(), ttl).Err(); err != nil {
			return fmt.Errorf("session_repository.Update: failed to update token key: %w", err)
		}
	}

	return nil
}

// Delete removes a session from Redis
func (r *SessionRepository) Delete(ctx context.Context, id account.SessionID) error {
	sessionKey := fmt.Sprintf("session:id:%s", id.String())
	
	// Get session data to clean up related keys
	jsonData, err := r.client.Get(ctx, sessionKey).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return nil // Already deleted
		}
		return fmt.Errorf("session_repository.Delete: %w", err)
	}

	var data sessionData
	if err := json.Unmarshal([]byte(jsonData), &data); err != nil {
		// If we can't unmarshal, still try to delete the session key
		_ = r.client.Del(ctx, sessionKey)
		return nil
	}

	// Delete all related keys
	tokenKey := fmt.Sprintf("session:token:%s", data.RefreshToken)
	userKey := fmt.Sprintf("user:sessions:%s", data.UserID)

	pipe := r.client.Pipeline()
	pipe.Del(ctx, sessionKey)
	pipe.Del(ctx, tokenKey)
	pipe.SRem(ctx, userKey, id.String())
	
	_, err = pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("session_repository.Delete: %w", err)
	}

	return nil
}

// DeleteByUserID removes all sessions for a specific user
func (r *SessionRepository) DeleteByUserID(ctx context.Context, userID account.UserID) error {
	userKey := fmt.Sprintf("user:sessions:%s", userID.String())
	
	// Get all session IDs for the user
	sessionIDs, err := r.client.SMembers(ctx, userKey).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return nil // No sessions
		}
		return fmt.Errorf("session_repository.DeleteByUserID: %w", err)
	}

	// Delete each session
	for _, sessionIDStr := range sessionIDs {
		sessionID, err := account.SessionIDFromString(sessionIDStr)
		if err != nil {
			continue // Skip invalid session IDs
		}
		_ = r.Delete(ctx, sessionID)
	}

	// Delete the user sessions set
	_ = r.client.Del(ctx, userKey)

	return nil
}

// DeleteExpired removes all expired sessions
func (r *SessionRepository) DeleteExpired(ctx context.Context) error {
	// Redis automatically expires keys, so we don't need to do anything special
	// However, we should clean up orphaned user session sets
	
	// This is a simplified implementation
	// In production, you might want to use a more sophisticated cleanup mechanism
	return nil
}

// GetByUserID retrieves all sessions for a specific user
func (r *SessionRepository) GetByUserID(ctx context.Context, userID account.UserID) ([]*account.Session, error) {
	userKey := fmt.Sprintf("user:sessions:%s", userID.String())
	
	sessionIDs, err := r.client.SMembers(ctx, userKey).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return []*account.Session{}, nil
		}
		return nil, fmt.Errorf("session_repository.GetByUserID: %w", err)
	}

	sessions := make([]*account.Session, 0, len(sessionIDs))
	for _, sessionIDStr := range sessionIDs {
		sessionID, err := account.SessionIDFromString(sessionIDStr)
		if err != nil {
			continue // Skip invalid session IDs
		}

		session, err := r.GetByID(ctx, sessionID)
		if err != nil {
			if errors.Is(err, account.ErrSessionNotFound) {
				// Clean up orphaned session ID from user set
				_ = r.client.SRem(ctx, userKey, sessionIDStr)
				continue
			}
			return nil, fmt.Errorf("session_repository.GetByUserID: %w", err)
		}

		sessions = append(sessions, session)
	}

	return sessions, nil
}

// dataToSession converts sessionData to domain Session
func (r *SessionRepository) dataToSession(data sessionData) (*account.Session, error) {
	sessionID, err := account.SessionIDFromString(data.ID)
	if err != nil {
		return nil, fmt.Errorf("invalid session ID: %w", err)
	}

	userID, err := account.UserIDFromString(data.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	return account.NewSessionWithID(
		sessionID,
		userID,
		data.RefreshToken,
		data.ExpiresAt,
		data.CreatedAt,
		data.UpdatedAt,
	), nil
}

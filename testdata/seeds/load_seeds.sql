-- Load all seed data in the correct order
-- This script loads test data for the payment gateway system
-- Run this script after running migrations to populate test data

-- Start transaction to ensure all-or-nothing loading
BEGIN;

-- Load permissions first (no dependencies)
\i testdata/seeds/permissions.sql

-- Load roles second (depends on permissions for role_permissions)
\i testdata/seeds/roles.sql

-- Load users last (depends on roles for user_roles)
\i testdata/seeds/users.sql

-- Commit transaction
COMMIT;

-- Verify data was loaded correctly
SELECT 'Permissions loaded: ' || COUNT(*) FROM permissions WHERE id::text LIKE '11111111-%';
SELECT 'Roles loaded: ' || COUNT(*) FROM roles WHERE id::text LIKE '550e8400-%';
SELECT 'Users loaded: ' || COUNT(*) FROM users WHERE id::text LIKE '770e8400-%';
SELECT 'Role-Permission assignments: ' || COUNT(*) FROM role_permissions WHERE role_id::text LIKE '550e8400-%';
SELECT 'User-Role assignments: ' || COUNT(*) FROM user_roles WHERE user_id::text LIKE '770e8400-%';

# Test Seed Data

This directory contains test seed data for the Payment Gateway application. The seed data is designed to support comprehensive testing of the authentication and authorization system following Clean Architecture principles.

## Overview

The seed data includes:
- **Permissions**: Fine-grained permissions for different resources and actions
- **Roles**: Role definitions with associated permissions
- **Users**: Test users with different role assignments
- **Relationships**: User-role and role-permission assignments

## Files

### permissions.sql
Contains test permissions for various system resources:
- User management (create, read, update, delete, list)
- Role management (create, read, update, delete, list)
- Permission management (read, list)
- Payment management (create, read, update, delete, list)
- Organization management (create, read, update, delete, list)
- System administration (admin, config, audit)
- Test-specific permissions

All test permissions use IDs starting with `********-` for easy identification and cleanup.

### roles.sql
Contains test roles with different permission levels:
- **Super Administrator**: Full system access with all permissions
- **Administrator**: Administrative access (most permissions except delete and system admin)
- **User Manager**: User and role management permissions
- **Payment Manager**: Payment management and basic user read permissions
- **Viewer**: Read-only access to most resources
- **Regular User**: Basic permissions for own data
- **Inactive Role**: Test role that is inactive

All test roles use IDs starting with `550e8400-` for easy identification and cleanup.

### users.sql
Contains test users with different role assignments:
- **Super Admin User** (<EMAIL>): Super Administrator role
- **Admin User** (<EMAIL>): Administrator role
- **User Manager** (<EMAIL>): User Manager + Viewer roles
- **Payment Manager** (<EMAIL>): Payment Manager + Viewer roles
- **Viewer User** (<EMAIL>): Viewer role
- **Regular User** (<EMAIL>): Regular User role
- **Inactive User** (<EMAIL>): Inactive role (for testing inactive users)
- **Test User 1** (<EMAIL>): Regular User role
- **Test User 2** (<EMAIL>): Regular User role

All test users use IDs starting with `770e8400-` for easy identification and cleanup.
All passwords are hashed using Argon2 and represent the plaintext password "password123".

## Loading Seed Data

### Using SQL Script
```bash
# Load all seeds in correct order
psql -d your_database -f testdata/seeds/load_seeds.sql
```

### Using Go Test Helper
```go
// In your test setup
seedSetup := testing.NewTestSeedSetup(db, "testdata/seeds")

// Load all seeds
err := seedSetup.LoadAllSeeds(ctx)

// Load only basic seeds (permissions, roles, users)
err := seedSetup.LoadBasicSeeds(ctx)

// Verify seeds were loaded correctly
err := seedSetup.VerifySeeds(ctx)

// Get test user credentials
users := seedSetup.GetTestUserCredentials()
superAdmin := users["superadmin"]
```

## Test User Credentials

All test users have the password `password123`. Here are the available test accounts:

| Username | Email | Role(s) | Use Case |
|----------|-------|---------|----------|
| superadmin | <EMAIL> | Super Administrator | Testing full system access |
| admin | <EMAIL> | Administrator | Testing admin operations |
| usermanager | <EMAIL> | User Manager, Viewer | Testing user management |
| paymentmanager | <EMAIL> | Payment Manager, Viewer | Testing payment operations |
| viewer | <EMAIL> | Viewer | Testing read-only access |
| user | <EMAIL> | Regular User | Testing basic user operations |

## Cleanup

The seed data is designed for easy cleanup using ID patterns:
- Permissions: `DELETE FROM permissions WHERE id::text LIKE '********-%'`
- Roles: `DELETE FROM roles WHERE id::text LIKE '550e8400-%'`
- Users: `DELETE FROM users WHERE id::text LIKE '770e8400-%'`

The `SeedManager.ClearAllData()` method handles proper cleanup in dependency order.

## Dependencies

The seed files must be loaded in the correct order due to foreign key relationships:
1. **permissions.sql** (no dependencies)
2. **roles.sql** (depends on permissions for role_permissions)
3. **users.sql** (depends on roles for user_roles)

The `load_seeds.sql` script and `SeedManager` handle this ordering automatically.

## Schema Compatibility

This seed data is compatible with the Clean Architecture schema defined in:
- `db/migrations/000001_initial_schema.up.sql`
- `db/migrations/000002_update_account_schema.up.sql`

The seed data follows the many-to-many relationship patterns for users-roles and roles-permissions as defined in the updated schema.

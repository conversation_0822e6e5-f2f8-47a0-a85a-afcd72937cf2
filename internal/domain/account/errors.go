package account

import "errors"

// Domain errors for the account bounded context
var (
	// User errors
	ErrUserNotFound      = errors.New("user not found")
	ErrUserAlreadyExists = errors.New("user already exists")
	ErrUserInactive      = errors.New("user account is inactive")

	// Role errors
	ErrRoleNotFound      = errors.New("role not found")
	ErrRoleAlreadyExists = errors.New("role already exists")

	// Permission errors
	ErrPermissionNotFound      = errors.New("permission not found")
	ErrPermissionAlreadyExists = errors.New("permission already exists")

	// Authentication errors
	ErrInvalidCredentials   = errors.New("invalid credentials")
	ErrInvalidRefreshToken  = errors.New("invalid refresh token")
	ErrSessionNotFound      = errors.New("session not found")
	ErrSessionExpired       = errors.New("session expired")
	ErrAccessDenied         = errors.New("access denied")
	ErrInsufficientPrivileges = errors.New("insufficient privileges")

	// Validation errors
	ErrInvalidEmail    = errors.New("invalid email format")
	ErrInvalidPassword = errors.New("invalid password")
	ErrPasswordTooWeak = errors.New("password is too weak")

	// Business rule errors
	ErrCannotDeleteLastAdmin = errors.New("cannot delete the last admin user")
	ErrCannotRevokeOwnAdmin  = errors.New("cannot revoke your own admin privileges")
)

version: "2"
sql:
  - engine: "postgresql"
    queries: "internal/infrastructure/database/sqlc/queries"
    schema: "db/migrations"
    gen:
      go:
        package: "sqlc"
        out: "internal/infrastructure/database/sqlc"
        sql_package: "pgx/v5"
        emit_json_tags: true
        emit_db_tags: true
        emit_prepared_queries: false
        emit_interface: true
        emit_exact_table_names: false
        emit_empty_slices: true
        overrides:
          # UUID type mapping
          - db_type: "uuid"
            go_type: "github.com/google/uuid.UUID"
          # BIGINT for monetary values
          - db_type: "bigint"
            go_type: "int64"
          # JSONB type mapping
          - db_type: "jsonb"
            go_type: "json.RawMessage"
            go_struct_tag: 'json:"-"'
          # Timestamp with timezone
          - db_type: "timestamptz"
            go_type: "time.Time"
          # Boolean type
          - db_type: "boolean"
            go_type: "bool"
          # Text type
          - db_type: "text"
            go_type: "string"
          # VARCHAR type
          - db_type: "varchar"
            go_type: "string"
          # INTEGER type
          - db_type: "integer"
            go_type: "int32"

package account

import (
	"context"
	"errors"
)

// AuthenticationService provides domain services for authentication
type AuthenticationService struct {
	userRepo       UserRepository
	passwordHasher PasswordHasher
}

// NewAuthenticationService creates a new AuthenticationService
func NewAuthenticationService(userRepo UserRepository, passwordHasher PasswordHasher) *AuthenticationService {
	return &AuthenticationService{
		userRepo:       userRepo,
		passwordHasher: passwordHasher,
	}
}

// AuthenticateUser authenticates a user with email and password
func (s *AuthenticationService) AuthenticateUser(ctx context.Context, email Email, password string) (*User, error) {
	// Get user by email
	user, err := s.userRepo.GetByEmail(ctx, email)
	if err != nil {
		if errors.Is(err, ErrUserNotFound) {
			return nil, ErrInvalidCredentials
		}
		return nil, err
	}

	// Check if user is active
	if !user.IsActive() {
		return nil, ErrUserInactive
	}

	// Verify password
	err = s.passwordHasher.Verify(password, user.PasswordHash().String())
	if err != nil {
		return nil, ErrInvalidCredentials
	}

	return user, nil
}

// ChangePassword changes a user's password
func (s *AuthenticationService) ChangePassword(ctx context.Context, userID UserID, oldPassword, newPassword string) error {
	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return err
	}

	// Verify old password
	err = s.passwordHasher.Verify(oldPassword, user.PasswordHash().String())
	if err != nil {
		return ErrInvalidCredentials
	}

	// Hash new password
	newHash, err := s.passwordHasher.Hash(newPassword)
	if err != nil {
		return err
	}

	// Update user password
	user.UpdatePassword(NewPasswordHash(newHash))

	// Save user
	return s.userRepo.Update(ctx, user)
}

// UserRegistrationService provides domain services for user registration
type UserRegistrationService struct {
	userRepo       UserRepository
	passwordHasher PasswordHasher
	eventPublisher EventPublisher
}

// NewUserRegistrationService creates a new UserRegistrationService
func NewUserRegistrationService(userRepo UserRepository, passwordHasher PasswordHasher, eventPublisher EventPublisher) *UserRegistrationService {
	return &UserRegistrationService{
		userRepo:       userRepo,
		passwordHasher: passwordHasher,
		eventPublisher: eventPublisher,
	}
}

// RegisterUser registers a new user
func (s *UserRegistrationService) RegisterUser(ctx context.Context, email Email, password, name string) (*User, error) {
	// Check if user already exists
	exists, err := s.userRepo.ExistsByEmail(ctx, email)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, ErrUserAlreadyExists
	}

	// Hash password
	hashedPassword, err := s.passwordHasher.Hash(password)
	if err != nil {
		return nil, err
	}

	// Create user
	user := NewUser(email, NewPasswordHash(hashedPassword), name)

	// Save user
	err = s.userRepo.Create(ctx, user)
	if err != nil {
		return nil, err
	}

	// Publish domain event
	event := NewUserRegisteredEvent(user.ID(), user.Email(), user.Name())
	err = s.eventPublisher.Publish(ctx, event)
	if err != nil {
		// Log error but don't fail the registration
		// In a production system, you might want to implement retry logic
	}

	return user, nil
}

// AuthorizationService provides domain services for authorization
type AuthorizationService struct {
	userRepo UserRepository
	roleRepo RoleRepository
}

// NewAuthorizationService creates a new AuthorizationService
func NewAuthorizationService(userRepo UserRepository, roleRepo RoleRepository) *AuthorizationService {
	return &AuthorizationService{
		userRepo: userRepo,
		roleRepo: roleRepo,
	}
}

// HasPermission checks if a user has a specific permission
func (s *AuthorizationService) HasPermission(ctx context.Context, userID UserID, permission Permission) (bool, error) {
	// Get user with roles
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return false, err
	}

	// Check if user is active
	if !user.IsActive() {
		return false, nil
	}

	// Get user roles with permissions
	roles, err := s.userRepo.GetUserRoles(ctx, userID)
	if err != nil {
		return false, err
	}

	// Check each role for the permission
	for _, role := range roles {
		if !role.IsActive() {
			continue
		}

		// Get role permissions
		permissions, err := s.roleRepo.GetRolePermissions(ctx, role.ID())
		if err != nil {
			continue // Skip this role if we can't get permissions
		}

		// Check if role has the permission
		for _, p := range permissions {
			if p.IsActive() && p.ID() == permission.ID() {
				return true, nil
			}
		}
	}

	return false, nil
}

// HasRole checks if a user has a specific role
func (s *AuthorizationService) HasRole(ctx context.Context, userID UserID, roleID RoleID) (bool, error) {
	// Get user roles
	roles, err := s.userRepo.GetUserRoles(ctx, userID)
	if err != nil {
		return false, err
	}

	// Check if user has the role
	for _, role := range roles {
		if role.ID() == roleID && role.IsActive() {
			return true, nil
		}
	}

	return false, nil
}

// AssignRole assigns a role to a user
func (s *AuthorizationService) AssignRole(ctx context.Context, userID UserID, roleID RoleID) error {
	// Check if user exists
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return err
	}

	// Check if role exists
	role, err := s.roleRepo.GetByID(ctx, roleID)
	if err != nil {
		return err
	}

	// Check if user already has the role
	hasRole, err := s.HasRole(ctx, userID, roleID)
	if err != nil {
		return err
	}
	if hasRole {
		return nil // User already has the role
	}

	// Assign role
	err = s.userRepo.AssignRole(ctx, userID, roleID)
	if err != nil {
		return err
	}

	// Publish domain event
	_ = NewRoleAssignedEvent(user.ID(), role.ID(), role.Name())
	// Note: In a real implementation, you'd inject an event publisher
	// For now, we'll skip event publishing in this service

	return nil
}

// RevokeRole revokes a role from a user
func (s *AuthorizationService) RevokeRole(ctx context.Context, userID UserID, roleID RoleID) error {
	// Check if user exists
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return err
	}

	// Check if role exists
	role, err := s.roleRepo.GetByID(ctx, roleID)
	if err != nil {
		return err
	}

	// Revoke role
	err = s.userRepo.RevokeRole(ctx, userID, roleID)
	if err != nil {
		return err
	}

	// Publish domain event
	_ = NewRoleRevokedEvent(user.ID(), role.ID(), role.Name())
	// Note: In a real implementation, you'd inject an event publisher
	// For now, we'll skip event publishing in this service

	return nil
}
